﻿using Microsoft.Office.Core;
using Microsoft.Office.Interop.Word;
using Microsoft.Office.Interop.Excel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;
using static WordAddIn_YJZS.CheckTimeApiService;
using static WordAddIn_YJZS.ImageHelper;
using Office = Microsoft.Office.Core;
using Task = System.Threading.Tasks.Task;
using Word = Microsoft.Office.Interop.Word;
using Excel = Microsoft.Office.Interop.Excel;


// TODO:   按照以下步骤启用功能区(XML)项:

// 1. 将以下代码块复制到 ThisAddin、ThisWorkbook 或 ThisDocument 类中。

//  protected override Microsoft.Office.Core.IRibbonExtensibility CreateRibbonExtensibilityObject()
//  {
//      return new Ribbon_YJZS();
//  }

// 2. 在此类的"功能区回调"区域中创建回调方法，以处理用户
//    操作(如单击某个按钮)。注意: 如果已经从功能区设计器中导出此功能区，
//    则将事件处理程序中的代码移动到回调方法并修改该代码以用于
//    功能区扩展性(RibbonX)编程模型。

// 3. 向功能区 XML 文件中的控制标记分配特性，以标识代码中的相应回调方法。  

// 有关详细信息，请参见 Visual Studio Tools for Office 帮助中的功能区 XML 文档。


namespace WordAddIn_YJZS
{
    [ComVisible(true)]
    public class Ribbon_YJZS : Office.IRibbonExtensibility
    {
        #region 私有变量

        private Office.IRibbonUI ribbon;

        //初始设置未登录状态
        private bool isUserLoggedIn = false;
        private bool areControlsEnabled = false; // 控制其他按钮是否启用
        private string loggedInUserName = ""; 
        private string loginusername = "登录";
        private string loggedInShowUserName = "显示的登录名";

        private string unuploadFileName = "文件名待上传";
        private List<string> uploadedFiles = new List<string>(); // 存储已上传文件名列表
        private string selectedProjectName = null;
        private string selectedProjectCycle = null;  // 添加立项周期字段

        // 根据用户选择的立项周期计算年份
        private string GetCoverYear()
        {
            if (string.IsNullOrEmpty(selectedProjectCycle))
            {
                // 如果没有选择立项周期，使用原来的逻辑
                if (DateTime.Now.Month < 6 || (DateTime.Now.Month == 6 && DateTime.Now.Day < 30))
                {
                    return DateTime.Now.Year.ToString();
                }
                else
                {
                    return (DateTime.Now.Year + 1).ToString();
                }
            }
            
            // 根据用户选择计算年份
            if (selectedProjectCycle == "current")
            {
                return DateTime.Now.Year.ToString();  // 年中
            }
            else if (selectedProjectCycle == "next")
            {
                return (DateTime.Now.Year + 1).ToString();  // 明年
            }
            else
            {
                // 默认使用当前年份
                return DateTime.Now.Year.ToString();
            }
        }

        // 生成带年份的完整项目名称
        private string GetFullProjectNameWithYear(string projectType, string projectName, string projectCycle)
        {
            // 临时保存当前的立项周期，以便计算年份
            string originalCycle = selectedProjectCycle;
            selectedProjectCycle = projectCycle;
            
            // 计算年份
            string year = GetCoverYear();
            
            // 恢复原来的立项周期
            selectedProjectCycle = originalCycle;
            
            // 返回带年份的完整项目名称
            return $"{year}年{projectType}（{projectName}）";
        }

        private const string CustomItemsFileName = "CustomComboBoxItems.xml";
        private List<string> customItems = new List<string>();
        private string currentComboBoxText = string.Empty;

        private FileApiService fileApiService; 
        private readonly WriterApiService writerApiService = new WriterApiService();
        private readonly WriterApiParse writerApiParse = new WriterApiParse();

        private List<string> selectedEnterprises = new List<string>();
        private List<string> selectedMenu = new List<string>();
        private GenerateForm currentGenerateForm;

        private readonly WordDocumentHelper wordHelper; 
        private readonly ImageHelper imageHelper;
        private readonly WordAtCursor wordAtCursor;
        private readonly ImageAtCursor imageAtCursor;

        private CircleProgree circleProgree;
        private bool checkBox1Pressed = true;
        private string editBox1Text = "";

        private static string defaultSavePath;
        private static readonly string SETTINGS_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");

        // 存储当前生成的Excel文件路径，用于Word文档中的表格插入
        private static string currentGeneratedExcelPath = null;

        private bool areGenerateEnabled = false;
        private bool isGenerating = false; // 防止重复点击

        #endregion

        public Ribbon_YJZS()
        {
            wordHelper = new WordDocumentHelper();
            imageHelper = new ImageHelper();
            wordAtCursor = new WordAtCursor();
            imageAtCursor = new ImageAtCursor();

            selectedEnterprises = GetSelectedEnterprises();
            selectedMenu = GetSelectedMenu();

        }

        #region IRibbonExtensibility 成员

        public string GetCustomUI(string ribbonID)
        {
            return GetResourceText("WordAddIn_YJZS.Ribbon_YJZS.xml");
        }

        #endregion

        #region 功能区回调
        //在此处创建回叫方法。有关添加回叫方法的详细信息，请访问 https://go.microsoft.com/fwlink/?LinkID=271226

        public void Ribbon_Load(Office.IRibbonUI ribbonUI)
        {
            this.ribbon = ribbonUI;
            DisableControlsExceptLogin(); // 初始状态禁用

            // 尝试自动登录
            _ = AutoLoginAsync();

            LoadCustomItems();

            // 确保settings.json文件存在
            EnsureSettingsFileExists();
            LoadSettings(); 

            // Ensure the file API service is initialized if the user is already logged in
            if (!string.IsNullOrEmpty(loggedInUserName) && !string.IsNullOrEmpty(selectedProjectName))
            {
                fileApiService = new FileApiService(loggedInUserName, selectedProjectName);
            }
        }

        #endregion

        #region 文件保存路径和模板
        private static string GetInstallDirectory()
        {
            return AppDomain.CurrentDomain.BaseDirectory;
        }

        // 修改 EnsureSettingsFileExists 方法
        private static void EnsureSettingsFileExists()
        {
            try
            {
                if (!File.Exists(SETTINGS_FILE_PATH))
                {
                    // 创建默认设置，使用用户文档文件夹下的实际路径
                    string defaultPath = Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        "Word-Solvy");

                    var settings = new
                    {
                        FilePath = defaultPath,
                        ModelSelection = "auto"
                    };

                    string settingsJson = JsonConvert.SerializeObject(settings, Formatting.Indented);
                    File.WriteAllText(SETTINGS_FILE_PATH, settingsJson);

                    // 更新字段
                    defaultSavePath = defaultPath;

                    // 确保目录存在
                    if (!Directory.Exists(defaultPath))
                    {
                        Directory.CreateDirectory(defaultPath);
                    }

                    // 复制Style文件夹到默认路径
                    CopyStyleFolder(GetInstallDirectory(), defaultPath);

                    Console.WriteLine($"已创建默认设置文件，默认保存路径: {defaultPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建设置文件时出错: {ex.Message}");
            }
        }

        // 添加复制Style文件夹的方法
        private static void CopyStyleFolder(string sourceBasePath, string targetBasePath)
        {
            try
            {
                string sourceStylePath = Path.Combine(sourceBasePath, "Style");
                string targetStylePath = Path.Combine(targetBasePath, "Style");

                if (Directory.Exists(sourceStylePath))
                {
                    // 确保目标目录存在
                    if (!Directory.Exists(targetStylePath))
                    {
                        Directory.CreateDirectory(targetStylePath);

                        // 设置Style文件夹为隐藏
                        try
                        {
                            DirectoryInfo dirInfo = new DirectoryInfo(targetStylePath);
                            dirInfo.Attributes |= FileAttributes.Hidden;
                            System.Diagnostics.Debug.WriteLine($"Style文件夹已设置为隐藏: {targetStylePath}");
                        }
                        catch (Exception hideEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置Style文件夹隐藏属性时出错: {hideEx.Message}");
                        }
                    }

                    // 复制所有文件
                    foreach (string file in Directory.GetFiles(sourceStylePath))
                    {
                        string fileName = Path.GetFileName(file);
                        string targetFile = Path.Combine(targetStylePath, fileName);
                        File.Copy(file, targetFile, true);
                    }

                    // 递归复制所有子目录
                    foreach (string dir in Directory.GetDirectories(sourceStylePath))
                    {
                        string dirName = Path.GetFileName(dir);
                        string targetDir = Path.Combine(targetStylePath, dirName);

                        if (!Directory.Exists(targetDir))
                        {
                            Directory.CreateDirectory(targetDir);
                        }

                        // 复制子目录中的文件
                        foreach (string file in Directory.GetFiles(dir))
                        {
                            string fileName = Path.GetFileName(file);
                            string targetFile = Path.Combine(targetDir, fileName);
                            File.Copy(file, targetFile, true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制Style文件夹时出错: {ex.Message}");
            }
        }

        // 添加确保Style文件夹存在的方法
        private static void EnsureStyleFolderExists(string targetBasePath)
        {
            try
            {
                string targetStylePath = Path.Combine(targetBasePath, "Style");

                // 如果Style文件夹不存在，则从安装目录复制
                if (!Directory.Exists(targetStylePath))
                {
                    Console.WriteLine($"Style文件夹不存在，正在复制到: {targetStylePath}");
                    CopyStyleFolder(GetInstallDirectory(), targetBasePath);
                }
                else
                {
                    // 检查Style文件夹是否为空或缺少关键文件
                    if (Directory.GetFiles(targetStylePath).Length == 0 && Directory.GetDirectories(targetStylePath).Length == 0)
                    {
                        Console.WriteLine($"Style文件夹为空，正在重新复制到: {targetStylePath}");
                        CopyStyleFolder(GetInstallDirectory(), targetBasePath);
                    }
                    else
                    {
                        // 确保现有的Style文件夹也是隐藏的
                        try
                        {
                            DirectoryInfo dirInfo = new DirectoryInfo(targetStylePath);
                            if ((dirInfo.Attributes & FileAttributes.Hidden) == 0)
                            {
                                dirInfo.Attributes |= FileAttributes.Hidden;
                                System.Diagnostics.Debug.WriteLine($"现有Style文件夹已设置为隐藏: {targetStylePath}");
                            }
                        }
                        catch (Exception hideEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置现有Style文件夹隐藏属性时出错: {hideEx.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确保Style文件夹存在时出错: {ex.Message}");
            }
        }

        // 添加确保Images文件夹隐藏的方法
        private static void EnsureImagesFolderHidden(string targetBasePath)
        {
            try
            {
                string imagesPath = Path.Combine(targetBasePath, "Images");

                // 如果Images文件夹存在，确保它是隐藏的
                if (Directory.Exists(imagesPath))
                {
                    try
                    {
                        DirectoryInfo dirInfo = new DirectoryInfo(imagesPath);
                        if ((dirInfo.Attributes & FileAttributes.Hidden) == 0)
                        {
                            dirInfo.Attributes |= FileAttributes.Hidden;
                            System.Diagnostics.Debug.WriteLine($"现有Images文件夹已设置为隐藏: {imagesPath}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"Images文件夹已经是隐藏的: {imagesPath}");
                        }
                    }
                    catch (Exception hideEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置Images文件夹隐藏属性时出错: {hideEx.Message}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Images文件夹不存在，无需设置隐藏: {imagesPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保Images文件夹隐藏时出错: {ex.Message}");
            }
        }

        // 创建项目专用文件夹的方法（重载版本，自动生成时间戳）
        public static string CreateProjectFolder(string basePath, string projectName)
        {
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return CreateProjectFolder(basePath, projectName, timestamp);
        }

        // 创建项目专用文件夹的方法（指定时间戳版本）
        public static string CreateProjectFolder(string basePath, string projectName, string timestamp)
        {
            try
            {
                // 清理项目名称中的非法字符
                string cleanProjectName = CleanFileName(projectName);

                // 生成文件夹名称：{项目名称}_{时间戳}
                string folderName = $"{cleanProjectName}_{timestamp}";

                // 构建完整路径
                string projectFolderPath = Path.Combine(basePath, folderName);

                // 创建文件夹
                if (!Directory.Exists(projectFolderPath))
                {
                    Directory.CreateDirectory(projectFolderPath);
                    System.Diagnostics.Debug.WriteLine($"创建项目文件夹: {projectFolderPath}");
                }

                return projectFolderPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建项目文件夹时出错: {ex.Message}");
                // 如果创建失败，返回基础路径
                return basePath;
            }
        }

        // 清理文件名中的非法字符
        private static string CleanFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "未命名项目";

            // 定义非法字符
            char[] invalidChars = Path.GetInvalidFileNameChars();

            // 替换非法字符为下划线
            string cleanName = fileName;
            foreach (char c in invalidChars)
            {
                cleanName = cleanName.Replace(c, '_');
            }

            // 移除多余的空格和特殊字符
            cleanName = cleanName.Trim().Replace("  ", " ");

            return cleanName;
        }

        // 修改 GetDefaultSavePath 方法，优先使用用户设置的路径，默认为用户文档文件夹
        public static string GetDefaultSavePath()
        {
            try
            {
                // 首先尝试从设置文件读取用户自定义的路径
                if (File.Exists(SETTINGS_FILE_PATH))
                {
                    string settingsJson = File.ReadAllText(SETTINGS_FILE_PATH);
                    var settings = JsonConvert.DeserializeObject<dynamic>(settingsJson);
                    string userSetPath = settings.FilePath?.ToString();

                    if (!string.IsNullOrEmpty(userSetPath) && userSetPath.Trim() != "")
                    {
                        // 检查是否是旧的环境变量格式，如果是则转换为实际路径
                        if (userSetPath.Contains("%APPDATA%"))
                        {
                            string actualPath = userSetPath.Replace("%APPDATA%",
                                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData));

                            // 更新设置文件为实际路径
                            UpdateSavePath(actualPath);
                            userSetPath = actualPath;
                            System.Diagnostics.Debug.WriteLine($"转换环境变量路径为实际路径: {actualPath}");
                        }

                        // 确保用户设置的路径存在
                        if (!Directory.Exists(userSetPath))
                        {
                            Directory.CreateDirectory(userSetPath);
                            System.Diagnostics.Debug.WriteLine($"创建用户设置的目录: {userSetPath}");
                        }

                        // 确保Style文件夹存在
                        EnsureStyleFolderExists(userSetPath);

                        // 确保Images文件夹隐藏（如果存在的话）
                        EnsureImagesFolderHidden(userSetPath);

                        // 更新缓存
                        defaultSavePath = userSetPath;

                        System.Diagnostics.Debug.WriteLine($"使用用户设置的保存路径: {userSetPath}");
                        return userSetPath;
                    }
                }

                // 如果没有设置文件或没有设置路径，使用用户文档文件夹作为默认值
                string userDocumentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                string defaultPath = Path.Combine(userDocumentsPath, "Word-Solvy");

                // 确保目录存在
                if (!Directory.Exists(defaultPath))
                {
                    Directory.CreateDirectory(defaultPath);
                    System.Diagnostics.Debug.WriteLine($"创建默认用户文档目录: {defaultPath}");
                }

                // 确保Style文件夹存在
                EnsureStyleFolderExists(defaultPath);

                // 确保Images文件夹隐藏（如果存在的话）
                EnsureImagesFolderHidden(defaultPath);

                // 更新缓存
                defaultSavePath = defaultPath;

                // 创建或更新设置文件，保存默认路径
                EnsureSettingsFileExists();

                System.Diagnostics.Debug.WriteLine($"使用默认保存路径: {defaultPath}");
                return defaultPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取保存路径时出错: {ex.Message}");

                // 如果出错，尝试使用备用路径
                try
                {
                    string fallbackPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Word-Solvy");
                    if (!Directory.Exists(fallbackPath))
                    {
                        Directory.CreateDirectory(fallbackPath);
                    }
                    EnsureStyleFolderExists(fallbackPath);
                    defaultSavePath = fallbackPath;
                    System.Diagnostics.Debug.WriteLine($"使用备用路径: {fallbackPath}");
                    return fallbackPath;
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"创建备用路径时也出错: {fallbackEx.Message}");
                    return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                }
            }
        }

        private static void LoadSettings()
        {
            try
            {
                if (File.Exists(SETTINGS_FILE_PATH))
                {
                    string settingsJson = File.ReadAllText(SETTINGS_FILE_PATH);
                    var settings = JsonConvert.DeserializeObject<dynamic>(settingsJson);

                    // 获取文件路径
                    string newPath = settings.FilePath?.ToString();

                    // 如果路径发生变化，清除缓存并确保新路径的Style文件夹存在
                    if (newPath != defaultSavePath)
                    {
                        Console.WriteLine($"设置加载时检测到路径变更: {defaultSavePath} -> {newPath}");
                        defaultSavePath = newPath;

                        // 确保新路径存在并包含Style文件夹
                        if (!string.IsNullOrEmpty(defaultSavePath))
                        {
                            if (!Directory.Exists(defaultSavePath))
                            {
                                Directory.CreateDirectory(defaultSavePath);
                            }
                            EnsureStyleFolderExists(defaultSavePath);
                        }
                    }

                    Console.WriteLine($"已加载设置，默认保存路径: {defaultSavePath}");
                }
                else
                {
                    // 创建默认设置
                    EnsureSettingsFileExists();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载设置时出错: {ex.Message}");
            }
        }

        #endregion

        #region Group1 账户 回调函数
        // 自动登录方法
        private async Task AutoLoginAsync()
        {
            try
            {
                var loginApiService = new LoginApiService();
                var (isLoggedIn, username, fullName) = await loginApiService.AutoLoginAsync();

                if (isLoggedIn)
                {
                    // 自动登录成功
                    isUserLoggedIn = true;
                    loggedInUserName = username;
                    loggedInShowUserName = fullName;
                    loginusername = loggedInShowUserName + "\n";//返回给登录按钮的label
                    EnableControlsAfterLogin();

                    // 设置共享的登录状态，只使用用户名和项目名
                    LoginStateManager.Instance.SetLoginState(
                        loggedInUserName,
                        selectedProjectName
                    );
                }

                // 刷新 splitButton1 的标签
                ribbon.InvalidateControl("splitButton1__btn");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动登录时出错: {ex.Message}");
                // 自动登录失败，不做任何处理，用户需要手动登录
            }
        }

        // 禁用所有控件，除了 splitButton1__btn
        private void DisableControlsExceptLogin()
        {
            areControlsEnabled = false;
            ribbon.Invalidate(); // 刷新所有控件
        }

        // 登录后启用所有控件
        private void EnableControlsAfterLogin()
        {
            areControlsEnabled = true;
            ribbon.Invalidate(); // 刷新所有控件
        }

        // 回调函数：动态获取 splitButton1 的标签
        public string GetSplitButton1Label(Office.IRibbonControl control)
        {
            return loginusername;
        }

        // 回调函数：动态控制 splitButton1 是否启用
        public bool GetSplitButton1Enabled(Office.IRibbonControl control)
        {
            return true; // splitButton1 始终启用
        }

        // 回调函数：动态控制其他控件是否启用
        public bool GetControlEnabled(Office.IRibbonControl control)
        {
            return areControlsEnabled;
        }

        public bool GetGenerateEnabled(Office.IRibbonControl control)
        {
            // 当areGenerateEnabled为true且没有正在生成时，按钮才可用
            return areGenerateEnabled && !isGenerating;
        }

        private void RefreshGenerate()
        {
            areGenerateEnabled = true;
            ribbon.Invalidate(); // 刷新所有控件
        }

        // 登录后启用所有控件
        public void SplitButton1_Click(Office.IRibbonControl control)
        {            
            if (!isUserLoggedIn)
            {
                // 处理登录逻辑
                LoginForm loginForm = new LoginForm();
                loginForm.ShowDialog();

                if (loginForm.DialogResult == DialogResult.OK)
                {
                    if(!string.IsNullOrEmpty(loginForm.LoggedInUserName) && !string.IsNullOrEmpty(loginForm.LoggedShowName))
                    {
                        isUserLoggedIn = true;
                        loggedInUserName = loginForm.LoggedInUserName;
                        //MessageBox.Show($"登录的用户名是{loggedInUserName}");
                        loggedInShowUserName = loginForm.LoggedShowName;
                        //MessageBox.Show($"登录的用户全名是{loggedInShowUserName}");

                        loginusername = loggedInShowUserName + "\n";//返回给登录按钮的label

                        //MessageBox.Show($"登录的token是{loginForm.LoggedAccessToken}");
                        //MessageBox.Show($"登录的expire是{loginForm.LoggedExpireSecond}");

                        // 保存令牌
                        if (!string.IsNullOrEmpty(loginForm.LoggedAccessToken))
                        {
                            // 直接使用整数类型的过期时间
                            TokenManager.SaveToken(loggedInUserName, loggedInShowUserName, loginForm.LoggedAccessToken);

                            // 设置HTTP客户端的默认请求头
                            HttpClientManager.Client.DefaultRequestHeaders.Authorization =
                                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", loginForm.LoggedAccessToken);
                        }

                        EnableControlsAfterLogin();

                        // 设置共享的登录状态，只使用用户名和项目名
                        LoginStateManager.Instance.SetLoginState(
                            loggedInUserName,
                            selectedProjectName
                        );
                    }
                    if (!string.IsNullOrEmpty(loginForm.LoggedErrorMessage))
                    {
                        MessageBox.Show(loginForm.LoggedErrorMessage, "提示");
                    }
                }
            }
            // 刷新 splitButton1 的标签
            ribbon.InvalidateControl("splitButton1__btn");
        }

        //个人中心
        public void Button1_Click(Office.IRibbonControl control)
        {
            AdminForm adminForm = new AdminForm();
            adminForm.ShowDialog();
        }

        //退出登录
        public void Button2_Click(Office.IRibbonControl control)
        {
            isUserLoggedIn = false;
            loggedInUserName = "";
            loggedInShowUserName = "";
            loginusername = "登录";

            // 清除令牌
            TokenManager.ClearToken();

            // 清除HTTP客户端的授权头
            if (HttpClientManager.Client.DefaultRequestHeaders.Contains("Authorization"))
            {
                HttpClientManager.Client.DefaultRequestHeaders.Remove("Authorization");
            }

            DisableControlsExceptLogin();

            // 清除 LoginStateManager 状态
            LoginStateManager.Instance.Clear();

            // 刷新头像
            ribbon.InvalidateControl("splitButton1__btn");

            MessageBox.Show("已退出登录！");
        }


        #endregion

        #region Group2 人机协同 回调函数

        #region 智能生成逻辑
        //智能生成
        public void Button3_Click(Office.IRibbonControl control)
        {
            var task = Button3_Click_Async(control);
            task.ContinueWith(t =>
            {
                System.Diagnostics.Debug.WriteLine($"[UNHANDLED TASK EXCEPTION in Button3_Click]: {t.Exception}");
            }, TaskContinuationOptions.OnlyOnFaulted);
        }

        private async Task Button3_Click_Async(Office.IRibbonControl control)
        {
            if (isGenerating)
            {
                MessageBox.Show("正在生成文档，请稍候...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            MessageBox.Show("请输入正确的章节标题，并将光标置于标题后");

            isGenerating = true;
            ribbon.Invalidate(); // 使按钮失效

            CircleProgree circleProgree = new CircleProgree();
            try
            {
                circleProgree.Show();

                // 获取当前文档
                Word.Document currentDoc = Globals.ThisAddIn.Application.ActiveDocument;

                // 检查文档是否为空（只需检查是否有内容）
                bool isDocumentEmpty = currentDoc == null || currentDoc.Content.Text.Trim().Length == 0;

                if (isDocumentEmpty)
                {
                    // 完整生成模式 - 生成完整文档
                    await GenerateFullDocumentAsync();
                }
                else
                {
                    // 智能生成模式 - 在光标位置插入内容
                    await GenerateContentAtCursorAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CRITICAL ERROR in Button3_Click]: {ex.ToString()}");
                MessageBox.Show($"生成文档时发生严重错误: {ex.Message}", "严重错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.DefaultDesktopOnly);
            }
            finally
            {
                circleProgree.Close();
                isGenerating = false;
                ribbon.Invalidate(); // 重新使能按钮
            }
        }

        private async Task GenerateContentAtCursorAsync()
        {
            try
            {
                // 基础检查
                if (string.IsNullOrEmpty(selectedProjectName))
                {
                    MessageBox.Show("请确保您已选择了项目名称。");
                    return;
                }

                // 获取当前文档
                Word.Document doc = Globals.ThisAddIn.Application.ActiveDocument;
                if (doc == null)
                {
                    MessageBox.Show("无法获取当前文档。");
                    return;
                }

                // 检查是否选择了企业
                List<string> selectedEnterprises = GetSelectedEnterprises();
                if (selectedEnterprises.Count == 0)
                {
                    MessageBox.Show("请先选择投资建设单位。");
                    return;
                }

                // 创建所有需要的服务实例
                CoverApiService coverService = new CoverApiService();
                OverviewApiService overviewService = new OverviewApiService();
                DemandApiService demandService = new DemandApiService();
                SchemeApiService schemeService = new SchemeApiService();
                InvestmentApiService investmentService = new InvestmentApiService();
                BenefitApiService benefitService = new BenefitApiService();
                RiskApiService riskService = new RiskApiService();
                ConclusionApiService conclusionService = new ConclusionApiService();
                ParametersApiService parametersService = new ParametersApiService();

                // 调用InsertContent方法在光标位置插入内容
                await InsertContentAtCursor(doc, coverService, overviewService, demandService, schemeService,
                                  investmentService, benefitService, riskService, conclusionService,
                                  parametersService);

                // 更新文档中的所有域
                doc.Fields.Update();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"在光标位置插入内容时发生错误: {ex.Message}\n\n详细信息: {ex.StackTrace}");
            }
        }

        private async Task InsertContentAtCursor(Word.Document doc, CoverApiService coverService,
                OverviewApiService overviewService, DemandApiService demandService, SchemeApiService schemeApiService,InvestmentApiService investmentService, BenefitApiService benefitService,RiskApiService riskService, ConclusionApiService conclusionService,ParametersApiService parametersService)
        {
            try
            {
                // 基础参数设置
                string construction_unit = string.Join("、", selectedEnterprises);
                string construction_cycle = GlobalParameters.CalculateData.Select12;
                string project_category = GlobalParameters.CalculateData.Select3;
                string project_type = GlobalParameters.CalculateData.Select1;
                string construction_nature = GlobalParameters.CalculateData.Select7;
                string system_security_level = GlobalParameters.CalculateData.Select9;
                double system_user_number = GlobalParameters.CalculateData.Input10;
                string system_deployment_mode = GlobalParameters.CalculateData.Select11;

                await parametersService.ParaConfigAsync(
                    loggedInUserName,
                    selectedProjectName,
                    construction_unit,
                    construction_cycle,
                    project_category,
                    project_type,
                    construction_nature,
                    system_security_level,
                    system_user_number,
                    system_deployment_mode
                );

                // 获取当前光标位置
                Word.Selection currentSelection = Globals.ThisAddIn.Application.Selection;

                // 在插入内容前先插入一个换行
                currentSelection.TypeParagraph();

                // 获取光标所在段落的前一段落文本
                string previousParagraphText = "";
                object missing = System.Reflection.Missing.Value;
                if (currentSelection.Paragraphs.First != null)
                {
                    Word.Paragraph prevPara = currentSelection.Paragraphs.First.Previous(ref missing) as Word.Paragraph;
                    if (prevPara != null)
                    {
                        previousParagraphText = prevPara.Range.Text.Trim();
                    }
                }

                // 根据前一段落的文本决定插入什么内容
                switch (previousParagraphText)
                {
                    case "项目背景":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => overviewService.BackgroundAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "项目依据":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => overviewService.BasisAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "项目目标":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => overviewService.GoalsAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "项目业务范围":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => overviewService.BusinessAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "应用范围":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            async () => new List<ContentItem> {
                                new ContentItem {
                                    content = await overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName,selectedEnterprises),
                                    style = "0"
                                }
                            },
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "投资（建设）单位":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            async () => new List<ContentItem> {
                                new ContentItem {
                                    content = await overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName,selectedEnterprises),
                                    style = "0"
                                }
                            },
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "开发范围":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => overviewService.DevelopScopeAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "现状分析":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.StatusAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;             

                    case "业务需求":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.BusinessDemandAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "功能需求":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.FunctionDemandAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "性能需求":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.PerformanceDemandAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "业务集成需求":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.BusinessIntegrationDemandAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "安全需求":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.SecurityDemandAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "用户规模":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.UserScaleAsync(loggedInUserName, selectedProjectName, system_user_number),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "必要性结论":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => demandService.NecessityAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "业务能力":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.BusinessCapabilityAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "业务流程协作能力":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.BusinessCollaborationAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "应用模块":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.ApplyModuleAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "应用功能":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.ApplyFunctionAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "应用交互":
                        // 插入分节符并设置为横向
                        wordAtCursor.InsertSectionBreak(doc);
                        doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientLandscape;

                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.ApplyInteractionAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "业务与应用对应情况":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.ApplyToBusinessAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);

                        // 恢复为纵向
                        wordAtCursor.InsertSectionBreak(doc);
                        doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientPortrait;
                        break;

                    case "数据域":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.DataDomainAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "逻辑实体":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.LogicEntityAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "逻辑实体分布":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.LogicEntityDistriAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "技术分类":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.TechClassifyAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "部署方式":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.DeploymentAsync(loggedInUserName, selectedProjectName, system_deployment_mode),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "安全技术方案":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.SecuritySolutionAsync(loggedInUserName, selectedProjectName, system_security_level),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "实施策略":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.StrategyAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "实施计划":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.ImplementPlanAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "实施任务分解":
                        // 设置为横向
                        wordAtCursor.InsertSectionBreak(doc);
                        doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientLandscape;

                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => schemeApiService.TaskDecomposeAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "投资依据说明":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => investmentService.InvestmentBasisAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);

                        // 插入表格
                        string tablePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Util", "Table", "4.1投资依据说明.docx");
                        if (File.Exists(tablePath))
                        {
                            await InsertTableFromTemplate(doc, tablePath);
                        }
                        break;

                    case "总投资":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => investmentService.TotalInvestmentAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);

                        string totalInvestmentTablePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Util", "Table", "4.2总投资.docx");
                        if (File.Exists(totalInvestmentTablePath))
                        {
                            await InsertTableFromTemplate(doc, totalInvestmentTablePath);
                        }
                        break;

                    case "资金计划建议":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => investmentService.FundPlanAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);

                        string fundPlanTablePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Util", "Table", "4.3资金计划建议.docx");
                        if (File.Exists(fundPlanTablePath))
                        {
                            await InsertTableFromTemplate(doc, fundPlanTablePath);
                        }

                        // 恢复为纵向
                        wordAtCursor.InsertSectionBreak(doc);
                        doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientPortrait;
                        break;

                    case "管理效益分析":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => benefitService.ManagementBenefitAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "经济效益分析":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => benefitService.EconimicBenefitAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "社会效益分析":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => benefitService.SocialBenefitAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "项目风险分析":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => riskService.RiskAnalyseAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    case "项目可研结论":
                        await wordAtCursor.InsertSectionAtCursor(doc,
                            () => conclusionService.ConclusionAsync(loggedInUserName, selectedProjectName),
                            WordAtCursor.ContentType.MainText2);
                        break;

                    default:
                        MessageBox.Show("请将光标放在正确的标题后面");
                        break;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"插入内容时发生错误：{ex.Message}", ex);
            }
        }

        // 辅助方法：插入表格模板
        private Task InsertTableFromTemplate(Word.Document doc, string templatePath)
        {
            if (!File.Exists(templatePath))
            {
                return Task.CompletedTask;
            }

            Word.Document tableDoc = null;
            try
            {
                tableDoc = Globals.ThisAddIn.Application.Documents.Open(templatePath);
                tableDoc.Content.Copy();

                Word.Range insertPoint = doc.Paragraphs.Last.Range;
                insertPoint.Collapse(Word.WdCollapseDirection.wdCollapseEnd);
                insertPoint.Paste();
            }
            finally
            {
                if (tableDoc != null)
                {
                    tableDoc.Close(WdSaveOptions.wdDoNotSaveChanges);
                    Marshal.ReleaseComObject(tableDoc);
                }
            }

            return Task.CompletedTask;
        }

        #endregion

        #region 直接生成逻辑

        public async Task GenerateFullDocumentAsync()
        {
            Word.Document doc = null;
            int maxRetries = 3;
            int currentRetry = 0;
            bool documentOpened = false;

            // 创建进度条和遮罩
            if (circleProgree == null || circleProgree.IsDisposed)
            {
                circleProgree = new CircleProgree();
            }

            // 获取当前活动窗口句柄
            IntPtr currentWordHandle = (IntPtr)Globals.ThisAddIn.Application.ActiveWindow.Hwnd;
            circleProgree.Show(currentWordHandle);

            // 设置初始进度
            await circleProgree.UpdateProgress(0, "准备生成文档...\n\r正在初始化");

            // 生成统一的时间戳，用于Word和Excel文件夹命名
            string sharedTimestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            // 清空之前的Excel文件路径
            currentGeneratedExcelPath = null;

            CheckTimeApiService checkTimeApiService = new CheckTimeApiService();
            TimeResponse response = null;
            string timeEstimate = "预计时间长";

            try
            {
                response = await checkTimeApiService.EvalTimeAsync(loggedInUserName, selectedProjectName);
                if (response != null && !string.IsNullOrEmpty(response.Data))
                {
                    timeEstimate = response.Data;
                }
                else
                {
                    timeEstimate = "预计时间未知";
                    System.Diagnostics.Debug.WriteLine("CheckTimeApiService返回的数据为空或无效");
                }              
            }
            catch (Exception ex)
            {
                timeEstimate = "预计时间未知";
                System.Diagnostics.Debug.WriteLine($"CheckTimeApiService调用失败: {ex.Message}");
                // 不抛出异常，继续执行文档生成
            }

            await circleProgree.UpdateProgress(3, $"{timeEstimate}");

            while (currentRetry < maxRetries)
            {
                try
                {
                    if (string.IsNullOrEmpty(selectedProjectName))
                    {
                        CircleProgree.ShowMessageBox("请确保您已选择了项目名称。", "请确保您已选择了项目名称");
                        return;
                    }

                    // 检查是否选择了企业单位
                    List<string> selectedEnterprises = GetSelectedEnterprises();
                    if (selectedEnterprises == null || selectedEnterprises.Count == 0)
                    {
                        CircleProgree.ShowMessageBox("请先选择投资建设单位。", "未选择投资建设单位");
                        return;
                    }

                    // 检查是否选择了生成目录
                    List<string> selectedMenu = GetSelectedMenu();
                    if (selectedMenu == null || selectedMenu.Count == 0)
                    {
                        CircleProgree.ShowMessageBox("请先选择要生成的章节。", "未选择生成章节");
                        return;
                    }

                    // 确保 CalculateData 已初始化
                    if (GlobalParameters.CalculateData == null)
                    {
                        GlobalParameters.CalculateData = new CalculateFormData();
                    }

                    // 获取当前 Word 应用程序
                    Word.Application wordApp = Globals.ThisAddIn.Application;

                    // 获取保存路径设置
                    string savePath = GetDefaultSavePath();

                    // 基于保存路径构建模板路径
                    string templatePath = Path.Combine(savePath, "Style", "template1117.dotx");

                    if (!File.Exists(templatePath))
                    {
                        CircleProgree.ShowMessageBox($"未找到模板文件，请确保模板文件存在于以下位置：\n{templatePath}", "未找到模板文件");
                        return;
                    }

                    await circleProgree.UpdateProgress(5, $"{timeEstimate}\n\r创建文档");

                    // 创建基于模板的新文档
                    object template = templatePath;
                    object missing = System.Reflection.Missing.Value;
                    doc = wordApp.Documents.Add(ref template, ref missing, ref missing, ref missing);

                    if (doc == null)
                    {
                        CircleProgree.ShowMessageBox("创建新文档失败。", "创建新文档失败");
                        return;
                    }

                    // 获取新文档的窗口句柄并更新进度条
                    IntPtr newDocHandle = (IntPtr)doc.ActiveWindow.Hwnd;

                    // 等待新文档完全加载
                    await Task.Delay(300);

                    // 更新进度条跟踪的窗口句柄
                    circleProgree.Close();
                    circleProgree = new CircleProgree();
                    circleProgree.Show(newDocHandle);
                    await circleProgree.UpdateProgress(10, $"{timeEstimate}\n\r准备数据中");


                    // 创建服务实例
                    CoverApiService coverService = new CoverApiService();
                    OverviewApiService overviewService = new OverviewApiService();
                    DemandApiService demandService = new DemandApiService();
                    SchemeApiService schemeService = new SchemeApiService();
                    InvestmentApiService investmentService = new InvestmentApiService();
                    BenefitApiService benefitService = new BenefitApiService();
                    RiskApiService riskService = new RiskApiService();
                    ConclusionApiService conclusionService = new ConclusionApiService();

                    await circleProgree.UpdateProgress(15, $"{timeEstimate}\n\r准备文档内容");

                    // 插入内容并应用样式
                    await InsertContent(doc, coverService, overviewService, demandService, schemeService,
                                      investmentService, benefitService, riskService, conclusionService, timeEstimate);

                    // 添加 Excel 生成逻辑
                    if (checkBox1Pressed)
                    {
                        // 检查是否请求了取消
                        if (circleProgree.IsCancellationRequested())
                        {
                            await circleProgree.UpdateProgress(65, "正在取消生成Excel...\n\r请稍等");
                            return; // 如果请求取消，直接返回
                        }

                        await circleProgree.UpdateProgress(67, $"{timeEstimate}");

                        try
                        {
                            // 启用Excel覆盖功能
                            circleProgree.EnableExcelCoverage();

                            // 设置进度表单到DocumentSyncService
                            DocumentSyncService.Instance.SetProgressForm(circleProgree);

                            // 直接使用项目名称和用户信息生成Excel（传入共享时间戳）
                            string excelPath = await DocumentSyncService.Instance.GenerateExcelFromWordAsync(
                                selectedProjectName,  // 使用项目名称
                                true,
                                true,
                                sharedTimestamp  // 传入共享时间戳
                            );

                            if (!string.IsNullOrEmpty(excelPath))
                            {
                                // 保存Excel文件路径供后续使用
                                currentGeneratedExcelPath = excelPath;

                                await circleProgree.UpdateProgress(70, $"{timeEstimate}\n\rExcel生成成功");
                                System.Diagnostics.Process.Start(excelPath);

                                // 等待Excel打开
                                await Task.Delay(1000);
                            }
                        }
                        catch (Exception excelEx)
                        {
                            CircleProgree.ShowMessageBox($"生成Excel文件时出错：{excelEx.Message}", "Excel生成错误");
                        }
                    }

                    // 插入内容并应用样式
                    await InsertRestContent(doc, coverService, overviewService, demandService, schemeService,
                                      investmentService, benefitService, riskService, conclusionService, timeEstimate);

                    await circleProgree.UpdateProgress(92, $"{timeEstimate}\n\r更新文档域");

                    // 更新文档中的所有域
                    doc.Fields.Update();

                    await circleProgree.UpdateProgress(94, $"{timeEstimate}\n\r保存文档中");

                    // 创建项目专用文件夹（使用共享时间戳）
                    string projectFolderPath = CreateProjectFolder(savePath, selectedProjectName, sharedTimestamp);

                    // 构建完整的文件路径（去除时间戳）
                    string fileName = $"{selectedProjectName}.docx";
                    string fullPath = Path.Combine(projectFolderPath, fileName);

                    // 保存文档
                    try
                    {
                        object saveAsPath = fullPath;
                        doc.SaveAs2(ref saveAsPath);

                        await circleProgree.UpdateProgress(96, $"{timeEstimate}\n\r文档保存成功");
                        await circleProgree.UpdateProgress(98, $"{timeEstimate}\n\r打开文档");

                        try
                        {
                            // 关闭当前文档
                            object saveChanges = WdSaveOptions.wdDoNotSaveChanges;
                            object originalFormat = Missing.Value;
                            object routeDocument = Missing.Value;
                            doc.Close(ref saveChanges, ref originalFormat, ref routeDocument);
                            Marshal.ReleaseComObject(doc);
                            doc = null;

                            // 先保存进度条引用，确保后续能正确关闭
                            var currentProgress = circleProgree;
                            circleProgree = null;

                            // 打开保存的文档
                            object readOnly = false;
                            object isVisible = true;
                            Word.Document openedDoc = wordApp.Documents.Open(
                                fullPath,
                                ReadOnly: readOnly,
                                Visible: isVisible
                            );

                            documentOpened = true;

                            // 使用定时器确保进度条关闭
                            System.Windows.Forms.Timer closeTimer = new System.Windows.Forms.Timer();
                            closeTimer.Interval = 500;
                            closeTimer.Tick += (sender, e) =>
                            {
                                closeTimer.Stop();
                                
                                // 在UI线程上关闭进度条
                                if (currentProgress != null && !currentProgress.IsDisposed)
                                {
                                    try
                                    {
                                        if (currentProgress.InvokeRequired)
                                        {
                                            currentProgress.Invoke(new System.Action(() =>
                                            {
                                                currentProgress.Close();
                                                currentProgress.Dispose();
                                            }));
                                        }
                                        else
                                        {
                                            currentProgress.Close();
                                            currentProgress.Dispose();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"关闭进度条时出错: {ex.Message}");
                                    }
                                }

                                closeTimer.Dispose();
                            };

                            closeTimer.Start();

                            CircleProgree.ShowMessageBox($"可研生成完毕！文件已保存至:{fullPath}", "生成完毕");
                        }
                        catch (Exception openEx)
                        {
                            if (circleProgree != null && !circleProgree.IsDisposed)
                            {
                                circleProgree.Close();
                                circleProgree.Dispose();
                                circleProgree = null;
                            }
                            CircleProgree.ShowMessageBox($"打开文档时发生错误: {openEx.Message}", "打开文档时发生错误");
                            throw;
                        }

                        break; // 成功则跳出重试循环
                    }
                    catch (Exception saveEx)
                    {
                        CircleProgree.ShowMessageBox($"保存文档时发生错误: {saveEx.Message}", "保存文档时发生错误");
                        throw;
                    }
                }
                catch (HttpRequestException ex)
                {
                    currentRetry++;
                    if (currentRetry >= maxRetries)
                    {
                        CircleProgree.ShowMessageBox($"网络请求失败，已重试{maxRetries}次：\n{ex.Message}", "网络请求失败");
                    }
                    else
                    {
                        await circleProgree.UpdateProgress(0, $"网络请求失败，正在重试({currentRetry}/{maxRetries})...");
                        await Task.Delay(1000 * currentRetry);
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    CircleProgree.ShowMessageBox($"生成文档时发生错误: {ex.Message}\n\n详细信息: {ex.StackTrace}", "生成文档时发生错误");
                    break;
                }
                finally
                {
                    // 只在没有成功打开新文档时才清理
                    if (!documentOpened && doc != null)
                    {
                        try
                        {
                            object saveChanges = WdSaveOptions.wdDoNotSaveChanges;
                            object originalFormat = Missing.Value;
                            object routeDocument = Missing.Value;

                            doc.Close(ref saveChanges, ref originalFormat, ref routeDocument);
                            Marshal.ReleaseComObject(doc);
                            doc = null;
                        }
                        catch (Exception closeEx)
                        {
                            Console.WriteLine($"关闭文档时发生错误: {closeEx.Message}");
                        }
                    }

                    // 确保进度条关闭
                    if (circleProgree != null && !circleProgree.IsDisposed)
                    {
                        circleProgree.Close();
                        circleProgree.Dispose();
                        circleProgree = null;
                    }

                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
        }

        private static readonly Dictionary<string, int> menuOrder = new Dictionary<string, int>
        {
            {"1.1项目背景", 1},
            {"1.2项目依据", 2},
            {"1.3项目目标", 3},
            {"1.4项目范围", 4},
            {"2.1现状分析", 5},
            {"2.2需求分析", 6},
            {"2.3必要性结论", 7},
            {"3.1业务架构", 8},
            {"3.2应用架构", 9},
            {"3.3数据架构", 10},
            {"3.4技术架构", 11},
            {"3.5系统部署方式及软硬件资源需求", 12},
            {"3.6安全技术方案", 13},
            {"3.7项目实施需求", 14},
            {"4.1投资依据说明", 15},
            {"4.2总投资", 16},
            {"4.3资金计划建议", 17},
            {"5.1管理效益分析", 18},
            {"5.2经济效益分析", 19},
            {"5.3社会效益分析", 20},
            {"6.1项目风险分析", 21},
            {"7.1项目可研结论", 22}
        };

        private async Task InsertContent(Word.Document doc, CoverApiService coverService,
            OverviewApiService overviewService, DemandApiService demandService, SchemeApiService schemeApiService, InvestmentApiService investmentService, BenefitApiService benefitService, RiskApiService riskService, ConclusionApiService conclusionService, string timeEstimate)
        {
            try
            {
                // 检查是否选择了企业单位
                List<string> selectedEnterprises = GetSelectedEnterprises();
                if (selectedEnterprises == null || selectedEnterprises.Count == 0)
                {
                    CircleProgree.ShowMessageBox("请先选择投资建设单位。", "未选择投资建设单位");
                    return;
                }

                // 检查是否选择了生成目录
                List<string> selectedMenu = GetSelectedMenu();
                if (selectedMenu == null || selectedMenu.Count == 0)
                {
                    CircleProgree.ShowMessageBox("请先选择要生成的章节。", "未选择生成章节");
                    return;
                }

                // 将企业列表转换为字符串
                string construction_unit = string.Join("、", selectedEnterprises);

                // 从 GlobalParameters.CalculateData 安全获取值
                string construction_cycle = GlobalParameters.CalculateData?.Select12 ?? throw new InvalidOperationException("建设周期未设置");
                string project_category = GlobalParameters.CalculateData?.Select3 ?? throw new InvalidOperationException("项目分类未设置");

                string project_type = GlobalParameters.CalculateData?.Select1 ?? "";//项目类型未设置
                string construction_nature = GlobalParameters.CalculateData?.Select7 ?? "";//建设性质未设置
                string system_security_level = GlobalParameters.CalculateData?.Select9 ?? "";//系统等保级别未设置
                double system_user_number = GlobalParameters.CalculateData?.Input10 ?? 0;//系统用户数量未设置
                string system_deployment_mode = GlobalParameters.CalculateData?.Select11 ?? "";//系统部署方式未设置             

                // 对 selectedMenu 进行排序
                var orderedMenu = selectedMenu
                    .OrderBy(item => {
                        int order;
                        return menuOrder.TryGetValue(item.Trim(), out order) ? order : int.MaxValue;
                    })
                    .ToList();

                // 筛选InsertContent需要处理的内容（到3.2为止）
                var menuItemsToProcess = orderedMenu
                    .Where(item => {
                        int order;
                        return menuOrder.TryGetValue(item.Trim(), out order) && order <= 9; // 9是"3.2应用架构"的序号
                    })
                    .ToList();

                // 计算总任务数（只计算当前要处理的菜单项）
                int totalTasks = menuItemsToProcess.Count;
                int completedTasks = 0;

                // 设置页眉页脚
                wordHelper.SetHeaderAndFooter(doc);

                // 插入项目名称
                await wordHelper.InsertSection(doc, "\n",
                    () => coverService.ProcessCoverProjectNameAsync(loggedInUserName, selectedProjectName),
                    WordDocumentHelper.ContentType.CoverFisrtTitle,
                    WordDocumentHelper.ContentType.CoverFisrtTitle);

                // 年份已经包含在selectedProjectName中，无需重复计算

                // 插入项目名称
                await wordHelper.InsertSection(doc, "",
                    () => Task.FromResult(selectedProjectName+"项目"),
                    WordDocumentHelper.ContentType.CoverFisrtTitle,
                    WordDocumentHelper.ContentType.CoverFisrtTitle);

                // 插入项目名称的"可行性研究报告"
                await wordHelper.InsertSection(doc, "",
                    () => Task.FromResult( "可行性研究报告"),
                    WordDocumentHelper.ContentType.CoverFisrtTitle,
                    WordDocumentHelper.ContentType.CoverFisrtTitle);

                // 插入公司名称（使用空行和居中对齐）
                await wordHelper.InsertSection(doc,
                    new string('\n', 10) + "中国能源建设集团广东省电力设计研究院有限公司",
                    () => Task.FromResult(string.Empty),
                    WordDocumentHelper.ContentType.AuthorInfo,
                    WordDocumentHelper.ContentType.AuthorInfo);             

                // 插入年月
                await wordHelper.InsertSection(doc,
                    DateTime.Now.ToString("yyyy年MM月"),
                    () => Task.FromResult(string.Empty),
                    WordDocumentHelper.ContentType.AuthorInfo,
                    WordDocumentHelper.ContentType.AuthorInfo);

                // 插入分节符
                wordHelper.InsertSectionBreak(doc);

                // 插入作者信息
                await wordHelper.InsertSection(doc,
                    new string('\n', 8), // 前置空行
                    async () =>
                    {
                        var staffsResult = await coverService.ProcessAuthorAsync(loggedInUserName);
                        var content = string.Join("\n\n", staffsResult.Select(kv => $"{kv.Key}: {kv.Value}"));
                        return content;  // 不在这里添加后置空行
                    },
                    WordDocumentHelper.ContentType.AuthorInfo,
                    WordDocumentHelper.ContentType.AuthorInfo);

                // 添加后置空行
                for (int i = 0; i < 6; i++)
                {
                    Word.Paragraph emptyPara = doc.Content.Paragraphs.Add();
                    emptyPara.Range.Text = "";
                    emptyPara.Range.InsertParagraphAfter();
                }

                // 插入分节符
                wordHelper.InsertSectionBreak(doc);

                // 先插入目录
                Word.Range tocRange = doc.Range(doc.Content.End - 1, doc.Content.End - 1);

                // 插入"目录"标题
                Word.Paragraph tocTitle = doc.Content.Paragraphs.Add();
                tocTitle.Range.Text = "目    录";
                tocTitle.Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                tocTitle.Range.Font.Size = 16;
                tocTitle.Range.Font.Bold = 1;
                tocTitle.Range.Font.Name = "宋体";
                tocTitle.Range.InsertParagraphAfter();

                // 直接插入目录，移除了额外的空行段落
                doc.TablesOfContents.Add(doc.Range(doc.Content.End - 1, doc.Content.End - 1),
                    UseHeadingStyles: true,
                    UpperHeadingLevel: 1,
                    LowerHeadingLevel: 3,
                    UseHyperlinks: true,
                    HidePageNumbersInWeb: false,
                    UseOutlineLevels: true);

                // 插入分页符
                Word.Range pageBreak = doc.Range(doc.Content.End - 1, doc.Content.End - 1);
                pageBreak.InsertBreak(Word.WdBreakType.wdPageBreak);

                // 定义进度范围
                const double startProgress = 25.0;
                const double endProgress = 60.0;
                double progressRange = endProgress - startProgress;

                // 记录当前处理的菜单项
                string currentMenuItem = "";

                // 创建一个字典来存储预先获取的API结果
                Dictionary<string, object> apiResults = new Dictionary<string, object>();

                // 创建任务列表，用于并行获取API数据
                List<Task> tasks = new List<Task>();

                // 更新进度条
                await circleProgree.UpdateProgress(18, $"{timeEstimate}\n\r预获取API数据");

                // 预先并行获取所有API数据
                if (orderedMenu.Contains("1.1项目背景"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var result = await overviewService.BackgroundAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["1.1项目背景"] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.1项目背景数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("1.2项目依据"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var result = await overviewService.BasisAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["1.2项目依据"] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.2项目依据数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("1.3项目目标"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var result = await overviewService.GoalsAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["1.3项目目标"] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.3项目目标数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("1.4项目范围"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var business = await overviewService.BusinessAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["1.4.1项目业务范围"] = business;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.4.1项目业务范围数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var applyScope = await overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName, selectedEnterprises);
                            lock (apiResults)
                            {
                                apiResults["1.4.2应用范围"] = applyScope;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.4.2应用范围数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var investmentInc = await overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName, selectedEnterprises);
                            lock (apiResults)
                            {
                                apiResults["1.4.3投资（建设）单位"] = investmentInc;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.4.3投资（建设）单位数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var developScope = await overviewService.DevelopScopeAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["1.4.4开发范围"] = developScope;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取1.4.4开发范围数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("2.1现状分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var result = await demandService.StatusAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.1现状分析"] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.1现状分析数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("2.2需求分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var businessDemand = await demandService.BusinessDemandAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.2.1业务需求"] = businessDemand;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.1业务需求数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var functionDemand = await demandService.FunctionDemandAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.2.2功能需求"] = functionDemand;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.2功能需求数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var performanceDemand = await demandService.PerformanceDemandAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.2.3性能需求"] = performanceDemand;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.3性能需求数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var businessIntegrationDemand = await demandService.BusinessIntegrationDemandAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.2.4业务集成需求"] = businessIntegrationDemand;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.4业务集成需求数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var securityDemand = await demandService.SecurityDemandAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.2.5安全需求"] = securityDemand;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.5安全需求数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var userScale = await demandService.UserScaleAsync(loggedInUserName, selectedProjectName, system_user_number);
                            lock (apiResults)
                            {
                                apiResults["2.2.6用户规模"] = userScale;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.2.6用户规模数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("2.3必要性结论"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var result = await demandService.NecessityAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["2.3必要性结论"] = result;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取2.3必要性结论数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.1业务架构"))
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var businessCapability = await schemeApiService.BusinessCapabilityAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.1.1业务能力"] = businessCapability;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.1.1业务能力数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            var businessCollaboration = await schemeApiService.BusinessCollaborationAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.1.2业务流程协作能力"] = businessCollaboration;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.1.2业务流程协作能力数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.2应用架构"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var applyModule = await schemeApiService.ApplyModuleAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.2.1应用模块"] = applyModule;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.2.1应用模块数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var applyFunction = await schemeApiService.ApplyFunctionAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.2.2应用功能"] = applyFunction;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.2.2应用功能数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var applyInteraction = await schemeApiService.ApplyInteractionAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.2.3应用交互"] = applyInteraction;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.2.3应用交互数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var applyToBusiness = await schemeApiService.ApplyToBusinessAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.2.4业务与应用对应情况"] = applyToBusiness;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.2.4业务与应用对应情况数据失败: {ex.Message}");
                        }
                    }));
                }

                await circleProgree.UpdateProgress(22, $"{timeEstimate}\n\r获取API数据");
                await Task.WhenAll(tasks);
                await circleProgree.UpdateProgress(25, "API数据获取完成\n\r开始生成文档");

                // 按顺序处理菜单项并插入内容（仅处理到3.2）
                foreach (string menuItem in menuItemsToProcess)
                {
                    try
                    {
                        // 记录当前正在处理的菜单项
                        currentMenuItem = menuItem;

                        // 优化：在每个菜单项开始时立即检查取消状态
                        if (circleProgree.IsCancellationRequested())
                        {
                            await circleProgree.UpdateProgress(0, "正在取消生成...");
                            return; // 如果请求取消，直接返回
                        }

                        // 更新状态 - 计算当前进度百分比（在25%到60%之间）
                        double currentProgress = startProgress + (progressRange * completedTasks / totalTasks);
                        await circleProgree.UpdateProgress(
                            currentProgress,
                            $"正在生成: {menuItem}"
                        );

                        switch (menuItem.Trim())
                        {
                            case "1.1项目背景":
                                //1
                                await wordHelper.InsertSection(doc, "概述",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title1);

                                //1.1 
                                if (apiResults.ContainsKey("1.1项目背景"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目背景",
                                        () => Task.FromResult((List<ContentItem>)apiResults["1.1项目背景"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目背景",
                                        () => overviewService.BackgroundAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "1.2项目依据":
                                //1.2 
                                if (apiResults.ContainsKey("1.2项目依据"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目依据",
                                        () => Task.FromResult((List<ContentItem>)apiResults["1.2项目依据"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目依据",
                                        () => overviewService.BasisAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "1.3项目目标":
                                //1.3 
                                if (apiResults.ContainsKey("1.3项目目标"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目目标",
                                        () => Task.FromResult((List<ContentItem>)apiResults["1.3项目目标"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目目标",
                                        () => overviewService.GoalsAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "1.4项目范围":
                                //1.4
                                await wordHelper.InsertSection(doc, "项目范围",
                                    () => Task.FromResult(string.Empty),
                                    WordDocumentHelper.ContentType.Title2);
                                //1.4.1 
                                if (apiResults.ContainsKey("1.4.1项目业务范围"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目业务范围",
                                        () => Task.FromResult((List<ContentItem>)apiResults["1.4.1项目业务范围"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "项目业务范围",
                                        () => overviewService.BusinessAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //1.4.2 
                                if (apiResults.ContainsKey("1.4.2应用范围"))
                                {
                                    await wordHelper.InsertSection(doc, "应用范围",
                                        () => Task.FromResult((string)apiResults["1.4.2应用范围"]),
                                        WordDocumentHelper.ContentType.Title3,
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                else
                                {
                                    await wordHelper.InsertSection(doc, "应用范围",
                                        () => overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName, selectedEnterprises),
                                        WordDocumentHelper.ContentType.Title3,
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                //1.4.3
                                if (apiResults.ContainsKey("1.4.3投资（建设）单位"))
                                {
                                    await wordHelper.InsertSection(doc, "投资（建设）单位",
                                        () => Task.FromResult((string)apiResults["1.4.3投资（建设）单位"]),
                                        WordDocumentHelper.ContentType.Title3,
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                else
                                {
                                    await wordHelper.InsertSection(doc, "投资（建设）单位",
                                        () => overviewService.InvestmentIncAsync(loggedInUserName, selectedProjectName, selectedEnterprises),
                                        WordDocumentHelper.ContentType.Title3,
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                //1.4.4 
                                if (apiResults.ContainsKey("1.4.4开发范围"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "开发范围",
                                        () => Task.FromResult((List<ContentItem>)apiResults["1.4.4开发范围"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "开发范围",
                                        () => overviewService.DevelopScopeAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "2.1现状分析":
                                //2
                                await wordHelper.InsertSection(doc, "项目现状及必要性分析",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title1);
                                //2.1 
                                if (apiResults.ContainsKey("2.1现状分析"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "现状分析",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.1现状分析"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "现状分析",
                                        () => demandService.StatusAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "2.2需求分析":
                                //2.2
                                await wordHelper.InsertSection(doc, "需求分析",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                //2.2.1 
                                if (apiResults.ContainsKey("2.2.1业务需求"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务需求",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.1业务需求"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务需求",
                                        () => demandService.BusinessDemandAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //2.2.2 
                                if (apiResults.ContainsKey("2.2.2功能需求"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "功能需求",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.2功能需求"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "功能需求",
                                        () => demandService.FunctionDemandAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //2.2.3 
                                if (apiResults.ContainsKey("2.2.3性能需求"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "性能需求",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.3性能需求"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "性能需求",
                                        () => demandService.PerformanceDemandAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //2.2.4 
                                if (apiResults.ContainsKey("2.2.4业务集成需求"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务集成需求",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.4业务集成需求"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务集成需求",
                                        () => demandService.BusinessIntegrationDemandAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //2.2.5 
                                if (apiResults.ContainsKey("2.2.5安全需求"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "安全需求",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.5安全需求"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "安全需求",
                                        () => demandService.SecurityDemandAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                //2.2.6 
                                if (apiResults.ContainsKey("2.2.6用户规模"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "用户规模",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.2.6用户规模"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "用户规模",
                                        () => demandService.UserScaleAsync(loggedInUserName, selectedProjectName, system_user_number),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "2.3必要性结论":
                                //2.3 
                                if (apiResults.ContainsKey("2.3必要性结论"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "必要性结论",
                                        () => Task.FromResult((List<ContentItem>)apiResults["2.3必要性结论"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "必要性结论",
                                        () => demandService.NecessityAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "3.1业务架构":
                                //3
                                await wordHelper.InsertSection(doc, "项目方案",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title1);
                                //3.1 
                                await wordHelper.InsertSection(doc, "业务架构",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                //3.1.1
                                if (apiResults.ContainsKey("3.1.1业务能力"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务能力",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.1.1业务能力"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务能力",
                                        () => schemeApiService.BusinessCapabilityAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.1.2
                                if (apiResults.ContainsKey("3.1.2业务流程协作能力"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务流程协作能力",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.1.2业务流程协作能力"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务流程协作能力",
                                        () => schemeApiService.BusinessCollaborationAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "3.2应用架构":
                                //3.2 
                                await wordHelper.InsertSection(doc, "应用架构",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                //3.2.1
                                if (apiResults.ContainsKey("3.2.1应用模块"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用模块",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.2.1应用模块"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用模块",
                                        () => schemeApiService.ApplyModuleAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.2.2
                                if (apiResults.ContainsKey("3.2.2应用功能"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用功能",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.2.2应用功能"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用功能",
                                        () => schemeApiService.ApplyFunctionAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.2.3
                                if (apiResults.ContainsKey("3.2.3应用交互"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用交互",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.2.3应用交互"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "应用交互",
                                        () => schemeApiService.ApplyInteractionAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.2.4
                                if (apiResults.ContainsKey("3.2.4业务与应用对应情况"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务与应用对应情况",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.2.4业务与应用对应情况"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "业务与应用对应情况",
                                        () => schemeApiService.ApplyToBusinessAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;


                            default:
                                // 可以选择记录未实现的目录项
                                Console.WriteLine($"未实现的目录项: {menuItem}");
                                break;
                        }

                        // 在所有内容生成完成后，更新目录
                        if (doc.TablesOfContents.Count > 0)
                        {
                            doc.TablesOfContents[1].Update();
                        }

                        // 更新进度
                        completedTasks++;

                        // 释放UI线程，防止死锁
                        await Task.Delay(1);
                    }
                    catch (Exception ex)
                    {
                        // 记录具体哪个菜单项出错
                        string errorMessage = $"生成 '{currentMenuItem}' 时出错: {ex.Message}";

                        // 记录详细错误信息
                        System.Diagnostics.Debug.WriteLine(errorMessage);
                        System.Diagnostics.Debug.WriteLine(ex.StackTrace);

                        // 显示错误消息
                        CircleProgree.ShowMessageBox(errorMessage, "生成错误",
                            MessageBoxButtons.OK);

                        // 等待一会儿，让用户看到错误信息
                        await Task.Delay(500);

                        // 继续处理下一个菜单项，而不是中断整个过程
                        completedTasks++;
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                await circleProgree.UpdateProgress(0, "生成失败");
                CircleProgree.ShowMessageBox($"生成文档时发生错误：{ex.Message}\n\n{ex.StackTrace}", "网络请求失败");
                //throw new Exception($"插入内容时发生错误：{ex.Message}", ex);
            }
        }

        private async Task InsertRestContent(Word.Document doc, CoverApiService coverService,
            OverviewApiService overviewService, DemandApiService demandService, SchemeApiService schemeApiService, InvestmentApiService investmentService, BenefitApiService benefitService, RiskApiService riskService, ConclusionApiService conclusionService, string timeEstimate)
        {
            try
            {
                // 检查是否选择了企业单位
                List<string> selectedEnterprises = GetSelectedEnterprises();
                if (selectedEnterprises == null || selectedEnterprises.Count == 0)
                {
                    CircleProgree.ShowMessageBox("请先选择投资建设单位。", "未选择投资建设单位");
                    return;
                }

                // 检查是否选择了生成目录
                List<string> selectedMenu = GetSelectedMenu();
                if (selectedMenu == null || selectedMenu.Count == 0)
                {
                    CircleProgree.ShowMessageBox("请先选择要生成的章节。", "未选择生成章节");
                    return;
                }

                // 将企业列表转换为字符串
                string construction_unit = string.Join("、", selectedEnterprises);

                // 从 GlobalParameters.CalculateData 安全获取值
                string construction_cycle = GlobalParameters.CalculateData?.Select12 ?? throw new InvalidOperationException("建设周期未设置");
                string project_category = GlobalParameters.CalculateData?.Select3 ?? throw new InvalidOperationException("项目分类未设置");

                string project_type = GlobalParameters.CalculateData?.Select1 ?? "";//项目类型未设置
                string construction_nature = GlobalParameters.CalculateData?.Select7 ?? "";//建设性质未设置
                string system_security_level = GlobalParameters.CalculateData?.Select9 ?? "";//系统等保级别未设置
                double system_user_number = GlobalParameters.CalculateData?.Input10 ?? 0;//系统用户数量未设置
                string system_deployment_mode = GlobalParameters.CalculateData?.Select11 ?? "";//系统部署方式未设置

                // 对 selectedMenu 进行排序
                var orderedMenu = selectedMenu
                    .OrderBy(item => {
                        int order;
                        return menuOrder.TryGetValue(item.Trim(), out order) ? order : int.MaxValue;
                    })
                    .ToList();

                // 筛选InsertRestContent需要处理的内容（3.3及之后的内容）
                var menuItemsToProcess = orderedMenu
                    .Where(item => {
                        int order;
                        return menuOrder.TryGetValue(item.Trim(), out order) && order >= 10; // 10是"3.3数据架构"的序号
                    })
                    .ToList();

                // 计算总任务数（只计算当前要处理的菜单项）
                int totalTasks = menuItemsToProcess.Count;
                int completedTasks = 0;

                // 定义进度范围
                const double startProgress = 70.0;
                const double endProgress = 90.0;
                double progressRange = endProgress - startProgress;

                // 记录当前处理的菜单项
                string currentMenuItem = "";

                // 创建一个字典来存储预先获取的API结果
                Dictionary<string, object> apiResults = new Dictionary<string, object>();

                // 创建任务列表，用于并行获取API数据
                List<Task> tasks = new List<Task>();

                if (orderedMenu.Contains("3.3数据架构"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var dataDomain = await schemeApiService.DataDomainAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.3.1数据域"] = dataDomain;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.3.1数据域数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var logicEntity = await schemeApiService.LogicEntityAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.3.2逻辑实体"] = logicEntity;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.3.2逻辑实体数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var logicEntityDistri = await schemeApiService.LogicEntityDistriAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.3.3逻辑实体分布"] = logicEntityDistri;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.3.3逻辑实体分布数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.4技术架构"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var techClassify = await schemeApiService.TechClassifyAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.4技术分类"] = techClassify;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.4技术分类数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.5系统部署方式及软硬件资源需求"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var deployment = await schemeApiService.DeploymentAsync(loggedInUserName, selectedProjectName, system_deployment_mode);
                            lock (apiResults)
                            {
                                apiResults["3.5部署方式"] = deployment;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.5部署方式数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.6安全技术方案"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var securitySolution = await schemeApiService.SecuritySolutionAsync(loggedInUserName, selectedProjectName, system_security_level);
                            lock (apiResults)
                            {
                                apiResults["3.6安全技术方案"] = securitySolution;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.6安全技术方案数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("3.7项目实施需求"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var strategy = await schemeApiService.StrategyAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.7.1实施策略"] = strategy;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.7.1实施策略数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var implementPlan = await schemeApiService.ImplementPlanAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.7.2实施计划"] = implementPlan;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.7.2实施计划数据失败: {ex.Message}");
                        }
                    }));

                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var taskDecompose = await schemeApiService.TaskDecomposeAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["3.7.3实施任务分解"] = taskDecompose;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取3.7.3实施任务分解数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("5.1管理效益分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var managementBenefit = await benefitService.ManagementBenefitAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["5.1管理效益分析"] = managementBenefit;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取5.1管理效益分析数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("5.2经济效益分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var economicBenefit = await benefitService.EconimicBenefitAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["5.2经济效益分析"] = economicBenefit;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取5.2经济效益分析数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("5.3社会效益分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var socialBenefit = await benefitService.SocialBenefitAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["5.3社会效益分析"] = socialBenefit;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取5.3社会效益分析数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("6.1项目风险分析"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var riskAnalyse = await riskService.RiskAnalyseAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["6.1项目风险分析"] = riskAnalyse;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取6.1项目风险分析数据失败: {ex.Message}");
                        }
                    }));
                }

                if (orderedMenu.Contains("7.1项目可研结论"))
                {
                    tasks.Add(Task.Run(async () => {
                        try
                        {
                            var conclusion = await conclusionService.ConclusionAsync(loggedInUserName, selectedProjectName);
                            lock (apiResults)
                            {
                                apiResults["7.1项目可研结论"] = conclusion;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取7.1项目可研结论数据失败: {ex.Message}");
                        }
                    }));
                }

                await Task.WhenAll(tasks);

                // 按顺序处理菜单项并插入内容（从3.3开始）
                foreach (string menuItem in menuItemsToProcess)
                {
                    try
                    {
                        // 记录当前正在处理的菜单项
                        currentMenuItem = menuItem;

                        // 优化：在每个菜单项开始时立即检查取消状态
                        if (circleProgree.IsCancellationRequested())
                        {
                            await circleProgree.UpdateProgress(0, "正在取消生成...");
                            return; // 如果请求取消，直接返回
                        }

                        // 更新状态 - 计算当前进度百分比（在70%到90%之间）
                        double currentProgress = startProgress + (progressRange * completedTasks / totalTasks);
                        await circleProgree.UpdateProgress(
                            currentProgress,
                            $"{timeEstimate}，正在生成: {menuItem}"
                        );

                        switch (menuItem.Trim())
                        {
                            case "3.3数据架构":
                                // 优化：在开始处理每个case时检查取消状态
                                if (circleProgree.IsCancellationRequested())
                                {
                                    await circleProgree.UpdateProgress(0, "正在取消生成...");
                                    return;
                                }

                                //3.3
                                await wordHelper.InsertSection(doc, "数据架构",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                //3.3.1
                                if (apiResults.ContainsKey("3.3.1数据域"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "数据域",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.3.1数据域"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "数据域",
                                        () => schemeApiService.DataDomainAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.3.2
                                if (apiResults.ContainsKey("3.3.2逻辑实体"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "逻辑实体",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.3.2逻辑实体"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "逻辑实体",
                                        () => schemeApiService.LogicEntityAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                //3.3.3
                                if (apiResults.ContainsKey("3.3.3逻辑实体分布"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "逻辑实体分布",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.3.3逻辑实体分布"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "逻辑实体分布",
                                        () => schemeApiService.LogicEntityDistriAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "3.4技术架构":
                                //3.4
                                await wordHelper.InsertSection(doc, "技术架构",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                if (apiResults.ContainsKey("3.4技术分类"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "技术分类",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.4技术分类"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "技术分类",
                                        () => schemeApiService.TechClassifyAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "3.5系统部署方式及软硬件资源需求":
                                //3.5
                                await wordHelper.InsertSection(doc, "系统部署方式及软硬件资源需求",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                if (apiResults.ContainsKey("3.5部署方式"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "部署方式",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.5部署方式"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "部署方式",
                                        () => schemeApiService.DeploymentAsync(loggedInUserName, selectedProjectName, system_deployment_mode),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "3.6安全技术方案":
                                //3.6
                                await wordHelper.InsertSection(doc, "安全技术方案",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                if (apiResults.ContainsKey("3.6安全技术方案"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.6安全技术方案"]),
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "",
                                        () => schemeApiService.SecuritySolutionAsync(loggedInUserName, selectedProjectName, system_security_level),
                                        WordDocumentHelper.ContentType.MainText2);
                                }
                                break;

                            case "3.7项目实施需求":
                                //3.7
                                await wordHelper.InsertSection(doc, "项目实施需求",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title2);

                                if (apiResults.ContainsKey("3.7.1实施策略"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施策略",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.7.1实施策略"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施策略",
                                        () => schemeApiService.StrategyAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                if (apiResults.ContainsKey("3.7.2实施计划"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施计划",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.7.2实施计划"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施计划",
                                        () => schemeApiService.ImplementPlanAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }

                                // 插入分节符并设置下一节为横向
                                wordHelper.InsertSectionBreak(doc);
                                doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientLandscape;

                                if (apiResults.ContainsKey("3.7.3实施任务分解"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施任务分解",
                                        () => Task.FromResult((List<ContentItem>)apiResults["3.7.3实施任务分解"]),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "实施任务分解",
                                        () => schemeApiService.TaskDecomposeAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title3);
                                }
                                break;

                            case "4.1投资依据说明":
                                try
                                {
                                    // 设置为横向纸张
                                    wordHelper.InsertSectionBreak(doc);
                                    doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientLandscape;

                                    // 确保光标位置正确
                                    Word.Range newSectionRange = doc.Sections[doc.Sections.Count].Range;
                                    newSectionRange.Collapse(Word.WdCollapseDirection.wdCollapseStart);
                                    newSectionRange.Select();

                                    // 插入内容
                                    await wordHelper.InsertSection(doc, "项目投资估算",
                                        () => Task.FromResult(string.Empty),
                                        WordDocumentHelper.ContentType.Title1);

                                    await wordHelper.InsertSectionWithStyles(doc, "投资依据说明",
                                        () => investmentService.InvestmentBasisAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);

                                    //// 插入表格
                                    //string tablePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Util", "Table", "4.1投资依据说明.docx");
                                    //if (File.Exists(tablePath))
                                    //{
                                    //    Word.Document tableDoc = null;
                                    //    try
                                    //    {
                                    //        tableDoc = Globals.ThisAddIn.Application.Documents.Open(tablePath);
                                    //        tableDoc.Content.Copy();
                                    //        Word.Range insertPoint = doc.Paragraphs.Last.Range;
                                    //        insertPoint.Collapse(Word.WdCollapseDirection.wdCollapseEnd);
                                    //        insertPoint.Paste();
                                    //    }
                                    //    finally
                                    //    {
                                    //        if (tableDoc != null)
                                    //        {
                                    //            tableDoc.Close(WdSaveOptions.wdDoNotSaveChanges);
                                    //            Marshal.ReleaseComObject(tableDoc);
                                    //        }
                                    //    }
                                    //}
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"插入投资依据说明时发生错误：{ex.Message}\n\n{ex.StackTrace}");
                                    throw;
                                }
                                break;

                            case "4.2总投资":
                                try
                                {
                                    await wordHelper.InsertSection(doc, "总投资",
                                        () => Task.FromResult(string.Empty),
                                        WordDocumentHelper.ContentType.Title2);

                                    // 从生成的Excel文件中复制表一数据（仅粘贴值）
                                    await InsertExcelTableToWord(doc, "表一（项目估算汇总表）", "4.2总投资");

                                    await wordHelper.InsertSectionWithStyles(doc, string.Empty,
                                         () => investmentService.TotalInvestmentAsync(loggedInUserName, selectedProjectName),
                                         WordDocumentHelper.ContentType.Title2);
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"插入总投资时发生错误：{ex.Message}\n\n{ex.StackTrace}");
                                    throw;
                                }
                                break;

                            case "4.3资金计划建议":
                                try
                                {
                                    await wordHelper.InsertSection(doc, "资金计划建议",
                                        () => Task.FromResult(string.Empty),
                                        WordDocumentHelper.ContentType.Title2);

                                    // 从生成的Excel文件中复制表五数据（仅粘贴值）
                                    await InsertExcelTableToWord(doc, "表五（分阶段投资估算表）", "4.3资金计划建议");

                                    await wordHelper.InsertSectionWithStyles(doc, string.Empty,
                                        () => investmentService.FundPlanAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);

                                    // 所有投资相关内容插入完成后，再切换回纵向
                                    wordHelper.InsertSectionBreak(doc);
                                    doc.Sections[doc.Sections.Count].PageSetup.Orientation = WdOrientation.wdOrientPortrait;
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"插入资金计划建议时发生错误：{ex.Message}\n\n{ex.StackTrace}");
                                    throw;
                                }
                                break;

                            case "5.1管理效益分析":
                                //5
                                await wordHelper.InsertSection(doc, "项目效益分析",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title1);
                                //5.1 
                                if (apiResults.ContainsKey("5.1管理效益分析"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "管理效益分析",
                                        () => Task.FromResult((List<ContentItem>)apiResults["5.1管理效益分析"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "管理效益分析",
                                        () => benefitService.ManagementBenefitAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "5.2经济效益分析":
                                //5.2 
                                if (apiResults.ContainsKey("5.2经济效益分析"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "经济效益分析",
                                        () => Task.FromResult((List<ContentItem>)apiResults["5.2经济效益分析"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "经济效益分析",
                                        () => benefitService.EconimicBenefitAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "5.3社会效益分析":
                                //5.3 
                                if (apiResults.ContainsKey("5.3社会效益分析"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "社会效益分析",
                                        () => Task.FromResult((List<ContentItem>)apiResults["5.3社会效益分析"]),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, "社会效益分析",
                                        () => benefitService.SocialBenefitAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.Title2);
                                }
                                break;

                            case "6.1项目风险分析":
                                try
                                {
                                    // 6. 项目风险分析（一级标题）
                                    await wordHelper.InsertSection(doc, "项目风险分析",
                                        () => Task.FromResult(string.Empty),
                                        WordDocumentHelper.ContentType.Title1);

                                    if (apiResults.ContainsKey("6.1项目风险分析"))
                                    {
                                        await wordHelper.InsertSectionWithStyles(doc, null,
                                            () => Task.FromResult((List<ContentItem>)apiResults["6.1项目风险分析"]),
                                            WordDocumentHelper.ContentType.MainText);
                                    }
                                    else
                                    {
                                        await wordHelper.InsertSectionWithStyles(doc, null,
                                            () => riskService.RiskAnalyseAsync(loggedInUserName, selectedProjectName),
                                            WordDocumentHelper.ContentType.MainText);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"处理风险分析数据时出错：{ex.Message}");
                                    throw;
                                }
                                break;

                            case "7.1项目可研结论":
                                //7
                                await wordHelper.InsertSection(doc, "项目可研结论",
                                    () => Task.FromResult(string.Empty), // 返回空字符串，只插入标题
                                    WordDocumentHelper.ContentType.Title1);

                                if (apiResults.ContainsKey("7.1项目可研结论"))
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, null,
                                        () => Task.FromResult((List<ContentItem>)apiResults["7.1项目可研结论"]),
                                        WordDocumentHelper.ContentType.MainText);
                                }
                                else
                                {
                                    await wordHelper.InsertSectionWithStyles(doc, null,
                                        () => conclusionService.ConclusionAsync(loggedInUserName, selectedProjectName),
                                        WordDocumentHelper.ContentType.MainText);
                                }
                                break;

                            default:
                                // 可以选择记录未实现的目录项
                                Console.WriteLine($"未实现的目录项: {menuItem}");
                                break;
                        }

                        // 在所有内容生成完成后，更新目录
                        if (doc.TablesOfContents.Count > 0)
                        {
                            doc.TablesOfContents[1].Update();
                        }

                        // 更新进度
                        completedTasks++;

                        // 释放UI线程，防止死锁
                        await Task.Delay(1);
                    }
                    catch (Exception ex)
                    {
                        // 记录具体哪个菜单项出错
                        string errorMessage = $"生成 '{currentMenuItem}' 时出错: {ex.Message}";

                        // 记录详细错误信息
                        System.Diagnostics.Debug.WriteLine(errorMessage);
                        System.Diagnostics.Debug.WriteLine(ex.StackTrace);

                        // 显示错误消息
                        CircleProgree.ShowMessageBox(errorMessage, "生成错误",
                            MessageBoxButtons.OK);

                        // 等待一会儿，让用户看到错误信息
                        await Task.Delay(500);

                        // 继续处理下一个菜单项，而不是中断整个过程
                        completedTasks++;
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                await circleProgree.UpdateProgress(0, "生成失败");
                CircleProgree.ShowMessageBox($"生成文档时发生错误：{ex.Message}\n\n{ex.StackTrace}", "网络请求失败");
                //throw new Exception($"插入内容时发生错误：{ex.Message}", ex);
            }
        }


        #endregion

        #region 文本选择三大功能-优化扩写精简

        //优化
        public async void Button4_Click(Office.IRibbonControl control)
        {
            try
            {
                // 创建 WriterApiParse 实例
                var writerApiParse = new WriterApiParse();

                // 调用 API 处理文本，进度条由 WriterApiParse 内部处理
                await writerApiParse.HandleSelectedTextAsync(Globals.ThisAddIn.Application.ActiveDocument, "polish");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"优化文本时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //扩写
        public async void Button5_Click(Office.IRibbonControl control)
        {
            try
            {
                // 创建 WriterApiParse 实例
                var writerApiParse = new WriterApiParse();

                // 调用 API 处理文本，进度条由 WriterApiParse 内部处理
                await writerApiParse.HandleSelectedTextAsync(Globals.ThisAddIn.Application.ActiveDocument, "expand");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"扩写文本时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //精简
        public async void Button6_Click(Office.IRibbonControl control)
        {
            try
            {
                // 创建 WriterApiParse 实例
                var writerApiParse = new WriterApiParse();

                // 调用 API 处理文本，进度条由 WriterApiParse 内部处理
                await writerApiParse.HandleSelectedTextAsync(Globals.ThisAddIn.Application.ActiveDocument, "shorten");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"精简文本时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion

        #region Group3 机器代人 回调函数

        #region comboBox1 文本框
        //主动点击项目名称
        public void Button30_Click(Office.IRibbonControl control)
        {
            // 清空企业和菜单的选择状态
            EnterpriseCheckboxStateManager.Clear();
            MenuCheckboxStateManager.Clear();
            // 清空本地引用
            selectedEnterprises.Clear();
            selectedMenu.Clear();

            ProjectForm projectForm = new ProjectForm();

            if (projectForm.ShowDialog() == DialogResult.OK && projectForm.IsConfirmed)
            {
                // 生成带年份的完整项目名称
                string combinedProjectName = GetFullProjectNameWithYear(
                    projectForm.SelectedProjectType, 
                    projectForm.ProjectName, 
                    projectForm.ProjectCycle);
                selectedProjectName = combinedProjectName;
                selectedProjectCycle = projectForm.ProjectCycle;  // 保存立项周期
                currentComboBoxText = combinedProjectName;  // 添加这行来更新显示文本
                ribbon.InvalidateControl("comboBox1");

                RefreshGenerate();
            }
            else
            {
                currentComboBoxText = string.Empty;  // 如果用户取消，清空显示文本
            }

            fileApiService = new FileApiService(loggedInUserName, selectedProjectName);

            _ = UpdateUploadedFilesAsync();
        }

        //当 ComboBox 选项改变时
        public async void ComboBox1_TextChanged(Office.IRibbonControl control, string text)
        {
            ProjectForm projectForm = new ProjectForm();
            // 如果项目名称变化，清除之前的选择状态
            if (currentComboBoxText != text)
            {
                // 清空企业和菜单的选择状态
                EnterpriseCheckboxStateManager.Clear();
                MenuCheckboxStateManager.Clear();
                // 清空本地引用
                selectedEnterprises.Clear();
                selectedMenu.Clear();
            }
            
            currentComboBoxText = text;

            if (!string.IsNullOrWhiteSpace(text))
            {
                try
                {
                    var coverService = new CoverApiService();
                    var projectName = await coverService.ProcessProjectNameAsync(loggedInUserName,text);//这里只给了封面第一行的单位，没有给项目名称和可研报告的文字

                    if (coverService.Result.status_code == 200)
                    {
                        if (customItems.Contains(text, StringComparer.OrdinalIgnoreCase))
                        {                            
                            selectedProjectName = text;

                            // 更新登录状态
                            if (isUserLoggedIn)
                            {
                                LoginStateManager.Instance.SetLoginState(loggedInUserName, selectedProjectName);
                            }
                        }
                        else
                        {
                            RefreshGenerate();
                            ribbon.InvalidateControl("comboBox1");
                            selectedProjectName = text;
                            currentComboBoxText = text;                            

                            // 更新登录状态
                            if (isUserLoggedIn)
                            {
                                LoginStateManager.Instance.SetLoginState(loggedInUserName, selectedProjectName);
                            }
                            else
                            {
                                currentComboBoxText = string.Empty;
                            }
                        }
                    }
                    else if (coverService.Result.status_code == 201)
                    {
                        // 设置项目名称到html的input
                        projectForm.SetProjectName(coverService.Result.data["project_name"].ToString());

                        if (projectForm.ShowDialog() == DialogResult.OK && projectForm.IsConfirmed)
                        {
                            // 生成带年份的完整项目名称
                            string combinedProjectName = GetFullProjectNameWithYear(
                                projectForm.SelectedProjectType, 
                                projectForm.ProjectName, 
                                projectForm.ProjectCycle);                          
                            selectedProjectName = combinedProjectName;
                            selectedProjectCycle = projectForm.ProjectCycle;  // 保存立项周期
                            currentComboBoxText = combinedProjectName;  // 添加这行来更新显示文本
                            ribbon.InvalidateControl("comboBox1");
                            RefreshGenerate();
                        }
                        else
                        {
                            currentComboBoxText = string.Empty;  // 如果用户取消，清空显示文本
                        }
                    }
                }
                catch (Exception)
                {
                    if (projectForm.ShowDialog() == DialogResult.OK && projectForm.IsConfirmed)
                    {
                        // 生成带年份的完整项目名称
                        string combinedProjectName = GetFullProjectNameWithYear(
                            projectForm.SelectedProjectType, 
                            projectForm.ProjectName, 
                            projectForm.ProjectCycle);

                            selectedProjectName = combinedProjectName;
                            selectedProjectCycle = projectForm.ProjectCycle;  // 保存立项周期
                            currentComboBoxText = combinedProjectName;  // 添加这行来更新显示文本
                            ribbon.InvalidateControl("comboBox1");
                            RefreshGenerate();
                    }
                    else
                    {
                        currentComboBoxText = string.Empty;  // 如果用户取消，清空显示文本
                    }
                }
            }

            if (isUserLoggedIn && !string.IsNullOrEmpty(selectedProjectName))
            {
                fileApiService = new FileApiService(loggedInUserName, selectedProjectName);
            }

            await UpdateUploadedFilesAsync();
            ribbon.InvalidateControl("comboBox1");
        }

        //获取 ComboBox 文本
        public string GetComboBoxText(Office.IRibbonControl control)
        {
            return currentComboBoxText;
        }

        // 获取 ComboBox 项目数量
        public int GetComboBoxItemCount(Office.IRibbonControl control)
        {
            return customItems.Count;
        }

        // 获取 ComboBox 项目标签
        public string GetComboBoxItemLabel(Office.IRibbonControl control, int index)
        {
            if (index >= 0 && index < customItems.Count)
            {
                return customItems[index];
            }
            return string.Empty;
        }

        public void UpdateProjectFromGenerate(string projectName)
        {
            if (!string.IsNullOrWhiteSpace(projectName))
            {
                currentComboBoxText = projectName;
                selectedProjectName = projectName;

                // 更新登录状态
                if (isUserLoggedIn)
                {
                    LoginStateManager.Instance.SetLoginState(loggedInUserName, selectedProjectName);
                }

                // 刷新 ComboBox
                ribbon.InvalidateControl("comboBox1");

                // 如果 Generate 窗体打开，更新其项目名称标签
                if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
                {
                    currentGenerateForm.UpdateProjectNameLabel(projectName);
                }
            }
        }

        #endregion

        #region 文件上传

        // 上传参考文件的点击事件
        public async void Button7_Click(Office.IRibbonControl control)
        {
            if (!isUserLoggedIn)
            {
                MessageBox.Show("请先登录！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrEmpty(selectedProjectName))
            {
                MessageBox.Show("请先选择项目名称！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Multiselect = true;
                openFileDialog.Filter = "Word文档和Excel文件|*.docx;*.doc;*.xlsx;*.xls|Word文档|*.docx;*.doc|Excel文件|*.xlsx;*.xls";
                openFileDialog.Title = "选择要上传的文件";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 显示进度条
                    var progressForm = new ProgressForm("配置参数更新");

                    try
                    {
                        progressForm.Show();

                        // 创建进度报告对象
                        var progress = new Progress<int>(percent =>
                        {
                            progressForm.UpdateProgress(percent);
                        });

                        // 创建文件API服务
                        var fileService = new FileApiService(loggedInUserName, selectedProjectName);

                        // 上传文件并获取完整响应
                        var response = await fileService.UploadFilesAsync(
                            openFileDialog.FileNames,
                            50 * 1024 * 1024, // 50MB
                            progress
                        );

                        // 从响应中提取文件名列表
                        var uploadedFileNames = fileService.ExtractUploadedFileNames(response);
                        uploadedFiles.AddRange(uploadedFileNames);

                        // 从响应中提取项目信息
                        var projectInfo = fileService.ExtractProjectInfo(response);
                        if (projectInfo.ContainsKey("项目名称") && projectInfo.ContainsKey("项目类"))
                        {
                            string newProjectName = $"{projectInfo["项目类"]}（{projectInfo["项目名称"]}）";
                            
                            // 如果项目名称变化，重置状态
                            if (selectedProjectName != newProjectName)
                            {
                                // 清空之前的企业和菜单选择
                                EnterpriseCheckboxStateManager.Clear();
                                MenuCheckboxStateManager.Clear();
                                selectedEnterprises.Clear();
                                selectedMenu.Clear();
                                
                                // 更新项目名称
                                selectedProjectName = newProjectName;
                                currentComboBoxText = selectedProjectName;
                                ribbon.InvalidateControl("comboBox1");
                            }
                        }

                        // 从响应中提取单位列表
                        var unitList = fileService.ExtractUnitList(response);
                        if (unitList.Count > 0)
                        {
                            // 更新企业选择状态
                            selectedEnterprises.Clear();
                            selectedEnterprises.AddRange(unitList);

                            // 使用新添加的方法更新企业选择状态
                            GlobalParameters.UpdateEnterpriseSelection(unitList, true);

                            // 从 EnterpriseCheckboxStateManager 获取更新后的选中项
                            selectedEnterprises = EnterpriseCheckboxStateManager.GetCheckedItems();

                            ribbon.InvalidateControl("button8");
                        }

                        // 从响应中提取七参数配置
                        var projectConfig = fileService.ExtractProjectConfig(response);
                        if (projectConfig.Count > 0)
                        {
                            // 更新全局参数
                            if (GlobalParameters.CalculateData == null)
                            {
                                GlobalParameters.CalculateData = new CalculateFormData();
                            }

                            if (projectConfig.ContainsKey("project_category"))
                                GlobalParameters.CalculateData.Select3 = projectConfig["project_category"].ToString();

                            if (projectConfig.ContainsKey("construction_cycle"))
                                GlobalParameters.CalculateData.Select12 = projectConfig["construction_cycle"].ToString();

                            if (projectConfig.ContainsKey("project_type"))
                                GlobalParameters.CalculateData.Select1 = projectConfig["project_type"].ToString();

                            if (projectConfig.ContainsKey("construction_nature"))
                                GlobalParameters.CalculateData.Select7 = projectConfig["construction_nature"].ToString();

                            if (projectConfig.ContainsKey("system_security_level"))
                                GlobalParameters.CalculateData.Select9 = projectConfig["system_security_level"].ToString();

                            if (projectConfig.ContainsKey("system_user_number") && projectConfig["system_user_number"] != null)
                            {
                                if (double.TryParse(projectConfig["system_user_number"].ToString(), out double userNumber))
                                    GlobalParameters.CalculateData.Input10 = userNumber;
                            }

                            if (projectConfig.ContainsKey("system_deployment_mode"))
                                GlobalParameters.CalculateData.Select11 = projectConfig["system_deployment_mode"].ToString();
                        }

                        // 从响应中提取章节列表
                        var chapterList = fileService.ExtractChapterList(response);
                        if (chapterList.Count > 0)
                        {
                            // 更新菜单选择状态
                            selectedMenu.Clear();
                            // 使用新添加的方法更新菜单选择状态，将isUserModified设置为true
                            GlobalParameters.UpdateMenuSelection(chapterList, true);
                            // 从 MenuCheckboxStateManager 获取更新后的选中项
                            selectedMenu = MenuCheckboxStateManager.GetCheckedItems();

                            ribbon.InvalidateControl("button9");
                        }

                        // 关闭进度条
                        progressForm.Close();

                        // 更新生成表单（如果已打开）
                        if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
                        {
                            currentGenerateForm.SetFileList(uploadedFiles);
                        }

                        MessageBox.Show($"成功上传 {uploadedFileNames.Count} 个文件！", "上传成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 等待一下再更新文件列表
                        await Task.Delay(500);
                        await UpdateUploadedFilesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"上传文件时出错: {ex.Message}", "上传错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        // 关闭进度条
                        progressForm.Close();
                    }
                }
            }
        }

        // 删除上传文件的回调事件
        public async void DeleteUploadedFile(Office.IRibbonControl control)
        {
            if (!EnsureFileApiServiceInitialized()) return;

            int buttonIndex = GetButtonIndexFromId(control.Id);
            if (buttonIndex >= 0 && buttonIndex < uploadedFiles.Count)
            {
                string fileToDelete = uploadedFiles[buttonIndex];

                var confirmResult = MessageBox.Show($"确定要删除文件 '{fileToDelete}' 吗？", "确认删除", MessageBoxButtons.YesNo);
                if (confirmResult == DialogResult.Yes)
                {
                    try
                    {
                        // 调用 API 删除文件
                        string result = await fileApiService.DeleteFileAsync(fileToDelete);
                        // 更新已上传文件列表并刷新按钮标签
                        await UpdateUploadedFilesAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"删除文件时发生错误: {ex.Message}");
                    }
                }
            }
        }

        // 动态获取按钮标签的回调函数
        public string GetFileLabel(Office.IRibbonControl control)
        {
            int buttonIndex = GetButtonIndexFromId(control.Id);
            string fileName = (buttonIndex >= 0 && buttonIndex < uploadedFiles.Count) ? uploadedFiles[buttonIndex] : unuploadFileName;
            return fileName;
        }

        // 动态获取按钮Screentip的回调函数
        public string GetFileScreentip(Office.IRibbonControl control)
        {
            int buttonIndex = GetButtonIndexFromId(control.Id);
            return (buttonIndex >= 0 && buttonIndex < uploadedFiles.Count) ? uploadedFiles[buttonIndex] : unuploadFileName;
        }

        public bool GetButtonVisible(Office.IRibbonControl control)
        {
            int buttonIndex = GetButtonIndexFromId(control.Id);
            return buttonIndex >= 0 && buttonIndex < uploadedFiles.Count;
        }

        #endregion

        #region 参数勾选及同步

        // 添加一个方法来获取选中的企业
        public List<string> GetSelectedEnterprises()
        {
            return selectedEnterprises;
        }

        // 选择建设单位
        public async void Button8_Click(Office.IRibbonControl control)
        {
            using (EnterprisesForm enterprisesForm = new EnterprisesForm())
            {
                enterprisesForm.ShowDialog();

                // 从状态管理器获取选中的企业
                selectedEnterprises = EnterpriseCheckboxStateManager.GetCheckedItems();
                
                // 添加这行来刷新按钮图标
                ribbon.InvalidateControl("button8");

                ParametersApiService parametersService = new ParametersApiService();

                // 准备参数
                string construction_unit = string.Join("、", selectedEnterprises);

                //调用参数配置服务前打印参数值，便于调试
                //CircleProgree.ShowMessageBox($"用户名: {loggedInUserName}，" +
                //    $"项目名称: {selectedProjectName}，" +
                //    $"建设单位: {construction_unit}，" +
                //    $"建设周期: {GlobalParameters.CalculateData.Select12}，" +
                //    $"项目分类: {GlobalParameters.CalculateData.Select3}，" +
                //    $"项目类型: {GlobalParameters.CalculateData.Select1}，" +
                //    $"建设性质: {GlobalParameters.CalculateData.Select7}," +
                //    $" 系统等保级别: { GlobalParameters.CalculateData.Select9}," +
                //    $"系统用户数量: {GlobalParameters.CalculateData.Input10}," +
                //    $"系统部署方式: {GlobalParameters.CalculateData.Select11}");

                LoginStateManager.Instance.SetUnitState(construction_unit);

                // 调用参数配置服务
                await parametersService.ParaConfigAsync(
                    loggedInUserName,
                    selectedProjectName,
                    construction_unit,
                    GlobalParameters.CalculateData.Select12 ?? "",
                    GlobalParameters.CalculateData.Select3 ?? "",
                    GlobalParameters.CalculateData.Select1 ?? "",
                    GlobalParameters.CalculateData.Select7 ?? "",
                    GlobalParameters.CalculateData.Select9 ?? "",
                    GlobalParameters.CalculateData.Input10,
                    GlobalParameters.CalculateData.Select11 ?? ""
                );
            }
        }

        // 添加一个方法来获取选中的目录结构
        public List<string> GetSelectedMenu()
        {
            return selectedMenu;
        }

        //设置目录结构
        public void Button9_Click(Office.IRibbonControl control)
        {
            using(MenuForm menuForm = new MenuForm()){
                menuForm.ShowDialog();

                // 从状态管理器获取选中的菜单
                selectedMenu = MenuCheckboxStateManager.GetCheckedItems();
                
                // 添加这行来刷新按钮图标
                ribbon.InvalidateControl("button9");
            }    
        }

        //直接生成
        public void Button29_Click(Office.IRibbonControl control)
        {
            var task = Button29_Click_Async(control);
            task.ContinueWith(t =>
            {
                System.Diagnostics.Debug.WriteLine($"[UNHANDLED TASK EXCEPTION in Button29_Click]: {t.Exception}");
            }, TaskContinuationOptions.OnlyOnFaulted);
        }

        private async Task Button29_Click_Async(Office.IRibbonControl control)
        {
            if (isGenerating)
            {
                MessageBox.Show("正在生成文档，请稍候...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 检查项目名称
            if (string.IsNullOrEmpty(selectedProjectName))
            {
                MessageBox.Show("请先选择或输入项目名称！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查是否选择了企业
            if (selectedEnterprises == null || selectedEnterprises.Count == 0)
            {
                MessageBox.Show("请先选择建设单位！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查是否选择了目录结构
            if (selectedMenu == null || selectedMenu.Count == 0)
            {
                MessageBox.Show("请先选择目录结构！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            isGenerating = true;
            ribbon.Invalidate(); // 使按钮失效

            try
            {
                // 创建进度条
                circleProgree = new CircleProgree();

                // 注册取消事件处理程序
                circleProgree.CancelRequested += async (sender, e) =>
                {
                    try
                    {
                        // 优化：使用新的取消状态更新方法
                        await circleProgree.UpdateCancellationStatus("生成已取消\n\r正在清理资源...");

                        // 给用户一些时间看到取消消息
                        await Task.Delay(1000);

                        // 最终状态显示
                        await circleProgree.UpdateCancellationStatus("取消完成");
                        await Task.Delay(500);

                        circleProgree.Close();
                    }
                    catch (Exception)
                    {
                        // 如果更新状态失败，直接关闭
                        try
                        {
                            circleProgree.Close();
                        }
                        catch { }
                    }
                };

                circleProgree.Show();
                Globals.ThisAddIn.Application.ScreenUpdating = false;

                // 执行生成逻辑
                await GenerateFullDocumentAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CRITICAL ERROR in Button29_Click]: {ex.ToString()}");
                MessageBox.Show($"生成文档时发生严重错误: {ex.Message}", "严重错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1,
                    MessageBoxOptions.DefaultDesktopOnly);
            }
            finally
            {
                Globals.ThisAddIn.Application.ScreenUpdating = true;
                if (circleProgree != null)
                {
                    circleProgree.Close();
                    circleProgree.Dispose();
                }
                isGenerating = false;
                ribbon.Invalidate(); // 重新使能按钮
            }
        }

        // 资料检查
        public async void Button32_Click(Office.IRibbonControl control)
        {
            Word.Document doc = null;
            int maxRetries = 3;
            int currentRetry = 0;
            bool documentOpened = false;

            // 创建进度条和遮罩
            if (circleProgree == null || circleProgree.IsDisposed)
            {
                circleProgree = new CircleProgree();
            }

            // 获取当前活动窗口句柄
            IntPtr currentWordHandle = (IntPtr)Globals.ThisAddIn.Application.ActiveWindow.Hwnd;
            circleProgree.Show(currentWordHandle);

            // 设置初始进度
            await circleProgree.UpdateProgress(0, "准备检查文档...");

            while (currentRetry < maxRetries)
            {
                try
                {
                    if (string.IsNullOrEmpty(selectedProjectName))
                    {
                        CircleProgree.ShowMessageBox("请确保您已选择了项目名称。", "请确保您已选择了项目名称");
                        return;
                    }

                    // 检查是否选择了企业单位
                    List<string> selectedEnterprises = GetSelectedEnterprises();
                    if (selectedEnterprises == null || selectedEnterprises.Count == 0)
                    {
                        CircleProgree.ShowMessageBox("请先选择投资建设单位。", "未选择投资建设单位");
                        return;
                    }

                    // 检查是否选择了生成目录
                    List<string> selectedMenu = GetSelectedMenu();
                    if (selectedMenu == null || selectedMenu.Count == 0)
                    {
                        CircleProgree.ShowMessageBox("请先选择要生成的章节。", "未选择生成章节");
                        return;
                    }

                    // 确保 CalculateData 已初始化
                    if (GlobalParameters.CalculateData == null)
                    {
                        GlobalParameters.CalculateData = new CalculateFormData();
                    }

                    // 获取当前 Word 应用程序
                    Word.Application wordApp = Globals.ThisAddIn.Application;

                    // 获取保存路径设置
                    string savePath = GetDefaultSavePath();

                    // 基于保存路径构建模板路径
                    string templatePath = Path.Combine(savePath, "Style", "template1117.dotx");

                    if (!File.Exists(templatePath))
                    {
                        CircleProgree.ShowMessageBox($"未找到模板文件，请确保模板文件存在于以下位置：\n{templatePath}", "未找到模板文件");
                        return;
                    }

                    await circleProgree.UpdateProgress(5, "正在检查文档...");

                    // 创建基于模板的新文档
                    object template = templatePath;
                    object missing = System.Reflection.Missing.Value;
                    doc = wordApp.Documents.Add(ref template, ref missing, ref missing, ref missing);

                    if (doc == null)
                    {
                        CircleProgree.ShowMessageBox("创建新文档失败。", "创建新文档失败");
                        return;
                    }

                    // 获取新文档的窗口句柄并更新进度条
                    IntPtr newDocHandle = (IntPtr)doc.ActiveWindow.Hwnd;

                    // 等待新文档完全加载
                    await Task.Delay(300);

                    // 更新进度条跟踪的窗口句柄
                    circleProgree.Close();
                    circleProgree = new CircleProgree();
                    circleProgree.Show(newDocHandle);
                    await circleProgree.UpdateProgress(10, "正在准备数据...\n\r初始化检查系统");


                    // 创建服务实例
                    CoverApiService coverService = new CoverApiService();
                    CheckWordApiService checkWordApiService = new CheckWordApiService();
                    CheckReportApiService checkReportApiService = new CheckReportApiService();

                    await circleProgree.UpdateProgress(15, "正在生成文档内容...\n\r设置页面格式");

                    // 设置页眉页脚
                    wordHelper.SetHeaderAndFooter(doc);

                    // 插入项目名称
                    await wordHelper.InsertSection(doc, "\n",
                        () => coverService.ProcessCoverProjectNameAsync(loggedInUserName, selectedProjectName),
                        WordDocumentHelper.ContentType.CoverFisrtTitle,
                        WordDocumentHelper.ContentType.CoverFisrtTitle);

                    // 年份已经包含在selectedProjectName中，无需重复计算

                    // 插入项目名称
                    await wordHelper.InsertSection(doc, "",
                        () => Task.FromResult(selectedProjectName+"项目"),
                        WordDocumentHelper.ContentType.CoverFisrtTitle,
                        WordDocumentHelper.ContentType.CoverFisrtTitle);

                    // 插入项目名称的"可行性研究报告"
                    await wordHelper.InsertSection(doc, "",
                        () => Task.FromResult( "可行性研究报告"),
                        WordDocumentHelper.ContentType.CoverFisrtTitle,
                        WordDocumentHelper.ContentType.CoverFisrtTitle);

                    // 插入公司名称（使用空行和居中对齐）
                    await wordHelper.InsertSection(doc,
                        new string('\n', 10) + "中国能源建设集团广东省电力设计研究院有限公司",
                        () => Task.FromResult(string.Empty),
                        WordDocumentHelper.ContentType.AuthorInfo,
                        WordDocumentHelper.ContentType.AuthorInfo);             

                    // 插入年月
                    await wordHelper.InsertSection(doc,
                        DateTime.Now.ToString("yyyy年MM月"),
                        () => Task.FromResult(string.Empty),
                        WordDocumentHelper.ContentType.AuthorInfo,
                        WordDocumentHelper.ContentType.AuthorInfo);

                    // 插入分节符
                    wordHelper.InsertSectionBreak(doc);

                    // 插入作者信息
                    await wordHelper.InsertSection(doc,
                        new string('\n', 8), // 前置空行
                        async () =>
                        {
                            var staffsResult = await coverService.ProcessAuthorAsync(loggedInUserName);
                            var content = string.Join("\n\n", staffsResult.Select(kv => $"{kv.Key}: {kv.Value}"));
                            return content;  // 不在这里添加后置空行
                        },
                        WordDocumentHelper.ContentType.AuthorInfo,
                        WordDocumentHelper.ContentType.AuthorInfo);

                    // 添加后置空行
                    for (int i = 0; i < 6; i++)
                    {
                        Word.Paragraph emptyPara = doc.Content.Paragraphs.Add();
                        emptyPara.Range.Text = "";
                        emptyPara.Range.InsertParagraphAfter();
                    }

                    // 插入分节符
                    wordHelper.InsertSectionBreak(doc);

                    // 先插入目录
                    Word.Range tocRange = doc.Range(doc.Content.End - 1, doc.Content.End - 1);

                    // 插入"目录"标题
                    Word.Paragraph tocTitle = doc.Content.Paragraphs.Add();
                    tocTitle.Range.Text = "目    录";
                    tocTitle.Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                    tocTitle.Range.Font.Size = 16;
                    tocTitle.Range.Font.Bold = 1;
                    tocTitle.Range.Font.Name = "宋体";
                    tocTitle.Range.InsertParagraphAfter();

                    // 直接插入目录，移除了额外的空行段落
                    doc.TablesOfContents.Add(doc.Range(doc.Content.End - 1, doc.Content.End - 1),
                        UseHeadingStyles: true,
                        UpperHeadingLevel: 1,
                        LowerHeadingLevel: 3,
                        UseHyperlinks: true,
                        HidePageNumbersInWeb: false,
                        UseOutlineLevels: true);

                    // 插入分页符
                    Word.Range pageBreak = doc.Range(doc.Content.End - 1, doc.Content.End - 1);
                    pageBreak.InsertBreak(Word.WdBreakType.wdPageBreak);

                    // 插入正文（只用CheckWordApiService接口）
                    await wordHelper.InsertSectionWithStyles(doc, "", () => checkWordApiService.CheckWordApiAsync(loggedInUserName, selectedProjectName), WordDocumentHelper.ContentType.Title1);

                    // 调用检查报告API并插入检查报告
                    await circleProgree.UpdateProgress(55, "正在生成检查报告...\n\r分析文档完整性");
                    try
                    {
                        var checkReportItems = await checkReportApiService.GenerateCheckReportAsync(loggedInUserName, selectedProjectName);
                        if (checkReportApiService.ValidateResponse(checkReportItems))
                        {
                            System.Diagnostics.Debug.WriteLine($"检查报告生成成功，共{checkReportItems.Count}项");
                            wordHelper.InsertCheckReport(doc, checkReportItems);
                            await circleProgree.UpdateProgress(60, "检查报告生成完成\n\r准备保存文档");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("检查报告生成失败或返回空数据");
                            await circleProgree.UpdateProgress(60, "检查报告生成失败\n\r继续保存文档");
                        }
                    }
                    catch (Exception reportEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"生成检查报告时出错: {reportEx.Message}");
                        CircleProgree.ShowMessageBox($"生成检查报告时出错：{reportEx.Message}", "检查报告错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        await circleProgree.UpdateProgress(60, "检查报告生成出错\n\r继续保存文档");
                    }

                    // Excel检查功能已禁用 - 资料检查功能跳过Excel文件检查
                    // 注释：不管"同步生成估算书"复选框状态如何，都跳过Excel检查
                    /*
                    if (checkBox1Pressed)
                    {
                        // 检查是否请求了取消
                        if (circleProgree.IsCancellationRequested())
                        {
                            await circleProgree.UpdateProgress(65, "正在取消检查Excel...\n\r请稍等");
                            return; // 如果请求取消，直接返回
                        }

                        await circleProgree.UpdateProgress(65, "检查Excel耗时长，请耐心等待...");

                        try
                        {
                            // 启用Excel覆盖功能
                            circleProgree.EnableExcelCoverage();

                            // 设置进度表单到DocumentSyncService
                            DocumentSyncService.Instance.SetProgressForm(circleProgree);

                            // 直接使用项目名称和用户信息生成Excel
                            string excelPath = await DocumentSyncService.Instance.CheckExcelFromWordAsync(
                                selectedProjectName,  // 使用项目名称
                                true,
                                true
                            );

                            if (!string.IsNullOrEmpty(excelPath))
                            {
                                await circleProgree.UpdateProgress(70, "Excel检查成功\n\r正在打开文件");
                                System.Diagnostics.Process.Start(excelPath);

                                // 等待Excel打开
                                await Task.Delay(1000);
                            }
                        }
                        catch (Exception excelEx)
                        {
                            CircleProgree.ShowMessageBox($"检查Excel文件时出错：{excelEx.Message}", "Excel检查错误");
                        }
                    }
                    */

                    // 更新文档中的所有域
                    doc.Fields.Update();

                    // 生成统一的时间戳，用于检查文档和Excel文件夹命名
                    string checkSharedTimestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

                    // 创建项目专用文件夹（使用共享时间戳）
                    string projectFolderPath = CreateProjectFolder(savePath, selectedProjectName, checkSharedTimestamp);

                    // 构建完整的文件路径（去除时间戳）
                    string fileName = $"{selectedProjectName}检查.docx";
                    string fullPath = Path.Combine(projectFolderPath, fileName);

                    // 保存文档
                    try
                    {
                        object saveAsPath = fullPath;
                        doc.SaveAs2(ref saveAsPath);

                        await circleProgree.UpdateProgress(96, "检查文档保存成功\n\r准备打开文档");
                        await circleProgree.UpdateProgress(98, "正在打开检查文档...");

                        try
                        {
                            // 关闭当前文档
                            object saveChanges = WdSaveOptions.wdDoNotSaveChanges;
                            object originalFormat = Missing.Value;
                            object routeDocument = Missing.Value;
                            doc.Close(ref saveChanges, ref originalFormat, ref routeDocument);
                            Marshal.ReleaseComObject(doc);
                            doc = null;

                            // 先保存进度条引用，确保后续能正确关闭
                            var currentProgress = circleProgree;
                            circleProgree = null;

                            // 打开保存的文档
                            object readOnly = false;
                            object isVisible = true;
                            Word.Document openedDoc = wordApp.Documents.Open(
                                fullPath,
                                ReadOnly: readOnly,
                                Visible: isVisible
                            );

                            documentOpened = true;

                            // 使用定时器确保进度条关闭
                            System.Windows.Forms.Timer closeTimer = new System.Windows.Forms.Timer();
                            closeTimer.Interval = 500;
                            closeTimer.Tick += (sender, e) =>
                            {
                                closeTimer.Stop();
                                
                                // 在UI线程上关闭进度条
                                if (currentProgress != null && !currentProgress.IsDisposed)
                                {
                                    try
                                    {
                                        if (currentProgress.InvokeRequired)
                                        {
                                            currentProgress.Invoke(new System.Action(() =>
                                            {
                                                currentProgress.Close();
                                                currentProgress.Dispose();
                                            }));
                                        }
                                        else
                                        {
                                            currentProgress.Close();
                                            currentProgress.Dispose();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"关闭进度条时出错: {ex.Message}");
                                    }
                                }

                                closeTimer.Dispose();
                            };

                            closeTimer.Start();

                            CircleProgree.ShowMessageBox($"可研检查完毕！文件已保存至:{fullPath}", "检查完毕");
                        }
                        catch (Exception openEx)
                        {
                            if (circleProgree != null && !circleProgree.IsDisposed)
                            {
                                circleProgree.Close();
                                circleProgree.Dispose();
                                circleProgree = null;
                            }
                            CircleProgree.ShowMessageBox($"打开检查文档时发生错误: {openEx.Message}", "打开检查文档时发生错误");
                            throw;
                        }

                        break; // 成功则跳出重试循环
                    }
                    catch (Exception saveEx)
                    {
                        CircleProgree.ShowMessageBox($"保存检查文档时发生错误: {saveEx.Message}", "保存检查文档时发生错误");
                        throw;
                    }
                }
                catch (HttpRequestException ex)
                {
                    currentRetry++;
                    if (currentRetry >= maxRetries)
                    {
                        CircleProgree.ShowMessageBox($"网络请求失败，已重试{maxRetries}次：\n{ex.Message}", "网络请求失败");
                    }
                    else
                    {
                        await circleProgree.UpdateProgress(0, $"网络请求失败，正在重试({currentRetry}/{maxRetries})...");
                        await Task.Delay(1000 * currentRetry);
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    CircleProgree.ShowMessageBox($"检查文档时发生错误: {ex.Message}\n\n详细信息: {ex.StackTrace}", "检查文档时发生错误");
                    break;
                }
                finally
                {
                    // 只在没有成功打开新文档时才清理
                    if (!documentOpened && doc != null)
                    {
                        try
                        {
                            object saveChanges = WdSaveOptions.wdDoNotSaveChanges;
                            object originalFormat = Missing.Value;
                            object routeDocument = Missing.Value;

                            doc.Close(ref saveChanges, ref originalFormat, ref routeDocument);
                            Marshal.ReleaseComObject(doc);
                            doc = null;
                        }
                        catch (Exception closeEx)
                        {
                            Console.WriteLine($"关闭文档时发生错误: {closeEx.Message}");
                        }
                    }

                    // 确保进度条关闭
                    if (circleProgree != null && !circleProgree.IsDisposed)
                    {
                        circleProgree.Close();
                        circleProgree.Dispose();
                        circleProgree = null;
                    }

                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
        }

        //生成设置
        public void Button17_Click(Office.IRibbonControl control)
        {
            if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
            {
                currentGenerateForm.Close();
                currentGenerateForm.Dispose();
                currentGenerateForm = null;
            }

            currentGenerateForm = new GenerateForm(selectedProjectName, this);  // 传入项目名称

            currentGenerateForm.FormClosed += (sender, e) =>
            {
                // 在窗体关闭时更新选中的列表
                selectedEnterprises = EnterpriseCheckboxStateManager.GetCheckedItems();
                selectedMenu = MenuCheckboxStateManager.GetCheckedItems();
                currentGenerateForm.Dispose();
                currentGenerateForm = null;
            };

            // 添加状态变更事件处理
            currentGenerateForm.EnterpriseStateChanged += (checkedItems) =>
            {
                selectedEnterprises = checkedItems;
            };

            currentGenerateForm.MenuStateChanged += (checkedItems) =>
            {
                selectedMenu = checkedItems;
            };

            currentGenerateForm.SetFileList(uploadedFiles);
            currentGenerateForm.Show(); // 使用 Show 而不是 ShowDialog
        }

        public bool GetCheckBox1Pressed(Office.IRibbonControl control)
        {
            return checkBox1Pressed;
        }

        public void CheckBox1_Click(Office.IRibbonControl control, bool pressed)
        {
            checkBox1Pressed = pressed;

            // 同步到 Generate.html
            if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
            {
                currentGenerateForm.UpdateEstimateCheckboxState(pressed);
            }

            ribbon.InvalidateControl("checkBox1");
        }

        public string GetEditBox1Text(Office.IRibbonControl control)
        {
            return editBox1Text;
        }

        public void EditBox1_TextChanged(Office.IRibbonControl control, string text)
        {
            editBox1Text = text;

            // 同步到 Generate.html
            if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
            {
                currentGenerateForm.UpdateExpectMoneyValue(text);
            }
        }

        public void UpdateCheckBox1FromGenerate(bool pressed)
        {
            if (checkBox1Pressed != pressed)
            {
                checkBox1Pressed = pressed;
                ribbon.InvalidateControl("checkBox1");
            }
        }

        public void UpdateEditBox1FromGenerate(string text)
        {
            if (editBox1Text != text)
            {
                editBox1Text = text;
                ribbon.InvalidateControl("editBox1");
            }
        }

        #endregion

        #endregion

        #region Group4 更多 回调函数
        //模板下载
        public void Button31_Click(Office.IRibbonControl control)
        {
            try
            {
                string url = "http://gpt.gedi.com.cn/template.html";
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                };
                Process.Start(psi);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开网页: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //虚拟顾问
        public void Button18_Click(Office.IRibbonControl control)
        {
            try
            {
                var taskPane = Globals.ThisAddIn.CustomTaskPanes.Add(new VirtualAssistant(), "虚拟顾问团");
                taskPane.Visible = true;
                taskPane.Width = 700; // 设置宽度

                // 虚拟顾问团的URL，需要访问权限或内网
                ((VirtualAssistant)taskPane.Control).NavigateToUrl("http://gpt.gedi.com.cn:7072/");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //企业架构导览
        public void Button19_Click(Office.IRibbonControl control)
        {
            try
            {
                string url = "http://10.150.112.80:12006";
                ProcessStartInfo psi = new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                };
                Process.Start(psi);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开网页: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //设置
        public void Button20_Click(Office.IRibbonControl control)
        {
            SettingForm settingForm = new SettingForm();
            settingForm.ShowDialog();
        }

        //帮助
        public void Button21_Click(Office.IRibbonControl control)
        {
            HelpForm helpForm = new HelpForm();
            helpForm.ShowDialog();
        }

        //反馈
        public void Button22_Click(Office.IRibbonControl control)
        {
            FeedbackForm feedbackForm = new FeedbackForm();
            feedbackForm.ShowDialog();
        }

        //关于
        public void Button23_Click(Office.IRibbonControl control)
        {
            AboutForm aboutForm = new AboutForm();
            aboutForm.ShowDialog();
        }

        // 当用户点击自定义的右键菜单项时触发的事件 
        public void OnMyCustomMenuItemClick(Office.IRibbonControl control)
        {
            System.Windows.Forms.MessageBox.Show("你点击了自定义右键菜单项！");
        }

        #endregion

        #region 图片资源回调方法
        public stdole.IPictureDisp GetUserImage(IRibbonControl control)
        {
            // 根据登录状态返回不同的头像
            Bitmap image = isUserLoggedIn
                ? Properties.Resources.logged_in_user  
                : Properties.Resources.group1_user;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetHomeImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group1_home;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetLogoutImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group1_logout;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetAIGenerateImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group2_generate;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetPolishImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group2_polish;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetExpandImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group2_expand;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetSimplifyImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group2_shorten;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetUploadImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_upload;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetEnterpriseImage(IRibbonControl control)
        {
            // 根据 selectedEnterprises 的状态选择不同的图片
            Bitmap image = selectedEnterprises != null && selectedEnterprises.Count > 0
                ? Properties.Resources.group3_checked  
                : Properties.Resources.group3_enterprise;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetMenuImage(IRibbonControl control)
        {
            // 根据 selectedMenu 的状态选择不同的图片
            Bitmap image = selectedMenu != null && selectedMenu.Count > 0
                ? Properties.Resources.group3_checked  
                : Properties.Resources.group3_menu;           

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetFileImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_close;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetFileMenuImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_filemenu;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetBudgetImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_budget;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetAllImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_nowgenerate;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetInfoSetImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_dashboard;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetParametersImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_parameters;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetCopilotImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_advisor;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetExlinkImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_structure;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetSetImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_setting;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetHelpImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_help;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetFeedbackImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_feedback;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetAboutImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group4_about;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetDownloadImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_preview;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        public stdole.IPictureDisp GetProjectNameImage(IRibbonControl control)
        {
            Bitmap image = Properties.Resources.group3_generate;

            // 使用 PictureConverter 来将图片转换为 IPictureDisp
            return PictureConverter.ImageToPictureDisp(image);
        }

        #endregion

        #region 文件上传
        // 删除已上传文件
        public async Task DeleteFileAsync(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(loggedInUserName) || string.IsNullOrEmpty(selectedProjectName))
                {
                    throw new ArgumentException("文件名、用户名或项目名称不能为空");
                }

                // 创建 FileApiService 实例
                if (fileApiService == null)
                {
                    fileApiService = new FileApiService(loggedInUserName, selectedProjectName);
                }

                // 调用 FileApiService 删除文件
                await fileApiService.DeleteFileAsync(fileName);

                // 删除成功后更新已上传文件列表
                await Task.Delay(500); // 等待一下再更新文件列表
                await UpdateUploadedFilesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除文件时出错: {ex.Message}");
                throw; // 重新抛出异常，让调用者处理
            }
        }

        // 添加公共方法供 GenerateForm 调用
        public async Task<Dictionary<string, object>> UploadFilesAsync(string[] filePaths, IProgress<int> progress = null)
        {
            try
            {
                if (string.IsNullOrEmpty(loggedInUserName) || string.IsNullOrEmpty(selectedProjectName))
                {
                    return new Dictionary<string, object> { { "status", "error" }, { "message", "请先登录并选择项目" } };
                }

                // 创建 FileApiService 实例
                if (fileApiService == null)
                {
                    fileApiService = new FileApiService(loggedInUserName, selectedProjectName);
                }

                // 设置最大文件大小（例如 100MB）
                long maxFileSize = 100 * 1024 * 1024;

                // 调用上传方法
                var response = await fileApiService.UploadFilesAsync(filePaths, maxFileSize, progress);

                if (response != null && response.ContainsKey("status") && response["status"].ToString() == "success")
                {
                    var unitList = fileApiService.ExtractUnitList(response);
                    if (unitList.Count > 0)
                    {
                        // 使用 GlobalParameters 更新企业选择状态，传入 false 表示这不是用户手动修改的
                        GlobalParameters.UpdateEnterpriseSelection(unitList, false);

                        // 如果用户没有手动修改过，则更新 selectedEnterprises
                        if (!GlobalParameters.EnterpriseSelectionModifiedByUser)
                        {
                            selectedEnterprises.Clear();
                            selectedEnterprises.AddRange(unitList);
                        }
                        else
                        {
                            // 如果用户已手动修改，则使用用户的选择
                            selectedEnterprises = EnterpriseCheckboxStateManager.GetCheckedItems();
                        }

                        ribbon.InvalidateControl("button8");
                    }

                    var chapterList = fileApiService.ExtractChapterList(response);
                    if (chapterList.Count > 0)
                    {
                        // 使用 GlobalParameters 更新菜单选择状态，传入 false 表示这不是用户手动修改的
                        GlobalParameters.UpdateMenuSelection(chapterList, false);

                        // 如果用户没有手动修改过，则更新 selectedMenu
                        if (!GlobalParameters.MenuSelectionModifiedByUser)
                        {
                            selectedMenu.Clear();
                            selectedMenu.AddRange(chapterList);
                        }
                        else
                        {
                            // 如果用户已手动修改，则使用用户的选择
                            selectedMenu = MenuCheckboxStateManager.GetCheckedItems();
                        }

                        ribbon.InvalidateControl("button9");
                    }

                    await UpdateUploadedFilesAsync();          
                }

                return response;
            }
            catch (Exception ex)
            {
                return new Dictionary<string, object> { { "status", "error" }, { "message", ex.Message } };
            }
        }

        // 获取已上传文件列表
        public async Task<List<string>> GetUploadedFilesAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(loggedInUserName) || string.IsNullOrEmpty(selectedProjectName))
                {
                    return new List<string>();
                }

                // 确保 FileApiService 已初始化
                if (fileApiService == null)
                {
                    fileApiService = new FileApiService(loggedInUserName, selectedProjectName);
                }

                // 调用 FileApiService 的方法获取文件列表
                var files = await fileApiService.GetUploadedFilesAsync();

                // 更新本地缓存的文件列表
                uploadedFiles = new List<string>(files);

                // 更新功能区控件
                UpdateButtonLabels();
                ribbon.Invalidate();

                return uploadedFiles;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取已上传文件列表时出错: {ex.Message}");
                return new List<string>();
            }
        }

        // 获取已上传文件列表（同步版本，供 GenerateForm 调用）
        public List<string> GetUploadedFiles()
        {
            return new List<string>(uploadedFiles); // 返回副本以避免外部修改
        }

        private async Task NotifyGenerateFormToUpdateFileList()
        {
            if (currentGenerateForm != null && !currentGenerateForm.IsDisposed)
            {
                await currentGenerateForm.UpdateFileList(new List<string>(uploadedFiles));
            }
        }

        // 私有方法：检查 FileApiService 初始化
        private bool EnsureFileApiServiceInitialized()
        {
            if (fileApiService == null)
            {
                MessageBox.Show("请输入正确的可研报告名称！格式为：项目类（项目名称）");
                return false;
            }
            return true;
        }

        // 获取上传文件并更新按钮标签
        private async Task UpdateUploadedFilesAsync()
        {
            try
            {
                if (!EnsureFileApiServiceInitialized()) return;

                // 获取上传文件
                var files = await fileApiService.GetUploadedFilesAsync();
                if (files != null)
                {
                    uploadedFiles = new List<string>(files); // 创建新的列表实例

                    UpdateButtonLabels();
                    ribbon.Invalidate();

                    // 通知 GenerateForm 更新文件列表
                    await NotifyGenerateFormToUpdateFileList();
                }
            }
            catch (Exception ex)
            {
                // 不要在这里显示错误消息，因为这是一个后台操作
                System.Diagnostics.Debug.WriteLine($"获取上传文件时发生错误: {ex.Message}");
            }
        }

        // 更新按钮标签的逻辑
        private void UpdateButtonLabels()
        {
            // 更新其他 button14~button16 和 button24~button28 的标签
            for (int i = 12; i <= 16; i++)
            {
                string buttonId = $"button{i}";
                SetButtonLabel(buttonId, uploadedFiles.ElementAtOrDefault(i - 12)); 
            }

            for (int i = 24; i <= 28; i++)
            {
                string buttonId = $"button{i}";
                SetButtonLabel(buttonId, uploadedFiles.ElementAtOrDefault(i - 19)); 
            }
        }

        // 根据按钮ID设置标签
        private void SetButtonLabel(string buttonId, string fileName)
        {
            // 在这里更新按钮的标签
            // 由于 Ribbon 控件的限制，我们需要使用回调函数来获取标签
            ribbon.InvalidateControl(buttonId);
        }

        private int GetButtonIndexFromId(string buttonId)
        {
            if (buttonId.StartsWith("button"))
            {
                string numericPart = buttonId.Substring(6);
                if (int.TryParse(numericPart, out int buttonNumber))
                {
                    switch (buttonNumber)
                    {
                        case 12: return 0;
                        case 13: return 1;
                        case 14: return 2;
                        case 15: return 3;
                        case 16: return 4;
                        case 24: return 5;
                        case 25: return 6;
                        case 26: return 7;
                        case 27: return 8;
                        case 28: return 9;
                        default: return -1;
                    }
                }
            }
            return -1;
        }

        // 添加强制刷新路径设置的方法，供设置界面调用
        public static void RefreshPathSettings()
        {
            try
            {
                Console.WriteLine("强制刷新路径设置...");

                // 清除缓存的路径
                string oldPath = defaultSavePath;
                defaultSavePath = null;

                // 重新加载设置
                LoadSettings();

                Console.WriteLine($"路径设置已刷新: {oldPath} -> {defaultSavePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刷新路径设置时出错: {ex.Message}");
            }
        }

        // 添加更新保存路径的方法，供设置界面调用
        public static void UpdateSavePath(string newPath)
        {
            try
            {
                if (string.IsNullOrEmpty(newPath))
                {
                    Console.WriteLine("新路径为空，忽略更新");
                    return;
                }

                Console.WriteLine($"更新保存路径: {defaultSavePath} -> {newPath}");

                // 确保新路径存在
                if (!Directory.Exists(newPath))
                {
                    Directory.CreateDirectory(newPath);
                }

                // 确保Style文件夹存在
                EnsureStyleFolderExists(newPath);

                // 确保Images文件夹隐藏（如果存在的话）
                EnsureImagesFolderHidden(newPath);

                // 更新设置文件
                var settings = new
                {
                    FilePath = newPath,
                    ModelSelection = "auto"
                };

                string settingsJson = JsonConvert.SerializeObject(settings, Formatting.Indented);
                File.WriteAllText(SETTINGS_FILE_PATH, settingsJson);

                // 更新缓存
                defaultSavePath = newPath;

                Console.WriteLine($"保存路径已更新并保存到设置文件: {newPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新保存路径时出错: {ex.Message}");
            }
        }

        #endregion

        #region comboBox处理
        private void LoadCustomItems()
        {
            string filePath = GetCustomItemsFilePath();
            if (File.Exists(filePath))
            {
                try
                {
                    XDocument doc = XDocument.Load(filePath);
                    customItems = doc.Root.Elements("Item").Select(e => e.Value).ToList();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载自定义项目时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private string GetCustomItemsFilePath()
        {
            string assemblyLocation = Assembly.GetExecutingAssembly().Location;
            string assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
            return Path.Combine(assemblyDirectory, CustomItemsFileName);
        }

        #endregion

        #region 帮助器

        private static string GetResourceText(string resourceName)
        {
            Assembly asm = Assembly.GetExecutingAssembly();
            string[] resourceNames = asm.GetManifestResourceNames();
            for (int i = 0; i < resourceNames.Length; ++i)
            {
                if (string.Compare(resourceName, resourceNames[i], StringComparison.OrdinalIgnoreCase) == 0)
                {
                    using (StreamReader resourceReader = new StreamReader(asm.GetManifestResourceStream(resourceNames[i])))
                    {
                        if (resourceReader != null)
                        {
                            return resourceReader.ReadToEnd();
                        }
                    }
                }
            }
            return null;
        }

        #endregion

        #region Excel表格复制到Word方法

        /// <summary>
        /// 从生成的Excel文件中复制指定工作表的有效区域到Word文档
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="sheetName">Excel工作表名称</param>
        /// <param name="sectionName">Word中的章节名称，用于错误提示</param>
        private async Task InsertExcelTableToWord(Word.Document doc, string sheetName, string sectionName)
        {
            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                // 查找最近生成的Excel文件
                string excelPath = FindLatestGeneratedExcelFile();
                if (string.IsNullOrEmpty(excelPath) || !File.Exists(excelPath))
                {
                    MessageBox.Show($"未找到生成的Excel文件，无法插入{sectionName}表格。", "警告",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 打开Excel应用程序
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.DisplayAlerts = false;

                // 打开Excel文件
                workbook = excelApp.Workbooks.Open(excelPath);

                // 查找指定的工作表
                Excel.Worksheet worksheet = null;
                foreach (Excel.Worksheet sheet in workbook.Worksheets)
                {
                    if (sheet.Name == sheetName)
                    {
                        worksheet = sheet;
                        break;
                    }
                }

                if (worksheet == null)
                {
                    MessageBox.Show($"在Excel文件中未找到工作表：{sheetName}", "警告",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 获取有效数据区域
                Excel.Range usedRange = GetEffectiveDataRange(worksheet, sheetName);
                if (usedRange == null)
                {
                    MessageBox.Show($"工作表 {sheetName} 中没有有效数据", "警告",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 创建一个临时工作表来处理数据
                Excel.Worksheet tempSheet = workbook.Worksheets.Add();

                try
                {
                    // 复制原始数据到临时工作表（保持格式，包括合并单元格）
                    usedRange.Copy();
                    tempSheet.Range["A1"].PasteSpecial(Excel.XlPasteType.xlPasteAll);
                    excelApp.CutCopyMode = 0;

                    // 等待粘贴完成，支持取消
                    await DelayWithCancellationCheck(200);

                    // 将公式转换为值（保持格式）
                    Excel.Range tempUsedRange = tempSheet.UsedRange;
                    if (tempUsedRange != null)
                    {
                        // 复制数据
                        tempUsedRange.Copy();
                        // 粘贴为值，但保持格式
                        tempUsedRange.PasteSpecial(Excel.XlPasteType.xlPasteValues);
                        excelApp.CutCopyMode = 0;

                        // 等待处理完成，支持取消
                        await DelayWithCancellationCheck(100);

                        // 最终复制处理后的数据
                        tempUsedRange.Copy();

                        // 确保复制成功，支持取消
                        int retryCount = 0;
                        while (excelApp.CutCopyMode == 0 && retryCount < 3)
                        {
                            // 检查是否请求取消
                            if (circleProgree != null && circleProgree.IsCancellationRequested())
                            {
                                return; // 如果请求取消，直接返回
                            }

                            await DelayWithCancellationCheck(100);
                            tempUsedRange.Copy();
                            retryCount++;
                        }

                        if (excelApp.CutCopyMode != 0)
                        {
                            // 在Word中粘贴
                            Word.Range insertPoint = doc.Paragraphs.Last.Range;
                            insertPoint.Collapse(Word.WdCollapseDirection.wdCollapseEnd);

                            bool pasteSuccess = false;

                            try
                            {
                                // 尝试直接粘贴
                                insertPoint.Paste();
                                pasteSuccess = true;
                                System.Diagnostics.Debug.WriteLine("直接粘贴成功");
                            }
                            catch (COMException comEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"直接粘贴失败: {comEx.Message}");

                                try
                                {
                                    // 尝试选择性粘贴为RTF
                                    insertPoint.PasteSpecial(DataType: Word.WdPasteDataType.wdPasteRTF);
                                    pasteSuccess = true;
                                    System.Diagnostics.Debug.WriteLine("RTF粘贴成功");
                                }
                                catch (COMException rtfEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"RTF粘贴失败: {rtfEx.Message}");

                                    try
                                    {
                                        // 最后尝试粘贴为HTML
                                        insertPoint.PasteSpecial(DataType: Word.WdPasteDataType.wdPasteHTML);
                                        pasteSuccess = true;
                                        System.Diagnostics.Debug.WriteLine("HTML粘贴成功");
                                    }
                                    catch (COMException htmlEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"HTML粘贴失败: {htmlEx.Message}");
                                        throw new Exception($"所有粘贴方法都失败了: {htmlEx.Message}");
                                    }
                                }
                            }

                            if (pasteSuccess)
                            {
                                // 清除剪贴板
                                excelApp.CutCopyMode = 0;

                                // 为粘贴的表格添加边框
                                await AddTableBorders(doc, insertPoint);
                            }
                        }
                        else
                        {
                            throw new Exception("无法将数据复制到剪贴板");
                        }
                    }
                    else
                    {
                        throw new Exception("临时工作表中没有数据");
                    }
                }
                finally
                {
                    // 删除临时工作表
                    try
                    {
                        excelApp.DisplayAlerts = false;
                        tempSheet.Delete();
                        excelApp.DisplayAlerts = true;
                    }
                    catch (Exception deleteEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除临时工作表时出错: {deleteEx.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"成功从Excel工作表 {sheetName} 复制数据到Word文档");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"从Excel复制{sectionName}数据时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"Excel复制错误: {ex.Message}\n{ex.StackTrace}");
            }
            finally
            {
                // 清理资源
                try
                {
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                }
                catch (Exception cleanupEx)
                {
                    System.Diagnostics.Debug.WriteLine($"清理Excel资源时出错: {cleanupEx.Message}");
                }
            }
        }

        /// <summary>
        /// 根据工作表名称获取有效数据区域
        /// </summary>
        /// <param name="worksheet">Excel工作表</param>
        /// <param name="sheetName">工作表名称</param>
        /// <returns>有效数据区域</returns>
        private Excel.Range GetEffectiveDataRange(Excel.Worksheet worksheet, string sheetName)
        {
            try
            {
                if (sheetName == "表一（项目估算汇总表）")
                {
                    // 表一的有效区域：从第1行开始（包含表头）到有数据的最后一行，限制为12列
                    Excel.Range usedRange = worksheet.UsedRange;
                    if (usedRange == null) return null;

                    int startRow = 1; // 从第1行开始，包含表头
                    int lastRow = 15;
                    int lastCol = Math.Min(usedRange.Columns.Count, 12); // 表一限制为12列

                    // 确保有数据
                    if (lastRow < startRow) return null;

                    System.Diagnostics.Debug.WriteLine($"表一数据区域: {startRow}行到{lastRow}行, {lastCol}列（限制为12列）");
                    return worksheet.Range[worksheet.Cells[startRow, 1], worksheet.Cells[lastRow, lastCol]];
                }
                else if (sheetName == "表五（分阶段投资估算表）")
                {
                    // 表五的有效区域：从第1行开始（包含表头）到有数据的最后一行，限制为10列
                    Excel.Range usedRange = worksheet.UsedRange;
                    if (usedRange == null) return null;

                    int startRow = 1; // 从第1行开始，包含表头
                    int lastRow = 15;
                    int lastCol = Math.Min(usedRange.Columns.Count, 10); // 表五限制为10列

                    // 确保有数据
                    if (lastRow < startRow) return null;

                    System.Diagnostics.Debug.WriteLine($"表五数据区域: {startRow}行到{lastRow}行, {lastCol}列（限制为10列）");
                    return worksheet.Range[worksheet.Cells[startRow, 1], worksheet.Cells[lastRow, lastCol]];
                }
                else
                {
                    // 其他工作表使用默认的UsedRange
                    return worksheet.UsedRange;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取有效数据区域时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查找最近生成的Excel文件
        /// </summary>
        /// <returns>Excel文件路径</returns>
        private string FindLatestGeneratedExcelFile()
        {
            try
            {
                // 优先使用当前生成的Excel文件路径
                if (!string.IsNullOrEmpty(currentGeneratedExcelPath) && File.Exists(currentGeneratedExcelPath))
                {
                    System.Diagnostics.Debug.WriteLine($"使用当前生成的Excel文件: {currentGeneratedExcelPath}");
                    return currentGeneratedExcelPath;
                }

                string savePath = GetDefaultSavePath();
                if (!Directory.Exists(savePath))
                {
                    return null;
                }

                // 首先查找项目文件夹中的Excel文件
                var projectFolders = Directory.GetDirectories(savePath)
                    .Where(d => Path.GetFileName(d).Contains(CleanFileName(selectedProjectName)))
                    .OrderByDescending(d => Directory.GetCreationTime(d))
                    .ToArray();

                foreach (string projectFolder in projectFolders)
                {
                    var excelFiles = Directory.GetFiles(projectFolder, "*.xls")
                        .Where(f => Path.GetFileName(f).Contains(selectedProjectName) &&
                                   Path.GetFileName(f).Contains("建设项目可行性研究投资估算书"))
                        .OrderByDescending(f => File.GetCreationTime(f))
                        .ToArray();

                    if (excelFiles.Length > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"在项目文件夹中找到Excel文件: {excelFiles[0]}");
                        return excelFiles[0];
                    }
                }

                // 如果项目文件夹中没有找到，再查找基础路径中的Excel文件（兼容旧版本）
                var legacyExcelFiles = Directory.GetFiles(savePath, "*.xls")
                    .Where(f => Path.GetFileName(f).Contains(selectedProjectName) &&
                               Path.GetFileName(f).Contains("建设项目可行性研究投资估算书"))
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .ToArray();

                if (legacyExcelFiles.Length > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"在基础路径中找到Excel文件: {legacyExcelFiles[0]}");
                    return legacyExcelFiles[0];
                }

                System.Diagnostics.Debug.WriteLine("未找到任何Excel文件");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找Excel文件时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 为Word文档中最后一个表格添加边框
        /// </summary>
        /// <param name="doc">Word文档</param>
        /// <param name="insertPoint">插入点</param>
        private async Task AddTableBorders(Word.Document doc, Word.Range insertPoint)
        {
            try
            {
                // 等待一下确保表格完全插入，支持取消
                await DelayWithCancellationCheck(200);

                // 方法1：查找文档中的最后一个表格
                if (doc.Tables.Count > 0)
                {
                    Word.Table lastTable = doc.Tables[doc.Tables.Count];
                    SetTableBorders(lastTable);
                    System.Diagnostics.Debug.WriteLine($"成功为文档中最后一个表格添加边框，表格行数: {lastTable.Rows.Count}");
                    return;
                }

                // 方法2：在插入点附近查找表格
                Word.Range searchRange = insertPoint.Duplicate;
                searchRange.Start = Math.Max(0, insertPoint.Start - 2000);
                searchRange.End = Math.Min(doc.Content.End, insertPoint.End + 1000);

                if (searchRange.Tables.Count > 0)
                {
                    Word.Table nearbyTable = searchRange.Tables[searchRange.Tables.Count];
                    SetTableBorders(nearbyTable);
                    System.Diagnostics.Debug.WriteLine($"在插入点附近找到表格并添加边框，表格行数: {nearbyTable.Rows.Count}");
                    return;
                }

                // 方法3：尝试选择插入点后的内容查找表格
                try
                {
                    Word.Range afterInsert = doc.Range(insertPoint.End, Math.Min(doc.Content.End, insertPoint.End + 5000));
                    if (afterInsert.Tables.Count > 0)
                    {
                        Word.Table foundTable = afterInsert.Tables[1];
                        SetTableBorders(foundTable);
                        System.Diagnostics.Debug.WriteLine($"在插入点后找到表格并添加边框，表格行数: {foundTable.Rows.Count}");
                        return;
                    }
                }
                catch (Exception searchEx)
                {
                    System.Diagnostics.Debug.WriteLine($"搜索插入点后表格时出错: {searchEx.Message}");
                }

                System.Diagnostics.Debug.WriteLine("未找到需要添加边框的表格");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加表格边框时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置表格的边框样式
        /// </summary>
        /// <param name="table">Word表格</param>
        private void SetTableBorders(Word.Table table)
        {
            try
            {
                // 设置表格边框样式
                table.Borders.Enable = 1; // 启用边框

                // 定义边框样式参数
                Word.WdLineStyle lineStyle = Word.WdLineStyle.wdLineStyleSingle;
                Word.WdLineWidth lineWidth = Word.WdLineWidth.wdLineWidth075pt; // 稍微粗一点的线条
                Word.WdColor borderColor = Word.WdColor.wdColorBlack;

                // 设置所有边框
                foreach (Word.WdBorderType borderType in new Word.WdBorderType[]
                {
                    Word.WdBorderType.wdBorderTop,
                    Word.WdBorderType.wdBorderBottom,
                    Word.WdBorderType.wdBorderLeft,
                    Word.WdBorderType.wdBorderRight,
                    Word.WdBorderType.wdBorderHorizontal,
                    Word.WdBorderType.wdBorderVertical
                })
                {
                    try
                    {
                        table.Borders[borderType].LineStyle = lineStyle;
                        table.Borders[borderType].LineWidth = lineWidth;
                        table.Borders[borderType].Color = borderColor;
                    }
                    catch (Exception borderEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置边框类型 {borderType} 时出错: {borderEx.Message}");
                    }
                }

                // 设置表格样式和对齐
                table.AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitContent);
                table.Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;

                // 设置表格居中
                table.Rows.Alignment = Word.WdRowAlignment.wdAlignRowCenter;

                // 为表头设置特殊格式（处理合并单元格的情况）
                if (table.Rows.Count > 0)
                {
                    try
                    {
                        // 由于有合并单元格，不能直接访问行，改为访问单元格范围
                        Word.Range headerRange = table.Cell(1, 1).Range;
                        headerRange.End = table.Rows[Math.Min(3, table.Rows.Count)].Range.End; // 前3行作为表头区域

                        // 设置表头文字格式
                        headerRange.Font.Bold = 1;
                        headerRange.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;

                        // 为前几行的所有单元格设置背景色（避免合并单元格问题）
                        int headerRowCount = Math.Min(3, table.Rows.Count); // 前3行作为表头
                        for (int rowIndex = 1; rowIndex <= headerRowCount; rowIndex++)
                        {
                            try
                            {
                                for (int colIndex = 1; colIndex <= table.Columns.Count; colIndex++)
                                {
                                    try
                                    {
                                        Word.Cell cell = table.Cell(rowIndex, colIndex);
                                        cell.Shading.BackgroundPatternColor = Word.WdColor.wdColorGray15;
                                    }
                                    catch (Exception cellEx)
                                    {
                                        // 某些单元格可能因为合并而无法访问，跳过
                                        System.Diagnostics.Debug.WriteLine($"设置单元格({rowIndex},{colIndex})背景色时跳过: {cellEx.Message}");
                                    }
                                }
                            }
                            catch (Exception rowEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"设置第{rowIndex}行背景色时出错: {rowEx.Message}");
                            }
                        }
                    }
                    catch (Exception headerEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"设置表头样式时出错: {headerEx.Message}");

                        // 如果表头设置失败，尝试简单的全表格式设置
                        try
                        {
                            table.Range.Font.Name = "宋体";
                            table.Range.Font.Size = 10;
                            System.Diagnostics.Debug.WriteLine("应用了基本的表格字体格式");
                        }
                        catch (Exception basicEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置基本表格格式时出错: {basicEx.Message}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"表格边框设置完成，表格大小: {table.Rows.Count}行 x {table.Columns.Count}列");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置表格边框样式时出错: {ex.Message}");
            }
        }

        // 支持取消的延迟方法
        private async Task DelayWithCancellationCheck(int milliseconds)
        {
            var checkInterval = Math.Min(100, milliseconds); // 每100ms或更短时间检查一次取消状态
            var remainingTime = milliseconds;

            while (remainingTime > 0)
            {
                if (circleProgree != null && circleProgree.IsCancellationRequested())
                {
                    return; // 如果请求取消，立即返回
                }

                var delayTime = Math.Min(checkInterval, remainingTime);
                await Task.Delay(delayTime);
                remainingTime -= delayTime;
            }
        }

        #endregion
    }
}
