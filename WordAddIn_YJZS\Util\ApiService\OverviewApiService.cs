﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System;
using System.Linq;
using System.Text;

namespace WordAddIn_YJZS
{
    public class OverviewApiService
    {
        private readonly HttpClient client;

        public OverviewApiService()
        {
            client = HttpClientManager.Client;
        }

        private void ShowMessage(string message, string caption = "提示",
        MessageBoxButtons buttons = MessageBoxButtons.OK,
        MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        // 通用的异步POST请求方法
        private async Task<List<ContentItem>> PostAndHandleAsync(string url, string username, string project_name, string operationType)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowMessage("用户名不能为空");
                    return new List<ContentItem>();
                }
                if (string.IsNullOrWhiteSpace(project_name))
                {
                    ShowMessage("项目名称不能为空");
                    return new List<ContentItem>();
                }

                var requestBody = new { username, project_name };
                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    var responseString = await response.Content.ReadAsStringAsync();
                    //ShowMessage($"服务器响应: {responseString}");

                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ContentItem>>>(responseString);
                            if (apiResponse?.data != null)
                            {
                                // 处理图片下载
                                foreach (var item in apiResponse.data)
                                {
                                    // 在处理图片的部分
                                    if (item.style == "-1")
                                    {
                                        Console.WriteLine($"发现图片类型内容: {item.content}"); // 添加日志
                                        string imagePath = await item.DownloadImageAsync();
                                        if (!string.IsNullOrEmpty(imagePath))
                                        {
                                            Console.WriteLine($"图片已下载到: {imagePath}"); // 添加日志
                                            item.content = imagePath;
                                        }
                                        else
                                        {
                                            Console.WriteLine("图片下载失败"); // 添加日志
                                        }
                                    }
                                    // 处理表格数据
                                    else if (item.style == "-3" && item.content != null)
                                    {
                                        try
                                        {
                                            // 如果 content 已经是 JArray
                                            if (item.content is JArray jArray)
                                            {
                                                var tableData = jArray.Select(row =>
                                                    ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                                                ).ToList();
                                                item.content = tableData;
                                            }
                                            // 如果 content 是字符串
                                            else if (item.content is string strContent)
                                            {
                                                try
                                                {
                                                    // 先尝试直接解析为二维字符串数组
                                                    var tableData = JsonConvert.DeserializeObject<List<List<string>>>(strContent);
                                                    if (tableData != null)
                                                    {
                                                        item.content = tableData;
                                                    }
                                                }
                                                catch
                                                {
                                                    // 如果解析失败，尝试解析为 JArray
                                                    var array = JsonConvert.DeserializeObject<JArray>(strContent);
                                                    if (array != null)
                                                    {
                                                        var tableData = array.Select(row =>
                                                            ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                                                        ).ToList();
                                                        item.content = tableData;
                                                    }
                                                }
                                            }
                                            // 如果 content 是其他类型，尝试转换为字符串后处理
                                            else
                                            {
                                                string jsonStr = JsonConvert.SerializeObject(item.content);
                                                try
                                                {
                                                    var tableData = JsonConvert.DeserializeObject<List<List<string>>>(jsonStr);
                                                    if (tableData != null)
                                                    {
                                                        item.content = tableData;
                                                    }
                                                }
                                                catch
                                                {
                                                    Console.WriteLine($"无法将内容转换为表格数据: {jsonStr}");
                                                    item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"处理表格数据时出错: {ex.Message}");
                                            item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                                        }
                                    }
                                }
                                return apiResponse.data;
                            }
                        }
                        catch (JsonException)
                        {
                            // 如果解析失败，返回原始响应作为内容
                            return new List<ContentItem> { 
                                new ContentItem{style = "0",content = $"获取{operationType}时发生错误，请稍后重试。"},
                                new ContentItem { style = "-5", content = responseString } 
                            };
                        }
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                    {
                        //ShowErrorMessage($"服务器内部错误: {responseString}");
                        return new List<ContentItem>
                        {
                            new ContentItem
                            {
                                style = "0",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            },
                            new ContentItem
                            {
                                style = "-5",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            }
                        };
                    }

                    //ShowErrorMessage($"请求失败: {response.StatusCode} - {responseString}");
                    return new List<ContentItem>();
                }
                catch (JsonException)
                {
                    //ShowErrorMessage($"解析响应数据时出错: {ex.Message}");
                    return new List<ContentItem>();
                }
                catch (HttpRequestException)
                {
                    //ShowErrorMessage($"网络请求错误: {ex.Message}");
                    return new List<ContentItem>();
                }
            }
            catch (Exception)
            {
                //ShowErrorMessage($"获取{operationType}时发生未知错误: {ex.Message}");
                return new List<ContentItem>();
            }
        }

        // 项目背景
        public async Task<List<ContentItem>> BackgroundAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BackgroundUrl, username, project_name, "项目背景");
        }

        // 项目依据
        public async Task<List<ContentItem>> BasisAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BasisUrl, username, project_name, "项目依据");
        }

        // 项目目标
        public async Task<List<ContentItem>> GoalsAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.GoalsUrl, username, project_name, "项目目标");
        }

        // 业务范围
        public async Task<List<ContentItem>> BusinessAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BusinessScopeUrl, username, project_name, "业务范围");
        }

        // 开发范围
        public async Task<List<ContentItem>> DevelopScopeAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.DevelopmentScopeUrl, username, project_name, "开发范围");
        }

        // 保持原有的特殊处理方法
        public async Task<string> ApplyScopeAsync(List<string> units)
        {
            if (units == null || units.Count == 0)
            {
                throw new ArgumentException("应用范围不能为空！");
            }

            var requestBody = new { units };
            return await OriginalPostJsonAsync<string>(AllApi.ApplicationScopeUrl, requestBody, "application_scope");
        }

        public async Task<string> InvestmentIncAsync(string username,string project_name,List<string> units)
        {
            if (units == null || units.Count == 0)
            {
                throw new ArgumentException("投资建设单位列表不能为空！");
            }

            var requestBody = new { username, project_name, units };
            return await OriginalPostJsonAsync<string>(AllApi.InvestmentIncUrl, requestBody, "investment_unit");
        }

        private async Task<T> OriginalPostJsonAsync<T>(string url, object data, string dataField)
        {
            var jsonString = JsonConvert.SerializeObject(data);
            using (var jsonContent = new StringContent(jsonString))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                //ShowMessage($"完整的请求 URL: {url}");
                //ShowMessage($"发送请求体: {jsonString}");

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    //ShowMessage($"收到响应: 状态码 {response.StatusCode}\n响应内容: {responseContent}");
                    return await OriginalHandleResponseAsync<T>(response, dataField);
                }
                catch (Exception ex)
                {
                    ShowMessage($"请求发生异常: {ex.Message}");
                    throw;
                }
            }
        }

        //处理响应
        private async Task<T> OriginalHandleResponseAsync<T>(HttpResponseMessage response, string dataField)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            //ShowMessage($"原始响应内容:\n{responseString}");

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<JObject>>(responseString);
                    if (result.data != null && result.data.TryGetValue(dataField, out JToken value))
                    {
                        return value.ToObject<T>();
                    }
                    throw new Exception($"响应中没有找到 {dataField} 字段");
                }
                catch (JsonException ex)
                {
                    ShowMessage($"JSON 解析错误: {ex.Message}");
                    throw;
                }
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
                var errorResponse = JsonConvert.DeserializeObject<ValidationErrorResponse>(responseString);
                throw new Exception($"BadRequest: {errorResponse.Detail}");
            }
            else if ((int)response.StatusCode == 422)
            {
                var validationErrors = JsonConvert.DeserializeObject<ValidationErrorResponse>(responseString);
                throw new Exception($"验证错误: {string.Join(", ", validationErrors.Detail.Select(e => e.Msg))}");
            }
            else
            {
                throw new Exception($"请求失败，状态码 {response.StatusCode}: {responseString}");
            }
        }
    }
}