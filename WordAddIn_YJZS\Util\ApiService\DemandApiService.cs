﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System;
using WordAddIn_YJZS;
using Newtonsoft.Json.Linq;
using System.Linq;

namespace WordAddIn_YJZS
{
    public class DemandApiService
    {
        private readonly HttpClient client;

        public DemandApiService()
        {
            client = HttpClientManager.Client;
        }

        private void ShowMessage(string message, string caption = "提示",
        MessageBoxButtons buttons = MessageBoxButtons.OK,
        MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        // 通用的异步POST请求方法
        private async Task<List<ContentItem>> PostAndHandleBaseAsync(
            string url,
            object requestBody,
            string operationType)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(url, jsonContent);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ContentItem>>>(responseString);
                        if (apiResponse?.data != null)
                        {
                            // 处理图片下载和表格数据
                            foreach (var item in apiResponse.data)
                            {
                                if (item.style == "-1")
                                {
                                    Console.WriteLine($"发现图片类型内容: {item.content}");
                                    string imagePath = await item.DownloadImageAsync();
                                    if (!string.IsNullOrEmpty(imagePath))
                                    {
                                        Console.WriteLine($"图片已下载到: {imagePath}");
                                        item.content = imagePath;
                                    }
                                    else
                                    {
                                        Console.WriteLine("图片下载失败");
                                    }
                                }
                                else if (item.style == "-3" && item.content != null)
                                {
                                    ProcessTableData(item);
                                }
                            }
                            return apiResponse.data;
                        }
                    }
                    catch (JsonException)
                    {
                        return new List<ContentItem> { new ContentItem { style = "-5", content = responseString } };
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                {
                    //ShowErrorMessage($"服务器内部错误: {responseString}");
                    return new List<ContentItem>
                    {
                        new ContentItem
                            {
                                style = "0",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            },
                        new ContentItem
                        {
                            style = "-5",
                            content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                        }
                    };
                }

                //ShowErrorMessage($"请求失败: {response.StatusCode} - {responseString}");
                return new List<ContentItem>();
            }
            catch (JsonException)
            {
                //ShowErrorMessage($"解析响应数据时出错: {ex.Message}");
                return new List<ContentItem>();
            }
            catch (HttpRequestException)
            {
                //ShowErrorMessage($"网络请求错误: {ex.Message}");
                return new List<ContentItem>();
            }
            catch (Exception)
            {
                //ShowErrorMessage($"获取{operationType}时发生未知错误: {ex.Message}");
                return new List<ContentItem>();
            }
        }

        private async Task<List<ContentItem>> PostAndHandleAsync(
            string url,
            string username,
            string project_name,
            string operationType)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                ShowMessage("用户名不能为空");
                return new List<ContentItem>();
            }
            if (string.IsNullOrWhiteSpace(project_name))
            {
                ShowMessage("项目名称不能为空");
                return new List<ContentItem>();
            }

            var requestBody = new { username, project_name };
            return await PostAndHandleBaseAsync(url, requestBody, operationType);
        }

        private async Task<List<ContentItem>> PostAndHandleUserNumberAsync(
            string url,
            string username,
            string project_name,
            string operationType,
            double user_number)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                ShowMessage("用户名不能为空");
                return new List<ContentItem>();
            }
            if (string.IsNullOrWhiteSpace(project_name))
            {
                ShowMessage("项目名称不能为空");
                return new List<ContentItem>();
            }
            if (user_number == 0)
            {
                ShowMessage("额外参数不能为空");
                return new List<ContentItem>();
            }

            var requestBody = new { username, project_name, user_number };
            return await PostAndHandleBaseAsync(url, requestBody, operationType);
        }

        // 辅助方法：处理表格数据
        private void ProcessTableData(ContentItem item)
        {
            try
            {
                if (item.content is JArray jArray)
                {
                    var tableData = jArray.Select(row =>
                        ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                    ).ToList();
                    item.content = tableData;
                }
                else if (item.content is string strContent)
                {
                    try
                    {
                        var tableData = JsonConvert.DeserializeObject<List<List<string>>>(strContent);
                        if (tableData != null)
                        {
                            item.content = tableData;
                        }
                    }
                    catch
                    {
                        var array = JsonConvert.DeserializeObject<JArray>(strContent);
                        if (array != null)
                        {
                            var tableData = array.Select(row =>
                                ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                            ).ToList();
                            item.content = tableData;
                        }
                    }
                }
                else
                {
                    string jsonStr = JsonConvert.SerializeObject(item.content);
                    try
                    {
                        var tableData = JsonConvert.DeserializeObject<List<List<string>>>(jsonStr);
                        if (tableData != null)
                        {
                            item.content = tableData;
                        }
                    }
                    catch
                    {
                        Console.WriteLine($"无法将内容转换为表格数据: {jsonStr}");
                        item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理表格数据时出错: {ex.Message}");
                item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
            }
        }


        // 现状分析
        public async Task<List<ContentItem>> StatusAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.StatusUrl, username, project_name, "现状分析");
        }

        // 业务需求
        public async Task<List<ContentItem>> BusinessDemandAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BusinessDemandUrl, username, project_name, "业务需求");
        }

        // 功能需求
        public async Task<List<ContentItem>> FunctionDemandAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.FunctionDemandUrl, username, project_name, "功能需求");
        }

        // 性能需求
        public async Task<List<ContentItem>> PerformanceDemandAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.PerformanceDemandUrl, username, project_name, "性能需求");
        }

        // 业务集成需求
        public async Task<List<ContentItem>> BusinessIntegrationDemandAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BusinessIntegrationDemandUrl, username, project_name, "业务集成需求");
        }

        // 安全需求
        public async Task<List<ContentItem>> SecurityDemandAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.SecurityDemandUrl, username, project_name, "安全需求");
        }

        // 用户规模
        public async Task<List<ContentItem>> UserScaleAsync(string username, string project_name, double user_number)
        {
            return await PostAndHandleUserNumberAsync(AllApi.UserScaleUrl, username, project_name, "用户规模", user_number);
        }

        // 必要性结论
        public async Task<List<ContentItem>> NecessityAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.NecessityUrl, username, project_name, "必要性结论");
        }
    }
}

