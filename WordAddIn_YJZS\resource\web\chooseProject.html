<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .dashboard {
            display: flex;
            max-width: 900px;
            width: 100%;
            margin: 0px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            min-height: 650px;
            /* 增加表单高度 */
        }

        .main-content {
            flex-grow: 1;
            padding: 30px;
        }

        .card {
            position: relative;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
        }

        .blocks {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            min-height: 36px;
            margin-bottom: 15px;
            padding: 0 25px;
        }

        .titles {
            color: #4a90e2;
            width: 120px;
            font-size: 14px;
            flex-shrink: 0;
            margin-right: 25px;
        }

        .selects,
        .inputs {
            flex: 1;
            max-width: calc(100% - 195px);
            height: 36px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
            color: #333;
            background-color: #fff;
            outline: none;
            transition: all 0.3s;
            box-sizing: border-box;
        }

        .selects {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 36px;
        }

        .selects:hover,
        .inputs:hover {
            border-color: #4a90e2;
        }

        .selects:focus,
        .inputs:focus {
            border-color: #4a90e2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
        }

        .btn-submit {
            display: block;
            width: 200px;
            height: 36px;
            margin: 30px auto 0;
            background: #4a90e2;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-submit:hover {
            background: #357abd;
        }

        /* 选中样式 */
        .selected-item .selects,
        .selected-item .inputs {
            border-color: #4a90e2;
            background-color: #f8f9ff;
        }

        /* 下拉框禁用样式 */
        select:disabled,
        input:disabled {
            background-color: #f5f5f5;
            border-color: #e0e0e0;
            color: #999;
            cursor: not-allowed;
        }

        /* 占位符样式 */
        .selects option[value=""][disabled] {
            color: #999;
        }

        .inputs::placeholder {
            color: #999;
        }

        /* 单选框样式 */
        .radio-group {
            flex: 1;
            max-width: calc(100% - 195px);
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .radio-item input[type="radio"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .radio-item label {
            font-size: 14px;
            color: #333;
            cursor: pointer;
            user-select: none;
        }

        .radio-item:hover label {
            color: #4a90e2;
        }
    </style>
    <script type="text/javascript">
        const data = {
            "两大基础": {
                "数字基础设施": {
                    "“3+1+X”数字基础设施建设": [
                        "网级数据中心建设",
                        "海外数据中心建设",
                        "省级分布式数据中心升级改造",
                        "边缘数据中心升级改造",
                        "资源租赁"
                    ],
                    "算力建设": [
                        " 南网云（经营管理节点）IaaS及PaaS采购",
                        "南网云（调度节点）IaaS及PaaS采购",
                        "南网公有云IaaS及PaaS采购"
                    ],
                    "管": [
                        "局域网建设及升级改造",
                        "通信网络建设"
                    ],
                    "边": [
                        "边缘算力节点建设"
                    ],
                    "端": [
                        "终端设备采购",
                        "终端软件采购",
                        "传感及智能网关采购",
                        "计量表计采购"
                    ]
                },
                "数据资源体系": {
                    "数据资产顶层设计": [
                        "数据资产顶层设计"
                    ],
                    "数据资产治理": [
                        "数据资产治理"
                    ],
                    "数据资产运营": [
                        "数据资产运营"
                    ],
                    "数据流通": [
                        "数据流通建设"
                    ]
                }
            },
            "四位一体赋能": {
                "数字技术平台": {
                    "南网云平台": [
                        "南网云平台建设"
                    ],
                    "物联网平台": [
                        "物联网平台建设"
                    ],
                    "人工智能平台": [
                        "人工智能平台建设"
                    ],
                    "移动应用平台": [
                        "移动应用平台建设"
                    ],
                    "区块链平台": [
                        "区块链平台建设"
                    ],
                    "南网智瞰（电网GIS平台）": [
                        "南网智瞰（电网GIS平台）建设"
                    ],
                    "南网智瞰（个性化应用）": [
                        "南网智瞰（个性化应用）建设"
                    ],
                    "研发平台": [
                        "研发平台建设"
                    ],
                    "数字身份与访问管理平台": [
                        "数字身份与访问管理平台建设"
                    ],
                    "统一密码服务平台": [
                        "统一密码服务平台建设"
                    ],
                    "云化数据中心": [
                        "云化数据中心建设",
                        "自主空间",
                        "南网智搜"
                    ],
                    "北斗": [
                        "北斗运营服务应用建设"
                    ],
                    "技术平台推广": [
                        "技术平台推广应用"
                    ]
                },
                "企业级中台": {
                    "应用中台": [
                        "应用中台建设"
                    ],
                    "技术中台": [
                        "技术中台建设"
                    ],
                    "数据中台": [
                        "数据中台建设"
                    ],
                    "安全中台": [
                        "安全中台建设"
                    ],
                    "中台管理平台": [
                        "中台运营管理应用建设"
                    ],
                    "个性化中台建设及推广": [
                        "中台个性化建设及推广应用"
                    ]
                },
                "数字电网应用": {
                    "资产全生命（规划）类": [
                        "电网规划管理应用建设",
                        "线损数字化管控应用建设",
                        "新型电力系统示范应用建设",
                        "新型电力系统综合分析及展示建设"
                    ],
                    "资产全生命（建设运维）类": [
                        "电网建设应用建设",
                        "电网安全生产应用建设",
                        "生产技术支持系统建设",
                        "生产指挥系统建设"
                    ],
                    "能量全过程（电网运行）类": [
                        "云边融合的智能调度运行平台建设",
                        "调度运行平台（电网运行管理）建设",
                        "调度运行平台（电网运行管理）个性化应用建设"
                    ],
                    "能量全过程（电力交易）类": [
                        "数字量测应用建设",
                        "电力交易应用建设",
                        "电力需求响应应用建设"
                    ],
                    "个性化应用": [
                        "数字电网（个性化应用）建设"
                    ]
                },
                "数字服务": {
                    "服务全方位类": [
                        "数字服务运营应用建设",
                        "数字渠道建设",
                        "营销现场作业应用建设",
                        "数字营业应用建设",
                        "数字评价应用建设",
                        "产融服务生态建设"
                    ],
                    "个性化应用": [
                        "数字服务（个性化）建设"
                    ]
                },
                "数字运营": {
                    "战略发展类": [
                        "战略管理应用建设",
                        "管理体系管理应用建设",
                        "架构管理应用建设",
                        "政策研究应用建设",
                        "深化改革应用建设",
                        "节能环保应用建设",
                        "农电管理应用建设"
                    ],
                    "资产全生命（投资类）": [
                        "前期管理应用建设",
                        "投资计划应用建设"
                    ],
                    "资源集约化类": [
                        "财务管理应用建设",
                        "集团司库管理应用建设",
                        "运营监控应用建设",
                        "人资管理应用建设",
                        "创新管理应用建设",
                        "项目管理应用建设",
                        "数字化项目全过程管理应用建设",
                        "品牌管理应用建设"
                    ],
                    "基础支撑类": [
                        "指标体系管理应用建设",
                        "标准管理应用建设",
                        "质量管理应用建设",
                        "统计管理应用建设",
                        "供应链管理应用建设",
                        "工会管理应用建设",
                        "行政办公应用建设",
                        "后勤管理应用建设",
                        "档案管理应用建设",
                        "年金管理应用建设",
                        "法治应用建设",
                        "风控数智化管理应用建设",
                        "合同管理应用建设",
                        "数字审计应用建设",
                        "电网管理平台（外部门户）建设",
                        "即时通信应用建设"
                    ],
                    "党建与保障类": [
                        "数字党建应用建设",
                        "数字巡视巡察应用建设",
                        "监督执纪应用建设",
                        "国资监管系统建设"
                    ],
                    "业务多元化类": [
                        "通用类统建通用类应用推广",
                        "统推统建应用境外延伸",
                        "国际业务特色应用",
                        "数字金融服务平台",
                        "产业金融业务数字化建设",
                        "“乐学南网”建设",
                        "智库建设",
                        "数字孪生仿真平台",
                        "南方电网知识共享服务平台",
                        "智慧检测平台建设",
                        "数字电网智能算法库",
                        "网络安全靶场建设",
                        "融媒业务应用建设",
                        "网络信息情报态势感知应用建设",
                        "科技家园应用推广",
                        "资讯管理应用",
                        "共享服务支撑体系个性化应用建设"
                    ],
                    "运营管控类": [
                        "云景数字化运营管控平台公共应用建设",
                        "云景数字化运营管控平台管制业务个性化应用建设",
                        "云景数字化运营管控平台管制业务个性化应用建设"
                    ]
                },
                "数字产业": {
                    "绿色用能产业": [
                        "绿色用能产品研发及推广"
                    ],
                    "数据产品产业": [
                        "数据中心对外门户",
                        "能源数据产品",
                        "面向数字政府及行业机构应用"
                    ],
                    "技术装备产业": [
                        "数字电网智能技术及装备产品研发"
                    ],
                    "产业协同": [
                        "规划协同应用",
                        "设计协同应用",
                        "建设产业生态应用",
                        "智能制造应用",
                        "开源社区建设"
                    ]
                }
            },
            "两项能力": {
                "网络安全运行保护": {
                    "保障单元": [
                        "服务管理",
                        "数字化运行服务平台维护及技术服务",
                        "IT资产维护",
                        "网络安全运维",
                        "运维工器具建设",
                        "应用系统版本发布测试",
                        "运维服务",
                        "资源管理",
                        "数字化安全运行调控中心建设",
                        "数字化安全运行调控中心技术服务",
                        "网络安全运行技术服务",
                        "网络安全运行技术服务"
                    ],
                    "技术单元": [
                        "网络安全技术防护",
                        "网络安全技术防护",
                        "互联网应用安全监测系统建设",
                        "统一威胁情报平台建设",
                        "物联网安全建设",
                        "数据安全管理",
                        "桌面级移动终端安全防护",
                        "网络安全弹性系统",
                        "各安全域服务平台及安全能力建设",
                        "商业秘密安全保护平台建设",
                        "电力监控安全态势感知平台建设及应用",
                        "电力监控系统商用密码保障系统",
                        "数字化运行服务平台建设",
                        "运维工具采购",
                        "保底系统建设",
                        "应用级灾备建设",
                        "南网云平台灾备建设",
                        "云化数据中心灾备建设",
                        "中台灾备建设",
                        "灾备建设",
                        "云盾平台建设"
                    ],
                    "监督单元": [
                        "网络安全运行监督"
                    ]
                },
                "数字技术创新": {
                    "技术创新": [
                        "前瞻技术研究",
                        "智能产品研发",
                        "重大工程",
                        "自主创新",
                        "自主可控改造",
                        "技术创新发展新模式"
                    ]
                }
            },
            "1个生态": {
                "数字生态": {
                    "数字生态": [
                        "内部生态建设",
                        "国资行业云建设",
                        "能源行业生态建设",
                        "数字化转型机制研究",
                        "数字化转型宣传"
                    ]
                }
            },
            "数字化管理体系": {
                "数字化管理体系": {
                    "数字化管理体系": [
                        "数字化管理体系机制完善"
                    ]
                },
                "数字化规划管理": {
                    "规划管理": [
                        "数字化规划编制与修编",
                        "数字化规划实施计划编制与修编"
                    ],
                    "架构管理": [
                        "企业架构资产更新及管控",
                        "企业架构资产更新及管控"
                    ],
                    "技术管理": [
                        "数字化标准体系建设",
                        "技术管控",
                        "技术监督",
                        "数据技术专题研究及示范应用",
                        "数据技术专题研究及示范应用"
                    ],
                    "前期管理": [
                        "信息化项目前期专项",
                        "数字化项目前期专项"
                    ],
                    "投资后评价": [
                        "数字化项目后评价",
                        "数字化年度投资评价"
                    ],
                    "考核评价": [
                        "数字化创新发展指数考核"
                    ],
                    "评测": [
                        "数字化水平评价",
                        "数字化测评"
                    ]
                },
                "建设管理": {
                    "质量管理": [
                        "全过程质量管控体系建设",
                        "实验室建设",
                        "自主可控适配测试",
                        "统一研发体系建设",
                        "数字化测评体系建设"
                    ],
                    "数字化宣传与人才培养": [
                        "数字化素养培训"
                    ]
                }
            }
        }

        function changeSelect1() {
            const select1 = document.querySelector('.select-1');
            const select1Text = select1.options[select1.selectedIndex].text;

            const select2 = document.querySelector('.select-2');
            select2.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

            for (let key in data[select1Text]) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = key;
                select2.appendChild(option);
            }

            const select3 = document.querySelector('.select-3');
            select3.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

            const select4 = document.querySelector('.select-4');
            select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';
        }

        function changeSelect2() {
            const select1 = document.querySelector('.select-1');
            const select1Text = select1.options[select1.selectedIndex].text;

            const select2 = document.querySelector('.select-2');
            const select2Text = select2.options[select2.selectedIndex].text;

            const select3 = document.querySelector('.select-3');
            select3.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

            for (let key in data[select1Text][select2Text]) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = key;
                select3.appendChild(option);
            }

            const select4 = document.querySelector('.select-4');
            select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';
        }

        function changeSelect3() {
            const select1 = document.querySelector('.select-1');
            const select1Text = select1.options[select1.selectedIndex].text;

            const select2 = document.querySelector('.select-2');
            const select2Text = select2.options[select2.selectedIndex].text;

            const select3 = document.querySelector('.select-3');
            const select3Text = select3.options[select3.selectedIndex].text;

            const select4 = document.querySelector('.select-4');
            select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

            for (let i = 0; i < data[select1Text][select2Text][select3Text].length; i++) {
                const option = document.createElement('option');
                option.value = data[select1Text][select2Text][select3Text][i];
                option.textContent = data[select1Text][select2Text][select3Text][i];
                select4.appendChild(option);
            }
        }

        function setProjectName(name) {
            const input = document.querySelector('.input-1');
            if (input) {
                input.value = name;
            }
        }

        function submit() {
            const select1 = document.querySelector('.select-1');
            const select2 = document.querySelector('.select-2');
            const select3 = document.querySelector('.select-3');
            const select4 = document.querySelector('.select-4');
            const input1 = document.querySelector('.input-1');
            const cycleRadio = document.querySelector('input[name="projectCycle"]:checked');

            if (select1.value === '' || select2.value === '' ||
                select3.value === '' || select4.value === '' ||
                input1.value.trim() === '' || !cycleRadio) {
                alert('请选择所有选项并输入项目名称');
                return;
            }

            const selectValue = {
                select1Value: select1.options[select1.selectedIndex].text,
                select2Value: select2.options[select2.selectedIndex].text,
                select3Value: select3.options[select3.selectedIndex].text,
                select4Value: select4.options[select4.selectedIndex].text,
                input1Value: input1.value.trim(),
                cycleValue: cycleRadio.value
            };

            window.chrome.webview.postMessage(selectValue);
        }
    </script>
</head>

<body>
    <div class="dashboard">
        <div class="main-content">
            <div class="card">
                <div class="blocks block-category-1">
                    <div class="titles title-1">一级分类</div>
                    <select class="selects select-1" onchange="changeSelect1()">
                        <option value="" disabled selected>选择一个选项</option>
                        <option value="两大基础">两大基础</option>
                        <option value="四位一体赋能">四位一体赋能</option>
                        <option value="两项能力">两项能力</option>
                        <option value="1个生态">1个生态</option>
                        <option value="数字化管理体系">数字化管理体系</option>
                    </select>
                </div>
                <div class="blocks block-category-2">
                    <div class="titles title-2">二级分类</div>
                    <select class="selects select-2" onchange="changeSelect2()">
                        <option value="" disabled selected>选择一个选项</option>
                    </select>
                </div>
                <div class="blocks block-category-3">
                    <div class="titles title-3">三级分类</div>
                    <select class="selects select-3" onchange="changeSelect3()">
                        <option value="" disabled selected>选择一个选项</option>
                    </select>
                </div>
                <div class="blocks block-category-4">
                    <div class="titles title-4">　项目类</div>
                    <select class="selects select-4">
                        <option value="" disabled selected>选择一个选项</option>
                    </select>
                </div>
                <div class="blocks block-input-1">
                    <div class="titles title-5">项目名称</div>
                    <input class="inputs input-1" type="text" placeholder="请输入项目名称">
                </div>
                <div class="blocks block-cycle">
                    <div class="titles title-6">立项周期</div>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="cycle-current" name="projectCycle" value="current" checked>
                            <label for="cycle-current">年中</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="cycle-next" name="projectCycle" value="next">
                            <label for="cycle-next">明年</label>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-submit" onclick="submit()">确定</button>
            </div>
        </div>
    </div>
</body>

</html>