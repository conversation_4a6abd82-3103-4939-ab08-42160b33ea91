﻿using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public partial class LoginForm : Form
    {
        private LoginApiService _loginApiService; // 使用LoginApiService来处理API调用
        private LlmHealthCheckService _llmHealthCheckService; // LLM健康检查服务
        public string LoggedInUserName { get; private set; } // 添加一个属性来获取登录成功的username
        public string LoggedShowName { get; private set; } // 添加一个属性来获取登录成功的full_name
        public string LoggedAccessToken { get; private set; } // 添加一个属性来获取access_token
        public string LoggedErrorMessage { get; private set; } // 添加一个属性来获取errorMessage



        public LoginForm()
        {
            InitializeComponent();
            InitializeWebView2();
            _loginApiService = new LoginApiService(); // 初始化LoginApiService
            // 配置LLM健康检查环境（生产环境）
            LlmHealthCheckConfig.ConfigureForEnvironment(EnvironmentType.Production);
            _llmHealthCheckService = new LlmHealthCheckService(LlmHealthCheckConfig.UseMockDataOnFailure); // 初始化LLM健康检查服务
        }

        private async void InitializeWebView2()
        {
            try
            {
                // 检查WebView2是否已经初始化
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                // 指定WebView2的数据目录到一个可写路径
                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                // 确保WebView2运行时已安装，并使用自定义环境
                await webView21.EnsureCoreWebView2Async(environment);

                // 获取本地HTML文件的完整路径
                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "Loginplus.html");
                // 将文件路径转换为URI
                webView21.Source = new Uri(htmlFilePath);

                // 添加事件处理程序
                webView21.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
                webView21.CoreWebView2.WebMessageReceived += WebView21_WebMessageReceived;
                webView21.CoreWebView2.NewWindowRequested += CoreWebView2_NewWindowRequested;

                // 可选：页面加载完成、导航完成等事件处理
                webView21.NavigationCompleted += WebView21_NavigationCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void CoreWebView2_NewWindowRequested(object sender, CoreWebView2NewWindowRequestedEventArgs e)
        {
            e.Handled = true;
            string newWindowUri = e.Uri;

            //MessageBox.Show($"新窗口请求URI: {newWindowUri}");

            if (newWindowUri.EndsWith("agreement.html"))
            {
                AgreementForm agreementForm = new AgreementForm();
                agreementForm.Show();
            }
            else if (newWindowUri.EndsWith("forgot_password.html"))
            {
                ForgetPsdForm forgotPasswordForm = new ForgetPsdForm();
                forgotPasswordForm.Show();
            }
            else if (newWindowUri.EndsWith("register.html"))
            {
                RegisterForm registerForm = new RegisterForm();
                registerForm.Show();
            }
        }

        private void WebView21_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                // 页面加载成功时的操作
                // MessageBox.Show("页面加载完成");
            }
            else
            {
                // 处理页面加载失败的情况
                MessageBox.Show("页面加载失败");
            }
        }

        private async void WebView21_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // 获取消息内容
                var messageJson = e.WebMessageAsJson;

                // 使用 JsonConvert 解析消息
                var loginData = JsonConvert.DeserializeAnonymousType(messageJson, new { username = "", password = "" });

                if (loginData != null)
                {
                    try
                    {
                        var loginResult = await _loginApiService.LoginAsync(loginData.username, loginData.password);

                        if (loginResult.errorMessage != null)
                        {
                            // 登录失败，发送错误消息回前端
                            webView21.CoreWebView2.PostWebMessageAsJson(
                                JsonConvert.SerializeObject(new
                                {
                                    status = "error",
                                    message = loginResult.errorMessage
                                })
                            );
                            return;
                        }


                        // 保存登录信息
                        LoggedInUserName = loginResult.username;
                        LoggedShowName = loginResult.fullName;
                        LoggedAccessToken = loginResult.access_token;
                        LoggedErrorMessage = loginResult.errorMessage;

                        // 发送登录成功消息
                        webView21.CoreWebView2.PostWebMessageAsJson(
                            JsonConvert.SerializeObject(new
                            {
                                status = "success",
                                message = "登录成功，检测服务状态..."
                            })
                        );

                        // 执行LLM服务检测
                        await CheckLlmServicesAsync();

                        DialogResult = DialogResult.OK;
                        Close();
                    }
                    catch (Exception ex)
                    {
                        // 发送错误消息回前端
                        webView21.CoreWebView2.PostWebMessageAsJson(
                            JsonConvert.SerializeObject(new
                            {
                                status = "error",
                                message = ex.Message
                            })
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理登录消息时出错: {ex.Message}\n原始消息: {e.WebMessageAsJson}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task CheckLlmServicesAsync()
        {
            // 检查是否启用了健康检查功能
            if (!LlmHealthCheckConfig.EnableHealthCheck)
            {
                return; // 如果禁用了功能，直接返回
            }

            try
            {
                // 更新前端状态
                webView21.CoreWebView2.PostWebMessageAsJson(
                    JsonConvert.SerializeObject(new
                    {
                        status = "checking",
                        message = "正在检测LLM服务状态，请稍候。"
                    })
                );

                // 执行LLM健康检查
                var healthResponse = await _llmHealthCheckService.CheckLlmHealthAsync();
                
                // 获取状态摘要
                var statusSummary = _llmHealthCheckService.GetStatusSummary(healthResponse);
                var hasAvailableServices = _llmHealthCheckService.HasAvailableServices(healthResponse);

                // 更新前端显示检测结果
                webView21.CoreWebView2.PostWebMessageAsJson(
                    JsonConvert.SerializeObject(new
                    {
                        status = hasAvailableServices ? "llm_ok" : "llm_warning",
                        message = statusSummary
                    })
                );

                // 延迟一下让用户看到结果
                await Task.Delay(LlmHealthCheckConfig.StatusDisplayDurationMs);

                // 不再显示详细状态窗口，只显示简单的状态提示
            }
            catch (Exception ex)
            {
                // LLM检测失败，但不影响登录
                System.Diagnostics.Debug.WriteLine($"LLM服务检测失败: {ex.Message}");
                
                webView21.CoreWebView2.PostWebMessageAsJson(
                    JsonConvert.SerializeObject(new
                    {
                        status = "llm_error",
                        message = "⚠️ LLM服务检测失败"
                    })
                );

                // 延迟一下让用户看到错误信息
                await Task.Delay(LlmHealthCheckConfig.StatusDisplayDurationMs);
            }
        }

        private void Login_Load(object sender, EventArgs e)
        {
            // 登录窗体加载时的初始化操作
        }
    }
}
