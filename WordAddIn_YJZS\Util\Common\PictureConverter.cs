﻿using System;
using System.Windows.Forms;
using System.Drawing;
using stdole;

//public class PictureConverter : System.Windows.Forms.AxHost
//{
//    public PictureConverter() : base("59EE46BA-677D-4d20-BF10-8D8067CB8B33") { }//base(string.Empty)

//    public static IPictureDisp ImageToPictureDisp(Image image)
//    {
//        // 调用受保护的 GetIPictureDispFromPicture 方法
//        return (IPictureDisp)AxHost.GetIPictureDispFromPicture(image);
//    }
//}

public class PictureConverter : System.Windows.Forms.AxHost
{
    private PictureConverter() : base("59EE46BA-677D-4d20-BF10-8D8067CB8B33") { }

    public static stdole.IPictureDisp ImageToPictureDisp(Image image)
    {
        // 直接使用静态方法
        return (stdole.IPictureDisp)GetIPictureDispFromPicture(image);
    }
}
