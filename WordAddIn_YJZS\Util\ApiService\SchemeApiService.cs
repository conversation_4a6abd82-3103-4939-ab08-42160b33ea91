﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace WordAddIn_YJZS
{
    public class SchemeApiService
    {
        private readonly HttpClient client;

        public SchemeApiService()
        {
            client = HttpClientManager.Client;
        }

        private void ShowMessage(string message, string caption = "提示",
        MessageBoxButtons buttons = MessageBoxButtons.OK,
        MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        // 在错误处理时使用不同的图标
        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        // 通用的异步POST请求方法
        private async Task<List<ContentItem>> PostAndHandleBaseAsync(
            string url,
            object requestBody,
            string operationType)
        {
            try
            {
                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(url, jsonContent);
                var responseString = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ContentItem>>>(responseString);
                        if (apiResponse?.data != null)
                        {
                            // 处理图片下载和表格数据
                            foreach (var item in apiResponse.data)
                            {
                                if (item.style == "-1")
                                {
                                    Console.WriteLine($"发现图片类型内容: {item.content}");
                                    string imagePath = await item.DownloadImageAsync();
                                    if (!string.IsNullOrEmpty(imagePath))
                                    {
                                        Console.WriteLine($"图片已下载到: {imagePath}");
                                        item.content = imagePath;
                                    }
                                    else
                                    {
                                        Console.WriteLine("图片下载失败");
                                    }
                                }
                                else if (item.style == "-3" && item.content != null)
                                {
                                    ProcessTableData(item);
                                }
                            }
                            return apiResponse.data;
                        }
                    }
                    catch (JsonException)
                    {
                        return new List<ContentItem> { new ContentItem { style = "-5", content = responseString } };
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                {
                    //ShowErrorMessage($"服务器内部错误: {responseString}");
                    return new List<ContentItem>
                    {
                        new ContentItem
                            {
                                style = "0",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            },
                        new ContentItem
                        {
                            style = "-5",
                            content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                        }
                    };
                }

                //ShowErrorMessage($"请求失败: {response.StatusCode} - {responseString}");
                return new List<ContentItem>();
            }
            catch (JsonException)
            {
                //ShowErrorMessage($"解析响应数据时出错: {ex.Message}");
                return new List<ContentItem>();
            }
            catch (HttpRequestException)
            {
                //ShowErrorMessage($"网络请求错误: {ex.Message}");
                return new List<ContentItem>();
            }
            catch (Exception)
            {
                //ShowErrorMessage($"获取{operationType}时发生未知错误: {ex.Message}");
                return new List<ContentItem>();
            }
        }

        private async Task<List<ContentItem>> PostAndHandleAsync(
            string url,
            string username,
            string project_name,
            string operationType)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                ShowMessage("用户名不能为空");
                return new List<ContentItem>();
            }
            if (string.IsNullOrWhiteSpace(project_name))
            {
                ShowMessage("项目名称不能为空");
                return new List<ContentItem>();
            }

            var requestBody = new { username, project_name };
            return await PostAndHandleBaseAsync(url, requestBody, operationType);
        }

        private async Task<List<ContentItem>> PostAndHandleDeployModeAsync(
            string url,
            string username,
            string project_name,
            string operationType,
            string deployment_mode)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                ShowMessage("用户名不能为空");
                return new List<ContentItem>();
            }
            if (string.IsNullOrWhiteSpace(project_name))
            {
                ShowMessage("项目名称不能为空");
                return new List<ContentItem>();
            }
            if (deployment_mode == null)
            {
                ShowMessage("额外参数不能为空");
                return new List<ContentItem>();
            }

            var requestBody = new { username, project_name, deployment_mode };
            return await PostAndHandleBaseAsync(url, requestBody, operationType);
        }

        private async Task<List<ContentItem>> PostAndHandleSystemLevelAsync(
            string url,
            string username,
            string project_name,
            string operationType,
            string system_level)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                ShowMessage("用户名不能为空");
                return new List<ContentItem>();
            }
            if (string.IsNullOrWhiteSpace(project_name))
            {
                ShowMessage("项目名称不能为空");
                return new List<ContentItem>();
            }
            if (system_level == null)
            {
                ShowMessage("额外参数不能为空");
                return new List<ContentItem>();
            }

            var requestBody = new { username, project_name, system_level };
            return await PostAndHandleBaseAsync(url, requestBody, operationType);
        }

        // 辅助方法：处理表格数据
        private void ProcessTableData(ContentItem item)
        {
            try
            {
                if (item.content is JArray jArray)
                {
                    var tableData = jArray.Select(row =>
                        ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                    ).ToList();
                    item.content = tableData;
                }
                else if (item.content is string strContent)
                {
                    try
                    {
                        var tableData = JsonConvert.DeserializeObject<List<List<string>>>(strContent);
                        if (tableData != null)
                        {
                            item.content = tableData;
                        }
                    }
                    catch
                    {
                        var array = JsonConvert.DeserializeObject<JArray>(strContent);
                        if (array != null)
                        {
                            var tableData = array.Select(row =>
                                ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                            ).ToList();
                            item.content = tableData;
                        }
                    }
                }
                else
                {
                    string jsonStr = JsonConvert.SerializeObject(item.content);
                    try
                    {
                        var tableData = JsonConvert.DeserializeObject<List<List<string>>>(jsonStr);
                        if (tableData != null)
                        {
                            item.content = tableData;
                        }
                    }
                    catch
                    {
                        Console.WriteLine($"无法将内容转换为表格数据: {jsonStr}");
                        item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理表格数据时出错: {ex.Message}");
                item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
            }
        }

        //业务架构-业务能力
        public async Task<List<ContentItem>> BusinessCapabilityAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BusinessCapabilityUrl, username, project_name, "业务架构-业务能力");
        }

        //业务架构-业务流程协作能力
        public async Task<List<ContentItem>> BusinessCollaborationAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.BusinessCollaborationUrl, username, project_name, "业务架构-业务流程协作能力");
        }

        //应用架构-应用模块
        public async Task<List<ContentItem>> ApplyModuleAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.ApplyModuleUrl, username, project_name, "应用架构-应用模块");
        }

        //应用架构-应用功能
        public async Task<List<ContentItem>> ApplyFunctionAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.ApplyFunctionUrl, username, project_name, "应用架构-应用功能");
        }

        //应用架构-应用交互
        public async Task<List<ContentItem>> ApplyInteractionAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.ApplyInteractionUrl, username, project_name, "应用架构-应用交互");
        }

        //应用架构-业务与应用对应情况
        public async Task<List<ContentItem>> ApplyToBusinessAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.ApplyToBusinessUrl, username, project_name, "应用架构-业务与应用对应情况");
        }

        //数据架构-数据域
        public async Task<List<ContentItem>> DataDomainAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.DataDomainUrl, username, project_name, "数据架构-数据域");
        }

        //数据架构-逻辑实体
        public async Task<List<ContentItem>> LogicEntityAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.LogicEntityUrl, username, project_name, "数据架构-逻辑实体");
        }

        //数据架构-逻辑实体分布
        public async Task<List<ContentItem>> LogicEntityDistriAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.LogicEntityDistriUrl, username, project_name, "数据架构-逻辑实体分布");
        }

        //技术架构-技术分类
        public async Task<List<ContentItem>> TechClassifyAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.TechClassifyUrl, username, project_name, "数据架构-逻辑实体分布");
        }

        //系统部署及软硬件-部署方式
        public async Task<List<ContentItem>> DeploymentAsync(string username, string project_name, string deployment_mode)
        {
            return await PostAndHandleDeployModeAsync(AllApi.DeploymentUrl, username, project_name, "系统部署及软硬件-部署方式", deployment_mode);
        }

        //安全技术方案
        public async Task<List<ContentItem>> SecuritySolutionAsync(string username, string project_name, string system_level)
        {
            return await PostAndHandleSystemLevelAsync(AllApi.SecuritySolutionUrl, username, project_name, "安全技术方案", system_level);
        }

        //项目实施需求-实施策略
        public async Task<List<ContentItem>> StrategyAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.StrategyUrl, username, project_name, "项目实施需求-实施策略");
        }

        //项目实施需求-实施计划
        public async Task<List<ContentItem>> ImplementPlanAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.ImplementPlanUrl, username, project_name, "项目实施需求-实施计划");
        }

        //项目实施需求-实施任务分解
        public async Task<List<ContentItem>> TaskDecomposeAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.TaskDecomposeUrl, username, project_name, "项目实施需求-实施任务分解");
        }
    }
}
