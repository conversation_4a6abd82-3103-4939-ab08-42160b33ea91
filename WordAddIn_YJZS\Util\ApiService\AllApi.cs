﻿namespace WordAddIn_YJZS
{
    public class AllApi
    {
        // API基础地址，硬编码为生产环境
        public static readonly string BaseUrl = "http://115.156.114.151:12010";//10.150.112.80:12010 or 115.156.114.151

        public static readonly string CheckVersionUrl = $"{BaseUrl}/frontend/version/check";//检查更新        

        public static readonly string CheckWordUrl = $"{BaseUrl}/docs_api/check/check_word";//检查资料

        public static readonly string CheckReportUrl = $"{BaseUrl}/docs_api/check/gen_report";//生成检查报告

        public static readonly string CheckExcelUrl = $"{BaseUrl}/excel_api/check_table/table3_1";//检查表格

        public static readonly string CheckTimeUrl = $"{BaseUrl}/excel_api/table3_1/time_eval";//评估时间

        # region 文件、参数配置、删除项目名称
        public static readonly string UploadUrl = $"{BaseUrl}/common/files/upload";//上传文件
        public static readonly string GetFilesUrl = $"{BaseUrl}/common/files/files";//获取文件列表
        public static readonly string DeleteFileUrl = $"{BaseUrl}/common/files/delete";//删除文件

        public static readonly string ParaConfigUrl = $"{BaseUrl}/common/files/project_config";//配置项目必要参数

        public static readonly string DelProjectNameUrl = $"{BaseUrl}/common/files/delete_project";//删除项目名称
        #endregion

        # region 登录、注册、重置密码
        public static readonly string RegisterUrl = $"{BaseUrl}/common/auth/register";//注册
        public static readonly string LoginUrl = $"{BaseUrl}/common/auth/login";//登录
        public static readonly string ResetPsdUrl = $"{BaseUrl}/common/auth/reset-password";//重置密码

        public static readonly string LoginStateUrl = $"{BaseUrl}/common/auth/verify_token";//验证访问令牌

        #endregion

        # region 主要功能
        public static readonly string WriterUrl = $"{BaseUrl}/common/llm_chat/basic";//扩写/缩写/优化文段
        public static readonly string LlmHealthCheckUrl = $"{BaseUrl}/common/llm_chat/health_check";//LLM服务健康检查
        #endregion

        # region 封面
        //封面
        public static readonly string GetAuthorUrl = $"{BaseUrl}/docs_api/cover/project_staff";//批准、审核、校核、编写人员
        public static readonly string GetProjectNameUrl = $"{BaseUrl}/docs_api/cover/project_name";//项目名称（校验并补齐）
        public static readonly string GetCoverProjectNameUrl = $"{BaseUrl}/docs_api/cover/project_full_name";//项目名称（校验并补齐）
        #endregion

        # region 1概述
        //1概述
        public static readonly string BackgroundUrl = $"{BaseUrl}/docs_api/overview/background";//项目背景
        public static readonly string BasisUrl = $"{BaseUrl}/docs_api/overview/basis";//项目依据
        public static readonly string GoalsUrl = $"{BaseUrl}/docs_api/overview/goals";//项目目标
        public static readonly string BusinessScopeUrl = $"{BaseUrl}/docs_api/overview/project_scope/business_scope";//生成项目业务范围
        public static readonly string ApplicationScopeUrl = $"{BaseUrl}/docs_api/overview/project_scope/application_scope";//应用范围
        public static readonly string InvestmentIncUrl = $"{BaseUrl}/docs_api/overview/project_scope/investment_unit";//投资（建设）单位
        public static readonly string DevelopmentScopeUrl = $"{BaseUrl}/docs_api/overview/project_scope/development_scope";//开发范围
        #endregion

        # region 2现状分析及必要性结论
        //2现状分析及必要性结论
        public static readonly string StatusUrl = $"{BaseUrl}/docs_api/status_and_necessity/current_status";//现状分析
        public static readonly string BusinessDemandUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/business_demand";//业务需求
        public static readonly string FunctionDemandUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/functional_demand";//功能需求
        public static readonly string PerformanceDemandUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/performance_demand";//性能需求
        public static readonly string BusinessIntegrationDemandUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/business_integration_demand";//业务集成需求
        public static readonly string SecurityDemandUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/security_demand"; //安全需求
        public static readonly string UserScaleUrl = $"{BaseUrl}/docs_api/status_and_necessity/demand/user_scale";//用户规模
        public static readonly string NecessityUrl = $"{BaseUrl}/docs_api/status_and_necessity/necessity_conclusion";//必要性结论
        #endregion

        # region 3项目方案
        //3项目方案
        public static readonly string BusinessCapabilityUrl = $"{BaseUrl}/docs_api/proposal/business_architecture/business_capability";//业务架构-业务能力
        public static readonly string BusinessCollaborationUrl = $"{BaseUrl}/docs_api/proposal/business_architecture/business_process_collaboration_status";//业务架构-业务流程协作能力

        public static readonly string ApplyModuleUrl = $"{BaseUrl}/docs_api/proposal/application_architecture/application_module";//应用架构-应用模块
        public static readonly string ApplyFunctionUrl = $"{BaseUrl}/docs_api/proposal/application_architecture/application_functionality";//应用架构-应用功能
        public static readonly string ApplyInteractionUrl = $"{BaseUrl}/docs_api/proposal/application_architecture/application_interaction";//应用架构-应用交互
        public static readonly string ApplyToBusinessUrl = $"{BaseUrl}/docs_api/proposal/application_architecture/business_application_relationship";//应用架构-业务与应用对应情况

        public static readonly string DataDomainUrl = $"{BaseUrl}/docs_api/proposal/data_architecture/data_domain";//数据架构-数据域
        public static readonly string LogicEntityUrl = $"{BaseUrl}/docs_api/proposal/data_architecture/logical_entity";//数据架构-逻辑实体
        public static readonly string LogicEntityDistriUrl = $"{BaseUrl}/docs_api/proposal/data_architecture/logical_entity_distribution";//数据架构-逻辑实体分布

        public static readonly string TechClassifyUrl = $"{BaseUrl}/docs_api/proposal/technical_architecture/technical_classification";//技术架构-技术分类

        public static readonly string DeploymentUrl = $"{BaseUrl}/docs_api/proposal/system_deployment/deployment_method";//系统部署及软硬件-部署方式

        public static readonly string SecuritySolutionUrl = $"{BaseUrl}/docs_api/proposal/security_technology_solution";//安全技术方案

        public static readonly string StrategyUrl = $"{BaseUrl}/docs_api/proposal/implementation_requirements/implementation_strategy";//项目实施需求-实施策略
        public static readonly string ImplementPlanUrl = $"{BaseUrl}/docs_api/proposal/implementation_requirements/implementation_plan";//项目实施需求-实施计划
        public static readonly string TaskDecomposeUrl = $"{BaseUrl}/docs_api/proposal/implementation_requirements/implementation_task_decomposition";//项目实施需求-实施任务分解
        #endregion

        # region 4项目投资估算
        //4项目投资估算
        public static readonly string InvestmentBasisUrl = $"{BaseUrl}/docs_api/investment/investment_justification";//投资依据说明
        public static readonly string TotalInvestmentUrl = $"{BaseUrl}/docs_api/investment/total_investment";//总投资
        public static readonly string FundPlanUrl = $"{BaseUrl}/docs_api/investment/funding_plan";//资金计划建议
        #endregion

        # region 5项目效益分析
        //5项目效益分析
        public static readonly string ManagementBenefitUrl = $"{BaseUrl}/docs_api/benefit/management_benefit";//管理效益分析
        public static readonly string EconimicBenefitUrl = $"{BaseUrl}/docs_api/benefit/economic_benefit";//经济效益分析
        public static readonly string SocialBenefitUrl = $"{BaseUrl}/docs_api/benefit/social_benefit";//社会效益分析
        #endregion

        # region 6项目风险分析
        //6项目风险分析
        public static readonly string RiskAnalyseUrl = $"{BaseUrl}/docs_api/risk/risk_analysis";//项目风险分析
        #endregion

        # region 7项目可研结论
        //7项目可研结论
        public static readonly string ConclusionUrl = $"{BaseUrl}/docs_api/conclusion/feasibility_conclusion";//项目可研结论
        #endregion

        #region excel
        public static readonly string ExcelCoverUrl = $"{BaseUrl}/excel_api/table0/estimate_cost";
        public static readonly string ExcelExplainUrl = $"{BaseUrl}/excel_api/table0/project_invest";
        public static readonly string InvestmentCoverUrl = $"{BaseUrl}/docs_api/overview/project_scope/investment_unit";//GET投资（建设）单位
        public static readonly string Sheet1Url = $"{BaseUrl}/excel_api/table1/table1";
        public static readonly string Sheet2Url = $"{BaseUrl}/excel_api/table2/table2";
        public static readonly string Sheet3Url = $"{BaseUrl}/excel_api/table3/table3";
        public static readonly string Sheet3_1Url = $"{BaseUrl}/excel_api/table3_1/table3_1";
        public static readonly string Sheet3_2Url = $"{BaseUrl}/excel_api/table3_2/table3_2";
        public static readonly string Sheet4Url = $"{BaseUrl}/excel_api/table4/table4";
        public static readonly string Sheet5Url = $"{BaseUrl}/excel_api/table5/table5";
        public static readonly string Sheet6Url = $"{BaseUrl}/excel_api/table678/table6";
        public static readonly string Sheet7Url = $"{BaseUrl}/excel_api/table678/table7";
        public static readonly string Sheet8Url = $"{BaseUrl}/excel_api/table678/table8";
        public static readonly string ParaSheetUrl = $"{BaseUrl}/excel_api/parameter_table/parameter_table";
        public static readonly string OtherTableUrl = $"{BaseUrl}/excel_api/other_table/other_table";//分摊单位表
        #endregion
    }
}
