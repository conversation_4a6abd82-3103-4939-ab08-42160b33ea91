﻿using System.Drawing;

namespace WordAddIn_YJZS
{
    partial class ProgressForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.progressBar = new WordAddIn_YJZS.ArcProgressBar();
            this.SuspendLayout();
            // 
            // progressBar
            // 
            this.progressBar.EndColor = System.Drawing.Color.MediumSeaGreen;
            this.progressBar.Location = new System.Drawing.Point(28, 26);
            this.progressBar.Margin = new System.Windows.Forms.Padding(0);
            this.progressBar.Maximum = 100;
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(180, 180);
            this.progressBar.StartColor = System.Drawing.Color.DodgerBlue;
            this.progressBar.StatusText = "";
            this.progressBar.TabIndex = 0;
            this.progressBar.TextColor = System.Drawing.Color.DarkSlateGray;
            this.progressBar.Value = 0;
            // 
            // ProgressForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.ClientSize = new System.Drawing.Size(218, 218);
            this.ControlBox = false;
            this.Controls.Add(this.progressBar);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ProgressForm";
            this.Opacity = 0.7D;
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.TopMost = true;
            this.ResumeLayout(false);

        }

        private ArcProgressBar progressBar;

        #endregion
    }
}