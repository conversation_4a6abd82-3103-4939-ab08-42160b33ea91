﻿using System;
using System.Windows.Forms;
using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;

namespace WordAddIn_YJZS
{
    public partial class VirtualAssistant : UserControl
    {
        //private WebBrowser webBrowser;
        private bool isWebView2Initialized = false;

        public VirtualAssistant()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            // webView21 已在 Designer 中初始化
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    isWebView2Initialized = true;
                    return;
                }
                string userDataFolder = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);
                await webView21.EnsureCoreWebView2Async(environment);
                isWebView2Initialized = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化虚拟顾问WebView2失败: {ex.Message}");
            }
        }

        public async void NavigateToUrl(string url)
        {
            if (!isWebView2Initialized)
            {
                // 等待初始化完成
                await System.Threading.Tasks.Task.Delay(500);
                if (!isWebView2Initialized)
                {
                    // 再次尝试初始化
                    InitializeWebView2();
                    await System.Threading.Tasks.Task.Delay(500);
                }
            }
            if (webView21.CoreWebView2 != null)
            {
                webView21.Source = new Uri(url);
            }
            else
            {
                webView21.CoreWebView2InitializationCompleted += (s, e) =>
                {
                    if (webView21.CoreWebView2 != null)
                        webView21.Source = new Uri(url);
                };
            }
        }
    }
}