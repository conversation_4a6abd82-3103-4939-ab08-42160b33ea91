<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 600px;
        }

        .profile {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .profile-pic {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .username {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .info-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid rgba(74, 144, 226, 0.3);
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.5);
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

            .info-group span {
                font-size: 16px;
                color: #333;
            }

            .info-group .status {
                margin-left: 10px;
                font-weight: 500;
            }

            .info-group .buttons {
                display: flex;
            }

            .info-group .input-group {
                display: none;
            }

                .info-group .buttons button,
                .info-group .input-group button {
                    padding: 8px 15px;
                    background: linear-gradient(45deg, #4a90e2, #63b3ed);
                    border: none;
                    border-radius: 8px;
                    color: #fff;
                    font-size: 14px;
                    cursor: pointer;
                    margin-left: 10px;
                }

        .input-group input {
            padding: 8px;
            font-size: 14px;
            margin-right: 10px;
            border: 1px solid rgba(74, 144, 226, 0.3);
            border-radius: 8px;
        }

            .input-group input:focus {
                outline: none;
                border-color: #4a90e2;
                box-shadow: 0 0 10px rgba(74, 144, 226, 0.2);
            }

        .input-error {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
        }

        .cancel-button {
            background: linear-gradient(45deg, #dc3545, #ff6b6b) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="profile">
            <img src="path_to_avatar.jpg" alt="头像" class="profile-pic">
            <div class="username">用户名</div>
        </div>
        <div class="info-group" id="phone-group">
            <span>手机：</span><span class="status">未绑定</span>
            <div class="buttons">
                <button class="bind-button">绑定</button>
                <button class="unbind-button">解绑</button>
                <button class="change-button">更改</button>
            </div>
            <div class="input-group">
                <input type="text" placeholder="请输入手机号码" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
        <div class="info-group" id="email-group">
            <span>邮箱：</span><span class="status">未绑定</span>
            <div class="buttons">
                <button class="bind-button">绑定</button>
                <button class="unbind-button">解绑</button>
                <button class="change-button">更改</button>
            </div>
            <div class="input-group">
                <input type="email" placeholder="请输入邮箱地址" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
        <div class="info-group" id="password-group">
            <span>密码：</span><span class="status">已设置</span>
            <div class="buttons">
                <button class="change-button">修改密码</button>
            </div>
            <div class="input-group">
                <input type="password" placeholder="请输入新密码" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
        <div class="info-group" id="qq-group">
            <span>QQ：</span><span class="status">未绑定</span>
            <div class="buttons">
                <button class="bind-button">绑定</button>
                <button class="unbind-button">解绑</button>
                <button class="change-button">更改</button>
            </div>
            <div class="input-group">
                <input type="text" placeholder="请输入QQ号码" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
        <div class="info-group" id="wechat-group">
            <span>微信：</span><span class="status">未绑定</span>
            <div class="buttons">
                <button class="bind-button">绑定</button>
                <button class="unbind-button">解绑</button>
                <button class="change-button">更改</button>
            </div>
            <div class="input-group">
                <input type="text" placeholder="请输入微信号" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
        <div class="info-group" id="department-group">
            <span>所属部门：</span><span class="status">未设置</span>
            <div class="buttons">
                <button class="set-button">设置</button>
                <button class="change-button">更改</button>
            </div>
            <div class="input-group">
                <input type="text" placeholder="请输入部门名称" class="input-field">
                <button class="confirm-button">确认</button>
                <button class="cancel-button">取消</button>
                <div class="input-error"></div>
            </div>
        </div>
    </div>

    <script>
        document.querySelectorAll('.info-group').forEach(group => {
            const statusElem = group.querySelector('.status');
            let status = statusElem.textContent.trim();
            const buttons = group.querySelector('.buttons');
            const inputGroup = group.querySelector('.input-group');
            const inputField = group.querySelector('.input-field');
            const bindButton = group.querySelector('.bind-button');
            const unbindButton = group.querySelector('.unbind-button');
            const changeButton = group.querySelector('.change-button');
            const setButton = group.querySelector('.set-button');
            const confirmButton = group.querySelector('.confirm-button');
            const cancelButton = group.querySelector('.cancel-button');
            const inputError = group.querySelector('.input-error');

            function updateButtons() {
                if (status.includes('未绑定')) {
                    if (bindButton) bindButton.style.display = 'inline-block';
                    if (unbindButton) unbindButton.style.display = 'none';
                    if (changeButton) changeButton.style.display = 'none';
                } else if (status.includes('已绑定')) {
                    if (bindButton) bindButton.style.display = 'none';
                    if (unbindButton) unbindButton.style.display = 'inline-block';
                    if (changeButton) changeButton.style.display = 'inline-block';
                } else if (status.includes('已设置')) {
                    if (setButton) setButton.style.display = 'none';
                    if (changeButton) changeButton.style.display = 'inline-block';
                } else if (status.includes('未设置')) {
                    if (setButton) setButton.style.display = 'inline-block';
                    if (changeButton) changeButton.style.display = 'none';
                }
            }

            if (bindButton) {
                bindButton.addEventListener('click', () => {
                    inputGroup.style.display = 'flex';
                    buttons.style.display = 'none';
                    inputError.textContent = '';
                });
            }

            if (unbindButton) {
                unbindButton.addEventListener('click', () => {
                    statusElem.textContent = '未绑定';
                    status = '未绑定';
                    updateButtons();
                });
            }

            if (changeButton) {
                changeButton.addEventListener('click', () => {
                    inputGroup.style.display = 'flex';
                    buttons.style.display = 'none';
                    inputError.textContent = '';
                });
            }

            if (setButton) {
                setButton.addEventListener('click', () => {
                    inputGroup.style.display = 'flex';
                    buttons.style.display = 'none';
                    inputError.textContent = '';
                });
            }

            confirmButton.addEventListener('click', () => {
                const value = inputField.value.trim();
                const isValid = validateInput(value, group.id);
                if (isValid) {
                    if (group.id === 'phone-group') {
                        statusElem.textContent = value;
                        status = '已绑定';
                    } else if (group.id === 'email-group') {
                        statusElem.textContent = value;
                        status = '已绑定';
                    } else if (group.id === 'qq-group') {
                        statusElem.textContent = value;
                        status = '已绑定';
                    } else if (group.id === 'wechat-group') {
                        statusElem.textContent = value;
                        status = '已绑定';
                    } else if (group.id === 'department-group') {
                        statusElem.textContent = value;
                        status = '已设置';
                    } else if (group.id === 'password-group') {
                        statusElem.textContent = '已设置';
                        status = '已设置';
                    }
                    inputGroup.style.display = 'none';
                    buttons.style.display = 'flex';
                    updateButtons();
                } else {
                    inputError.textContent = getErrorMessage(group.id);
                }
            });

            cancelButton.addEventListener('click', () => {
                inputGroup.style.display = 'none';
                buttons.style.display = 'flex';
                inputError.textContent = '';
            });

            function validateInput(value, groupId) {
                switch (groupId) {
                    case 'phone-group':
                        return /^\d{11}$/.test(value);
                    case 'email-group':
                        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                    case 'qq-group':
                        return /^[1-9]\d{4,11}$/.test(value);
                    case 'wechat-group':
                        return value.length > 0;
                    case 'department-group':
                        return value.length > 0;
                    case 'password-group':
                        return value.length >= 6;
                    default:
                        return false;
                }
            }

            function getErrorMessage(groupId) {
                switch (groupId) {
                    case 'phone-group':
                        return '手机号码格式不正确，请输入11位数字。';
                    case 'email-group':
                        return '邮箱格式不正确，请输入有效的邮箱地址。';
                    case 'qq-group':
                        return 'QQ号码格式不正确，请输入5-12位数字。';
                    case 'wechat-group':
                        return '微信号不能为空。';
                    case 'department-group':
                        return '部门名称不能为空。';
                    case 'password-group':
                        return '密码长度不能少于6位。';
                    default:
                        return '输入无效，请重新输入。';
                }
            }

            updateButtons();
        });
    </script>
</body>
</html>