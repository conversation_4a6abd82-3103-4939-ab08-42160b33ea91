﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using WordAddIn_YJZS;
using System.Linq;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

public class CoverApiService
{
    private readonly HttpClient client;
    public ApiResponse<JObject> Result { get; private set; }

    public CoverApiService()
    {
        client = HttpClientManager.Client;
    }

    // 项目名称
    public async Task<string> ProcessProjectNameAsync(string username, string project_name)
    {
        if (string.IsNullOrEmpty(project_name))
        {
            throw new ArgumentNullException(nameof(project_name), "项目名称不能为空！");
        }

        var requestBody = new { username, project_name };
        return await PostJsonAsync<string>(AllApi.GetProjectNameUrl, requestBody, "project_name");
    }

    public async Task<string> ProcessCoverProjectNameAsync(string username, string project_name)
    {
        if (string.IsNullOrEmpty(project_name))
        {
            throw new ArgumentNullException(nameof(project_name), "项目名称不能为空！");
        }

        var requestBody = new {username, project_name };
        return await PostJsonAsync<string>(AllApi.GetCoverProjectNameUrl, requestBody, "construction_unit");
    }

    // 作者
    public async Task<Dictionary<string, string>> ProcessAuthorAsync(string username)
    {
        if (string.IsNullOrEmpty(username))
        {
            throw new ArgumentNullException(nameof(username), "用户名不能为空！");
        }

        var requestBody = new {username};
        return await PostJsonAsync<Dictionary<string, string>>(AllApi.GetAuthorUrl, requestBody, "staffs");
    }

    // 获取投资单位
    public async Task<string> GetInvestmentUnitAsync(string projectName = null)
    {
        try
        {
            string username = LoginStateManager.Instance.Username;

            if (string.IsNullOrEmpty(username))
            {
                throw new Exception("用户未登录，无法获取投资单位");
            }

            if (string.IsNullOrEmpty(projectName))
            {
                throw new Exception("项目名称不能为空");
            }

            // 构建带参数的URL
            string url = $"{AllApi.InvestmentCoverUrl}?username={Uri.EscapeDataString(username)}&project_name={Uri.EscapeDataString(projectName)}";

            // 发送GET请求
            var response = await client.GetAsync(url);
            var responseString = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<ApiResponse<JObject>>(responseString);
                if (result.data != null && result.data.TryGetValue("investment_unit", out JToken value))
                {
                    return value.ToString();
                }
                throw new Exception("响应中没有找到投资单位信息");
            }
            else
            {
                throw new Exception($"获取投资单位失败：{response.StatusCode}, 响应内容: {responseString}");
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"获取投资单位时发生错误：{ex.Message}");
        }
    }

    // POST请求
    private async Task<T> PostJsonAsync<T>(string url, object data, string dataField)
    {
        using (var jsonContent = new StringContent(JsonConvert.SerializeObject(data)))
        {
            jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            try
            {
                var response = await client.PostAsync(url, jsonContent);
                return await HandleResponseAsync<T>(response, dataField);
            }
            catch (TaskCanceledException)
            {
                throw new Exception("请求超时");
            }
            catch (HttpRequestException ex)
            {
                throw new Exception("网络错误：" + ex.Message);
            }
        }
    }

    // 处理响应
    private async Task<T> HandleResponseAsync<T>(HttpResponseMessage response, string dataField)
    {
        var responseString = await response.Content.ReadAsStringAsync();
        Result = JsonConvert.DeserializeObject<ApiResponse<JObject>>(responseString);

        if (response.IsSuccessStatusCode)
        {
            if (Result.data != null && Result.data.TryGetValue(dataField, out JToken value))
            {
                return value.ToObject<T>();
            }
            throw new Exception($"响应中没有找到 {dataField} 字段");
        }
        else if ((int)response.StatusCode == 422)
        {
            var validationErrors = JsonConvert.DeserializeObject<ValidationErrorResponse>(responseString);
            throw new Exception($"验证错误: {string.Join(", ", validationErrors.Detail.Select(e => e.Msg))}");
        }
        else
        {
            throw new Exception($"请求失败，状态码 {response.StatusCode}: {responseString}");
        }
    }
}