﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class ForgetPsdForm : Form
    {
        private readonly LoginApiService _loginApiService = new LoginApiService();

        public ForgetPsdForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "forgot_password.html");
                webView21.Source = new Uri(htmlFilePath);

                webView21.NavigationCompleted += WebView_NavigationCompleted;
                // 监听 Web 消息事件
                webView21.CoreWebView2.WebMessageReceived += WebView_WebMessageReceived;

                //MessageBox.Show("用户协议WebView2初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                //MessageBox.Show("用户协议页面加载完成");
            }
            else
            {
                MessageBox.Show("用户协议页面加载失败");
            }
        }

        // 处理来自HTML页面的表单数据
        private async void WebView_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                var formData = e.TryGetWebMessageAsString();
                var resetPasswordData = JsonConvert.DeserializeObject<ResetPasswordData>(formData);

                // 调用API进行密码重置
                var response = await _loginApiService.ResetPasswordAsync(
                    resetPasswordData.Username,
                    resetPasswordData.OldPassword,
                    resetPasswordData.NewPassword
                );

                // 将结果传回HTML页面
                var successMessage = $"{{\"message\": \"{response}\" }}";
                webView21.CoreWebView2.PostWebMessageAsString(successMessage);
            }
            catch (Exception ex)
            {
                var errorMessage = $"{{ \"error\": \"{ex.Message}\" }}";
                webView21.CoreWebView2.PostWebMessageAsString(errorMessage);
            }
        }
    }
}
