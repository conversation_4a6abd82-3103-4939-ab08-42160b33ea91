<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择建设单位</title>
    <style>
		@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

		body {
			font-family: 'Roboto', sans-serif;
			background-color: #f0f2f5;
			margin: 0;
			padding: 0;
			color: #333;
		}

		.dashboard {
			display: flex;
			width: 1080px;
			margin: 0 auto;
			background-color: #ffffff;
			border-radius: 10px;
			box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
			overflow: hidden;
			min-height: 700px;
		}

		.main-content {
			flex-grow: 1;
			padding: 20px;
			max-width: none;
			width: 830px;
		}

		.card {
			background-color: #fff;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			padding: 20px;
			margin-bottom: 20px;
			position: relative;
			max-width: none;
			min-height: 600px;
		}

		.card h3 {
			margin-top: 0;
			margin-bottom: 20px;
			color: #1a237e;
			font-size: 16px;
		}

		.button-group {
			display: flex;
			gap: 10px;
			position: absolute;
			top: 15px;
			right: 15px;
		}

		.button {
			padding: 8px 15px;
			background: linear-gradient(45deg, #4a90e2, #63b8ff);
			color: #fff;
			border: none;
			border-radius: 5px;
			cursor: pointer;
			transition: background-color 0.3s;
		}

		.button:hover {
			background: linear-gradient(45deg, #3a80d2, #53a8ff);
		}

		/* 侧边栏样式 */
		.sidebar {
			width: 250px;
			background-color: #ffffff;
			color: #333333;
			padding: 30px 20px;
			border-radius: 10px 0 0 10px;
			box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
		}

		.sidebar ul {
			list-style-type: none;
			padding: 0;
			margin: 0;
		}

		.sidebar li {
			margin-bottom: 15px;
		}

		.sidebar a {
			color: #333333;
			text-decoration: none;
			font-size: 16px;
			display: block;
			padding: 12px 15px;
			border-radius: 5px;
			transition: all 0.3s ease;
		}

		.sidebar a:hover {
			background-color: #f0f0f0;
		}

		.sidebar a.active {
			background: linear-gradient(to bottom, #3498db, #2980b9);
			color: #ffffff;
		}

		/* Checkbox 相关样式 */
		.checkbox-group {
			display: flex;
			flex-direction: column;
			gap: 12px;
			width: 100%;
			margin-top: 30px;
		}

		.checkbox-item {
			position: relative;
			min-height: 40px;
			display: block;
			width: 100%;
		}

		.checkbox-item input[type="checkbox"] {
			display: none;
		}

		.checkbox-item label {
			display: flex;
			align-items: center;
			height: 40px;
			border: 1px solid #ddd;
			border-radius: 4px;
			cursor: pointer;
			font-size: 14px;
			color: #333;
			background: #fff;
			transition: all 0.3s ease;
			position: relative;
			padding: 0 15px;
			box-sizing: border-box;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 100%;
			min-width: auto;
			max-width: none;
		}

		.checkbox-item input[type="checkbox"]:checked + label {
			border-color: #4a90e2;
			color: #4a90e2;
		}

		.checkbox-item input[type="checkbox"]:checked + label::after {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			width: 0;
			height: 0;
			border-style: solid;
			border-width: 0 0 20px 20px;
			border-color: transparent transparent #4a90e2 transparent;
		}

		/* 内容区域样式 */
		.content-section {
			display: none;
		}

		.content-section.active {
			display: block;
		}
	</style>
</head>
	<body>
    <div class="dashboard">
        <div class="sidebar">
            <ul>
                <li><a href="#section1" class="sidebar-item active">管制业务单位</a></li>
                <li><a href="#section2" class="sidebar-item">新兴业务单位</a></li>
                <li><a href="#section3" class="sidebar-item">国际业务单位</a></li>
                <li><a href="#section4" class="sidebar-item">金融业务单位</a></li>
                <li><a href="#section5" class="sidebar-item">共享平台单位</a></li>
            </ul>
        </div>

        <div class="main-content">
            <!-- 管制业务单位 -->
            <div class="card content-section active" id="section1">
                <h3>一、选择建设单位 - 管制业务单位</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox1">
                        <label for="checkbox1">南方电网总部</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox2">
                        <label for="checkbox2">广东电网有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox3">
                        <label for="checkbox3">广西电网有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox4">
                        <label for="checkbox4">云南电网有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox5">
                        <label for="checkbox5">贵州电网有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox6">
                        <label for="checkbox6">海南电网有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox7">
                        <label for="checkbox7">深圳供电局有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox8">
                        <label for="checkbox8">超高压输电公司</label>
                    </div>
                </div>
            </div>

            <!-- 新兴业务单位 -->
            <div class="card content-section" id="section2">
                <h3>一、选择建设单位 - 新兴业务单位</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox50">
                        <label for="checkbox50">南方电网储能股份有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox51">
                        <label for="checkbox51">南方电网产业投资集团有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox52">
                        <label for="checkbox52">南方鼎元资产运营有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox53">
                        <label for="checkbox53">南方电网综合能源股份有限公司</label>
                    </div>
                </div>
            </div>

            <!-- 国际业务单位 -->
            <div class="card content-section" id="section3">
                <h3>一、选择建设单位 - 国际业务单位</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox54">
                        <label for="checkbox54">南方电网国际（香港）有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox55">
                        <label for="checkbox55">南方电网澜湄国际能源有限责任公司</label>
                    </div>
                </div>
            </div>

            <!-- 金融业务单位 -->
            <div class="card content-section" id="section4">
                <h3>一、选择建设单位 - 金融业务单位</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox56">
                        <label for="checkbox56">南方电网资本控股有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox57">
                        <label for="checkbox57">南方电网财务有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox58">
                        <label for="checkbox58">鼎和财产保险股份有限公司</label>
                    </div>
                </div>
            </div>

            <!-- 共享平台单位 -->
            <div class="card content-section" id="section5">
                <h3>一、选择建设单位 - 共享平台单位</h3>
                <div class="button-group">
                    <button class="button select-all">全选</button>
                    <button class="button invert-selection">反选</button>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox59">
                        <label for="checkbox59">中共中国南方电网有限责任公司党校</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox60">
                        <label for="checkbox60">中国南方电网有限责任公司北京分公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox61">
                        <label for="checkbox61">中国南方电网有限责任公司共享运营公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox62">
                        <label for="checkbox62">中国南方电网用户生态运营公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox63">
                        <label for="checkbox63">南方电网数字电网集团有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox64">
                        <label for="checkbox64">南方电网供应链集团有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox65">
                        <label for="checkbox65">南方电网能源发展研究院有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox66">
                        <label for="checkbox66">南方电网科学研究院有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox67">
                        <label for="checkbox67">广州电力交易中心有限责任公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox68">
                        <label for="checkbox68">南方电网数字传媒科技有限公司</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="checkbox69">
                        <label for="checkbox69">南方电网新型电力系统（北京）研究院有限公司</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
	<script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有侧边栏链接和内容区域
            const sidebarLinks = document.querySelectorAll('.sidebar a');
            const contentSections = document.querySelectorAll('.content-section');

            // 侧边栏切换逻辑
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有active类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    contentSections.forEach(section => section.classList.remove('active'));
                    
                    // 添加active类到当前项
                    this.classList.add('active');
                    
                    // 显示对应的内容区域
                    const targetId = this.getAttribute('href').substring(1);
                    document.getElementById(targetId).classList.add('active');
                });
            });

            // 全选功能
            document.querySelectorAll('.select-all').forEach(button => {
                button.addEventListener('click', function() {
                    const section = this.closest('.content-section');
                    const checkboxes = section.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = true;
                        sendCheckboxState(checkbox);
                    });
                });
            });

            // 反选功能
            document.querySelectorAll('.invert-selection').forEach(button => {
                button.addEventListener('click', function() {
                    const section = this.closest('.content-section');
                    const checkboxes = section.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = !checkbox.checked;
                        sendCheckboxState(checkbox);
                    });
                });
            });

            // checkbox1 的特殊逻辑
            document.getElementById('checkbox1').addEventListener('change', function() {
                if (this.checked) {
                    // 如果 checkbox1 被选中，自动选中 checkbox2 到 checkbox6
                    for (let i = 2; i <= 6; i++) {
                        const checkbox = document.getElementById(`checkbox${i}`);
                        if (checkbox) {
                            checkbox.checked = true;
                            sendCheckboxState(checkbox);
                        }
                    }
                }
            });

            // 发送复选框状态到 C#
            function sendCheckboxState(checkbox) {
                const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: 'checkbox',
                    id: checkbox.id,
                    checked: checkbox.checked,
                    label: label
                });
            }

            // 为所有复选框添加change事件监听器
            document.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    sendCheckboxState(this);
                });
            });

            // 设置初始状态
            function setInitialActiveState() {
                const firstLink = document.querySelector('.sidebar a');
                if (firstLink) {
                    firstLink.classList.add('active');
                    const targetId = firstLink.getAttribute('href').substring(1);
                    document.getElementById(targetId).classList.add('active');
                }
            }

            // 页面加载时设置初始状态
            setInitialActiveState();
        });
    </script>
</body>
</html>