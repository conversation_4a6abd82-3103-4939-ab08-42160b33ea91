﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class EnterprisesForm : Form
    {
        private CalculateFormData currentFormData;
        public bool IsConfirmed { get; private set; }

        public EnterprisesForm()
        {
            InitializeComponent();
            InitializeWebView2();
            IsConfirmed = false;
        }

        private async void InitializeWebView2()
        {
            try
            {
                string userDataFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "WebView2UserData"
                );
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);
                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "resource/web",
                    "merge.html"  // 新的整合后的HTML文件
                );
                webView21.Source = new Uri(htmlFilePath);

                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                webView21.NavigationCompleted += WebView_NavigationCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                try
                {
                    // 创建企业名称到复选框ID的映射
                    var enterpriseToCheckboxId = new Dictionary<string, string>();
                    for (int i = 1; i <= 69; i++) // 假设有69个复选框，根据HTML文件调整
                    {
                        enterpriseToCheckboxId[$"checkbox{i}"] = $"checkbox{i}";
                    }

                    // 创建要传递给JavaScript的复选框状态映射
                    var checkboxMapping = new Dictionary<string, bool>();

                    // 获取所有状态
                    foreach (var state in EnterpriseCheckboxStateManager.CheckboxStates)
                    {
                        string id = state.Key;
                        bool isChecked = state.Value;

                        // 如果是checkbox开头的ID，直接使用
                        if (id.StartsWith("checkbox") && enterpriseToCheckboxId.ContainsKey(id))
                        {
                            checkboxMapping[id] = isChecked;
                        }
                        // 否则尝试查找对应的checkbox ID
                        else if (EnterpriseCheckboxStateManager.CheckboxLabels.ContainsKey(id))
                        {
                            string label = EnterpriseCheckboxStateManager.CheckboxLabels[id];
                            string checkboxId = FindCheckboxIdByLabel(label);
                            if (!string.IsNullOrEmpty(checkboxId))
                            {
                                checkboxMapping[checkboxId] = isChecked;
                            }
                        }
                    }

                    // 创建恢复状态的JavaScript代码
                    string statesJson = JsonConvert.SerializeObject(checkboxMapping);
                    string script = $@"
                    try {{
                        // 恢复复选框状态
                        var states = {statesJson};
                        for (let id in states) {{
                            const checkbox = document.getElementById(id);
                            if (checkbox) {{
                                checkbox.checked = states[id];
                            }}
                        }}
                    }} catch(e) {{
                        // 静默处理错误
                    }}";

                    // 执行JavaScript代码
                    webView21.CoreWebView2.ExecuteScriptAsync(script);

                    // 恢复表单状态
                    if (GlobalParameters.CalculateData != null)
                    {
                        RestoreFormState(GlobalParameters.CalculateData);
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但不显示消息框
                    Console.WriteLine($"更新企业复选框状态时出错: {ex.Message}");
                }
            }
            else
            {
                // 避免显示消息框
                Console.WriteLine("企业选择页面加载失败");
            }
        }
        
        // 根据标签查找对应的checkbox ID
        private string FindCheckboxIdByLabel(string label)
        {
            // 企业名称到checkbox ID的映射
            var enterpriseLabels = new Dictionary<string, string>
            {
                { "南方电网", "checkbox1" },
                { "广东电网公司", "checkbox2" },
                { "广西电网公司", "checkbox3" },
                { "云南电网公司", "checkbox4" },
                { "贵州电网公司", "checkbox5" },
                { "海南电网公司", "checkbox6" },
                { "深圳供电局", "checkbox7" },
                { "南网超高压公司", "checkbox8" },
                { "南网储能公司", "checkbox50" },
                { "南网产业投资集团", "checkbox51" },
                { "鼎元资产公司", "checkbox52" },
                { "南网能源公司", "checkbox53" },
                { "南网国际（香港）公司", "checkbox54" },
                { "南网澜湄国际公司", "checkbox55" },
                { "南网资本控股公司", "checkbox56" },
                { "南网财务公司", "checkbox57" },
                { "鼎和保险公司", "checkbox58" },
                { "南网党校", "checkbox59" },
                { "南网北京分公司", "checkbox60" },
                { "南网共享公司", "checkbox61" },
                { "南网生态运营公司", "checkbox62" },
                { "南网数字集团", "checkbox63" },
                { "南网供应链集团", "checkbox64" },
                { "南网能源院", "checkbox65" },
                { "南网科研院", "checkbox66" },
                { "广州电力交易中心", "checkbox67" },
                { "南网传媒公司", "checkbox68" },
                { "北京研究院", "checkbox69" }
            };
            
            return enterpriseLabels.ContainsKey(label) ? enterpriseLabels[label] : string.Empty;
        }

        private void RestoreFormState(CalculateFormData formData)
        {
            if (webView21?.CoreWebView2 == null || formData == null) return;

            try
            {
                Console.WriteLine("EnterprisesForm.RestoreFormState: 开始恢复表单状态");
                
                // 构建完整的状态对象，确保传递所有值，即使是空值
                var stateJson = JsonConvert.SerializeObject(new
                {
                    select_3 = formData.Select3 ?? "",
                    select_12 = formData.Select12 ?? "",
                    select_1 = formData.Select1 ?? "",
                    select_7 = formData.Select7 ?? "",
                    select_9 = formData.Select9 ?? "",
                    input_10 = formData.Input10,
                    select_11 = formData.Select11 ?? ""
                });

                string script = $"updateFormState({stateJson});";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
                Console.WriteLine("EnterprisesForm.RestoreFormState: 表单状态恢复指令已发送");

                // 同步到其他窗体
                foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
                {
                    if (form is GenerateForm generateForm && !form.IsDisposed && form != this)
                    {
                        generateForm.Invoke(new Action(() => {
                            generateForm.UpdateFormData(formData);
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"EnterprisesForm.RestoreFormState: 恢复表单状态时出错: {ex.Message}");
            }
        }

        public void UpdateFormData(CalculateFormData formData)
        {
            if (webView21?.CoreWebView2 == null || formData == null) 
            {
                Console.WriteLine("EnterprisesForm.UpdateFormData: WebView未初始化或表单数据为空");
                return;
            }

            try
            {
                Console.WriteLine("EnterprisesForm.UpdateFormData: 正在更新表单数据");
                RestoreFormState(formData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"EnterprisesForm.UpdateFormData: 更新表单数据时出错: {ex.Message}");
            }
        }

        // 添加新方法：重置用户修改状态
        public void ResetUserModificationState()
        {
            GlobalParameters.SetEnterpriseSelectionModified(false);
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // 先获取原始消息
                string jsonMessage = e.WebMessageAsJson;

                // 检查是否包含closeForm关键字
                if (jsonMessage.Contains("closeForm"))
                {
                    // 直接关闭表单
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    return;
                }

                // 检查是否是初始状态请求消息
                if (jsonMessage.Contains("getInitialState"))
                {
                    // 不需要任何处理，因为WebView_NavigationCompleted已经处理了状态初始化
                    return;
                }

                var message = JsonConvert.DeserializeObject<dynamic>(jsonMessage);
                string type = message.type.ToString();

                switch (type)
                {
                    case "closeForm":
                        // 处理关闭表单的消息
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                        break;

                    case "checkbox":
                        {
                            // 确保必要的属性存在
                            if (message.id == null || message.@checked == null || message.label == null)
                            {
                                // 如果缺少必要属性，仅记录日志
                                Console.WriteLine("收到的消息格式不正确，缺少必要的属性");
                                return;
                            }

                            string id = message.id.ToString();
                            bool isChecked = message.@checked;
                            string label = message.label.ToString();

                            // 清除之前的所有选择状态（仅当是用户第一次修改时）
                            if (!GlobalParameters.EnterpriseSelectionModifiedByUser)
                            {
                                EnterpriseCheckboxStateManager.Clear();
                            }

                            // 检查状态管理器中是否已存在相同的标签，避免重复
                            bool existingLabelFound = false;
                            string existingId = null;

                            // 查找是否已有相同标签的记录
                            foreach (var pair in EnterpriseCheckboxStateManager.CheckboxLabels)
                            {
                                // 如果找到相同标签的记录
                                if (pair.Value == label)
                                {
                                    existingLabelFound = true;
                                    existingId = pair.Key;
                                    break;
                                }
                            }

                            // 如果找到相同标签的ID但不是当前ID，则移除旧记录
                            if (existingLabelFound && existingId != id && existingId != null)
                            {
                                // 删除旧的记录
                                if (EnterpriseCheckboxStateManager.CheckboxStates.ContainsKey(existingId))
                                {
                                    EnterpriseCheckboxStateManager.CheckboxStates.Remove(existingId);
                                }
                                if (EnterpriseCheckboxStateManager.CheckboxLabels.ContainsKey(existingId))
                                {
                                    EnterpriseCheckboxStateManager.CheckboxLabels.Remove(existingId);
                                }
                            }

                            // 更新状态管理器
                            EnterpriseCheckboxStateManager.UpdateCheckboxState(id, isChecked, label);

                            // 标记为用户手动修改
                            GlobalParameters.SetEnterpriseSelectionModified(true);

                            // 同步到其他窗体
                            foreach (Form form in Application.OpenForms)
                            {
                                if (form is GenerateForm generateForm && !form.IsDisposed)
                                {
                                    generateForm.Invoke(new Action(() => {
                                        generateForm.UpdateEnterpriseCheckboxState(id, isChecked);
                                    }));
                                }
                            }
                        }
                        break;

                    case "formSubmit":
                        {
                            var formData = message.data;

                            // 创建新的 CalculateFormData 实例
                            currentFormData = new CalculateFormData
                            {
                                Select3 = formData.select_3?.ToString(),
                                Select12 = formData.select_12?.ToString(),
                                Select1 = formData.select_1?.ToString() ?? "",
                                Select7 = formData.select_7?.ToString() ?? "",
                                Select9 = formData.select_9?.ToString() ?? "",
                                Input10 = formData.input_10 != null ? Convert.ToDouble(formData.input_10.ToString()) : 0,
                                Select11 = formData.select_11?.ToString() ?? ""
                            };

                            // 验证必要字段
                            if (ValidateFormData(currentFormData))
                            {
                                GlobalParameters.CalculateData = currentFormData;
                                IsConfirmed = true;
                                this.DialogResult = DialogResult.OK;
                                this.Close();
                            }
                        }
                        break;
                    case "saveFormState":
                        {
                            try
                            {
                                Console.WriteLine("EnterprisesForm接收到saveFormState消息，开始处理...");
                                
                                if (message.data == null)
                                {
                                    Console.WriteLine("表单数据不完整，请检查输入");
                                    return;
                                }
                                
                                // 安全地解析数据
                                var formData = new CalculateFormData
                                {
                                    Select3 = message.data.select_3?.ToString() ?? "",
                                    Select12 = message.data.select_12?.ToString() ?? "",
                                    Select1 = message.data.select_1?.ToString() ?? "",
                                    Select7 = message.data.select_7?.ToString() ?? "",
                                    Select9 = message.data.select_9?.ToString() ?? "",
                                    Input10 = message.data.input_10 != null && !string.IsNullOrEmpty(message.data.input_10.ToString())
                                        ? Convert.ToDouble(message.data.input_10.ToString())
                                        : 0,
                                    Select11 = message.data.select_11?.ToString() ?? ""
                                };

                                // 更新全局参数
                                GlobalParameters.CalculateData = formData;
                                Console.WriteLine("EnterprisesForm: 全局表单数据已更新");

                                // 同步到GenerateForm窗体
                                foreach (Form form in Application.OpenForms)
                                {
                                    if (form is GenerateForm generateForm && !form.IsDisposed && form != this)
                                    {
                                        Console.WriteLine("EnterprisesForm: 准备同步到GenerateForm");
                                        generateForm.Invoke(new Action(() => {
                                            generateForm.UpdateFormData(formData);
                                        }));
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"EnterprisesForm: 处理表单数据时出错：{ex.Message}");
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                // 记录错误到控制台而不是显示弹窗
                Console.WriteLine($"处理消息时出错: {ex.Message}");
            }
        }

        private bool ValidateFormData(CalculateFormData data)
        {
            try
            {
                if (data == null) return false;

                // 基础验证：项目分类和建设周期必填
                if (string.IsNullOrEmpty(data.Select3) || string.IsNullOrEmpty(data.Select12))
                {
                    Console.WriteLine("请填写项目分类和建设周期！");
                    return false;
                }

                // 如果是信息系统建设与升级改造，验证其他字段
                if (data.Select3 == "信息系统建设与升级改造")
                {
                    if (string.IsNullOrEmpty(data.Select1) ||
                        string.IsNullOrEmpty(data.Select7) ||
                        string.IsNullOrEmpty(data.Select9) ||
                        data.Input10 <= 0 ||
                        string.IsNullOrEmpty(data.Select11))
                    {
                        Console.WriteLine("请填写所有必要参数！");
                        return false;
                    }
                }
                else
                {
                    // 非信息系统建设与升级改造项目，设置默认值
                    data.Select1 = "";
                    data.Select7 = "";
                    data.Select9 = "";
                    data.Input10 = 0;
                    data.Select11 = "";
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证表单数据时出错：{ex.Message}");
                return false;
            }
        }

        // 更新checkbox状态的公共方法
        public void UpdateCheckboxState(string idOrLabel, bool isChecked)
        {
            if (webView21?.CoreWebView2 == null) return;

            try
            {
                // 尝试查找ID对应的checkbox
                string checkboxId = idOrLabel;

                // 如果不是以checkbox开头，则认为是标签，尝试查找对应的ID
                if (!idOrLabel.StartsWith("checkbox"))
                {
                    checkboxId = FindCheckboxIdByLabel(idOrLabel);
                    
                    // 如果找不到对应ID，可能是直接使用了企业名称
                    if (string.IsNullOrEmpty(checkboxId))
                    {
                        Console.WriteLine($"未找到标签 '{idOrLabel}' 对应的复选框ID");
                        return;
                    }
                }

                // 更新状态管理器
                string label = ""; 
                
                // 从复选框ID反查标签
                if (checkboxId.StartsWith("checkbox"))
                {
                    label = GetLabelFromCheckboxId(checkboxId);
                    if (!string.IsNullOrEmpty(label))
                    {
                        EnterpriseCheckboxStateManager.UpdateCheckboxState(checkboxId, isChecked, label);
                    }
                    else
                    {
                        EnterpriseCheckboxStateManager.UpdateCheckboxState(checkboxId, isChecked, idOrLabel);
                    }
                }

                // 更新UI
                string script = $@"
                try {{
                    const checkbox = document.getElementById('{checkboxId}');
                    if (checkbox) {{
                        checkbox.checked = {isChecked.ToString().ToLower()};
                    }}
                }} catch (e) {{
                    console.error('更新复选框状态出错:', e);
                }}";

                webView21.CoreWebView2.ExecuteScriptAsync(script);

                // 同步到其他窗体
                foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
                {
                    if (form is GenerateForm generateForm && !form.IsDisposed)
                    {
                        generateForm.Invoke(new Action(() => {
                            generateForm.UpdateEnterpriseCheckboxState(checkboxId, isChecked);
                            // 手动触发重新同步
                            generateForm.SyncEnterpriseCheckboxes();
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新复选框状态时出错: {ex.Message}");
            }
        }

        // 辅助方法：根据复选框ID获取对应的标签
        private string GetLabelFromCheckboxId(string checkboxId)
        {
            var idToLabel = new Dictionary<string, string>
            {
                { "checkbox1", "南方电网" },
                { "checkbox2", "广东电网公司" },
                { "checkbox3", "广西电网公司" },
                { "checkbox4", "云南电网公司" },
                { "checkbox5", "贵州电网公司" },
                { "checkbox6", "海南电网公司" },
                { "checkbox7", "深圳供电局" },
                { "checkbox8", "南网超高压公司" },
                { "checkbox50", "南网储能公司" },
                { "checkbox51", "南网产业投资集团" },
                { "checkbox52", "鼎元资产公司" },
                { "checkbox53", "南网能源公司" },
                { "checkbox54", "南网国际（香港）公司" },
                { "checkbox55", "南网澜湄国际公司" },
                { "checkbox56", "南网资本控股公司" },
                { "checkbox57", "南网财务公司" },
                { "checkbox58", "鼎和保险公司" },
                { "checkbox59", "南网党校" },
                { "checkbox60", "南网北京分公司" },
                { "checkbox61", "南网共享公司" },
                { "checkbox62", "南网生态运营公司" },
                { "checkbox63", "南网数字集团" },
                { "checkbox64", "南网供应链集团" },
                { "checkbox65", "南网能源院" },
                { "checkbox66", "南网科研院" },
                { "checkbox67", "广州电力交易中心" },
                { "checkbox68", "南网传媒公司" },
                { "checkbox69", "北京研究院" }
            };
            
            return idToLabel.ContainsKey(checkboxId) ? idToLabel[checkboxId] : string.Empty;
        }

        public List<string> GetSelectedEnterprises()
        {
            return EnterpriseCheckboxStateManager.GetCheckedItems();
        }
    }
}