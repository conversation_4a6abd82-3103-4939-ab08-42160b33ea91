﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public partial class FeedbackForm : Form
    {
        public FeedbackForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "Feedback.html");
                webView21.Source = new Uri(htmlFilePath);

                webView21.NavigationCompleted += WebView_NavigationCompleted;

                //MessageBox.Show("用户协议WebView2初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                //MessageBox.Show("用户协议页面加载完成");
            }
            else
            {
                MessageBox.Show("用户协议页面加载失败");
            }
        }
    }
}
