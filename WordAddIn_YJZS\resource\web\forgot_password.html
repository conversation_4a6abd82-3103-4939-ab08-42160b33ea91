<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
            transition: all 0.3s ease;
        }

        h2 {
            color: #4a90e2;
            margin-bottom: 20px;
            font-size: 24px;
            font-weight: 700;
        }

        p {
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
            width: 100%;
        }

            .form-group input {
                width: 100%;
                padding: 15px;
                border: 1px solid rgba(74, 144, 226, 0.3);
                border-radius: 10px;
                font-size: 14px;
                box-sizing: border-box;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.9);
            }

                .form-group input:focus {
                    outline: none;
                    border-color: #4a90e2;
                    box-shadow: 0 0 10px rgba(74, 144, 226, 0.2);
                }

        button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

            button:hover {
                background: linear-gradient(45deg, #3a80d2, #53a3dd);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
            }
    </style>
</head>
<body>
    <div class="container">
        <h2>忘记密码</h2>
        <p>请输入您的电子邮件地址，我们将向您发送重置密码的链接。</p>
        <form id="resetPasswordForm">
            <div class="form-group">
                <input type="text" id="username" placeholder="用户名" required>
            </div>
            <div class="form-group">
                <input type="password" id="old_password" placeholder="输入新密码" required>
            </div>
            <div class="form-group">
                <input type="password" id="new_password" placeholder="再次输入新密码" required>
            </div>
            <button type="submit">重置密码</button>
        </form>
    </div>
    <script>
        document.getElementById('resetPasswordForm').addEventListener('submit', function (event) {
            event.preventDefault();

            const formData = {
                username: document.getElementById('username').value,
                old_password: document.getElementById('old_password').value,
                new_password: document.getElementById('new_password').value,
            };

            // 发送表单数据给C# WebView2
            window.chrome.webview.postMessage(formData);
        });

        // 接收来自C#的响应消息
        window.chrome.webview.addEventListener('message', event => {
            const response = JSON.parse(event.data);
            if (response.result) {
                alert("密码重置成功：" + response.message);
            } else if (response.error) {
                alert("错误：" + response.error);
            }
        });
    </script>
</body>
</html>