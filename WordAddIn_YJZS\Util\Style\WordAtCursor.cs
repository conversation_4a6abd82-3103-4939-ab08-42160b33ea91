﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Word = Microsoft.Office.Interop.Word;
using Task = System.Threading.Tasks.Task;
using System.IO;
using Microsoft.Office.Interop.Word;

namespace WordAddIn_YJZS
{
    public class WordAtCursor
    {
        private readonly ImageAtCursor imageAtCursor;

        public WordAtCursor()
        {
            imageAtCursor = new ImageAtCursor();
        }

        // 定义内容类型枚举
        public enum ContentType
        {
            Font21,             // font21
            Font31,             // font31
            Font41,             // font41
            Font51,             // font51
            Title1,             // 标题 1
            Title2,             // 标题 2
            Title3,             // 标题 3
            Title4,             // 标题 4
            Title5,             // 标题 5
            Title6,             // 标题 6
            Title7,             // 标题 7
            Title8,             // 标题 8
            Title9,             // 标题 9
            StandardBody,       // 标准正文
            TableText,          // 表格文字样式
            DepartmentText,     // 部门正文
            AuthorInfo,         // 封面"EPC联合体"中文文字
            CoverSecondTitle,   // 封面二级标题文字
            CoverFisrtTitle,    // 封面一级标题文字
            CoverSubtitle,      // 封面副标题
            CoverDateLocTime,   // 封面日期地点时间
            GraphTitle,         // 附图标题
            SubTitle,           // 副标题
            Overview,           // 概述
            Comment,            // 批注框文本
            Emphasize,          // 强调
            Annotation,         // 题注
            NormalTableText,    // 通用表格字体
            Pivot,              // 要点
            Header,             // 页眉
            MainText,           // 正文
            MainText2,          // 正文文本
            MainTextChar,       // 正文文本Char
            MainTextFirstLine,  // 正文文本首行
            MainText0Indent,    // 正文无缩进
            ListParagraph,      // 列表段落
            MainTitle,          // 标题
            UnobviousEmphasize, // 不明显强调
            ObviousEmphasize,   // 明显强调
            UnobviousReference, // 不明显参考
            ObviousReference,   // 明显参考
            BookTitle,          // 书籍标题
            TableTextLeft,      // 表格文字 居左
            ListParagraph1,     // 列出段落1
            ListParagraph11,    // 列出段落11
            TOC1,               // TOC 1
            TOC2,               // TOC 2
            TOC3,               // TOC 3
            TOC4,               // TOC 4
            TOC5,               // TOC 5
            TOC6,               // TOC 6
            TOC7,               // TOC 7
            TOC8,               // TOC 8
            TOC9,               // TOC 9
            AnnotationReference,// 批注引用
            Footer,             // 页脚
        }

        // 在光标位置插入内容的方法
        public async Task InsertSectionAtCursor(Word.Document doc,
            Func<Task<List<ContentItem>>> contentProvider,
            ContentType contentType = ContentType.MainText2)
        {
            try
            {
                // 获取当前光标位置
                Word.Selection currentSelection = doc.Application.Selection;
                Word.Range insertRange = currentSelection.Range;

                // 获取并插入内容
                var contentItems = await contentProvider();
                if (contentItems != null && contentItems.Any())
                {
                    // 分类内容
                    var normalContents = contentItems.Where(item =>
                        item.style != "-5" && item.style != "-1" &&
                        item.style != "-3" && item.style != "-6").ToList();
                    var comments = contentItems.Where(item => item.style == "-5").ToList();
                    var images = contentItems.Where(item => item.style == "-1").ToList();
                    var tables = contentItems.Where(item => item.style == "-3").ToList();
                    var listparagraph = contentItems.Where(item => item.style == "-6").ToList();

                    // 插入普通文本内容
                    foreach (var item in normalContents)
                    {
                        Word.Paragraph para = doc.Paragraphs.Add(insertRange);
                        para.Range.Text = item.GetContentAsString();

                        ContentType type = GetContentTypeFromStyleString(item.style);
                        string style = GetStyleForContentType(type);

                        try
                        {
                            para.Range.set_Style(style);

                            // 处理对应的批注
                            if (comments.Any())
                            {
                                foreach (var comment in comments)
                                {
                                    para.Range.Comments.Add(para.Range, comment.GetContentAsString());
                                }
                            }
                        }
                        catch
                        {
                            para.Range.set_Style("正文");
                        }

                        // 更新插入位置到段落末尾
                        insertRange = doc.Range(para.Range.End, para.Range.End);
                    }

                    // 处理列表段落
                    foreach (var item in listparagraph)
                    {
                        // 在插入新段落前，先保存当前位置
                        object start = insertRange.Start;

                        Word.Paragraph para = doc.Paragraphs.Add(insertRange);
                        para.Range.Text = item.GetContentAsString();

                        try
                        {
                            // 应用正文样式
                            para.Range.set_Style("正文文本");

                            // 获取默认的编号模板
                            Word.ListTemplate listTemplate =
                                doc.Application.ListGalleries[Word.WdListGalleryType.wdNumberGallery].ListTemplates[1];

                            // 应用编号
                            para.Range.ListFormat.ApplyListTemplate(
                                listTemplate,
                                true,  // 继续前一个列表的编号
                                Word.WdListApplyTo.wdListApplyToWholeList);

                            // 处理对应的批注
                            if (comments.Any())
                            {
                                foreach (var comment in comments)
                                {
                                    para.Range.Comments.Add(para.Range, comment.GetContentAsString());
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            para.Range.set_Style("正文");
                            Console.WriteLine($"多级列表编号应用失败: {ex.Message}");
                        }

                        // 确保新段落插入在正确的位置
                        para.Range.InsertParagraphAfter();

                        // 更新插入位置到新段落的末尾
                        object end = para.Range.End;
                        insertRange = doc.Range(ref end, ref end);

                        // 确保光标位置正确
                        insertRange.Select();
                    }

                    // 处理表格
                    foreach (var table in tables)
                    {
                        try
                        {
                            var tableData = table.GetTableContent();
                            if (tableData != null && tableData.Any())
                            {
                                InsertTableAtRange(doc, tableData, insertRange, ContentType.TableText);
                                insertRange = doc.Range(insertRange.End, insertRange.End);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"处理表格数据时出错: {ex.Message}");
                        }
                    }

                    // 处理图片
                    foreach (var image in images)
                    {
                        try
                        {
                            string imagePath = image.content.ToString();
                            if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                            {
                                imageAtCursor.InsertImageEnhanced(
                                    doc,
                                    imagePath,
                                    position: ImageAtCursor.ImagePosition.Center,
                                    wrapping: ImageAtCursor.TextWrapping.InLine,
                                    description: null,
                                    insertRange: insertRange
                                );
                                insertRange = doc.Range(insertRange.End, insertRange.End);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"插入图片时出错: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"在光标位置插入内容时发生错误：{ex.Message}", ex);
            }
        }

        // 在指定范围插入表格的辅助方法
        private void InsertTableAtRange(Word.Document doc, List<List<string>> tableData,
            Word.Range insertRange, ContentType style)
        {
            if (tableData == null || !tableData.Any()) return;

            var rows = tableData;
            var columns = rows.FirstOrDefault()?.Count ?? 0;
            if (columns == 0) return;

            Word.Table table = doc.Tables.Add(
                insertRange,
                rows.Count,
                columns);

            // 填充表格数据
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                for (int j = 0; j < Math.Min(row.Count, columns); j++)
                {
                    var content = row[j]?.Trim('"', ' ', '\r', '\n') ?? string.Empty;
                    table.Cell(i + 1, j + 1).Range.Text = content;
                }
            }

            // 设置表格样式
            table.Borders.Enable = 1;
            table.Range.set_Style(GetStyleForContentType(style));

            // 设置表头样式
            if (rows.Count > 0)
            {
                for (int i = 0; i < Math.Min(2, rows.Count); i++)
                {
                    Word.Range headerRow = table.Rows[i + 1].Range;
                    headerRow.Bold = 1;
                    headerRow.ParagraphFormat.Alignment =
                        Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }

            table.AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitContent);
            table.Range.ParagraphFormat.Alignment =
                Word.WdParagraphAlignment.wdAlignParagraphCenter;
        }

        // 将后端返回的style字符串转换为ContentType
        private ContentType GetContentTypeFromStyleString(string style)
        {
            switch (style)
            {
                case "0":
                    return ContentType.MainText2;
                case "1":
                    return ContentType.Title1;
                case "2":
                    return ContentType.Title2;
                case "3":
                    return ContentType.Title3;
                case "4":
                    return ContentType.Title4;
                case "5":
                    return ContentType.Title5;
                case "6":
                    return ContentType.Title6;
                case "7":
                    return ContentType.Title7;
                case "8":
                    return ContentType.Title8;
                case "9":
                    return ContentType.Title9;
                //case "-1":
                //    return ContentType.Overview;//图片格式
                case "-2":
                    return ContentType.GraphTitle;//附图标题
                case "-3":
                    return ContentType.NormalTableText;//通用表格字体
                //case "-4":
                //    return ContentType.NormalTableText;//表格标题
                case "-5":
                    return ContentType.AnnotationReference;//批注引用
                default:
                    return ContentType.MainText2;
            }
        }

        public async Task InsertSection<T>(Word.Document doc, string title, Func<Task<T>> contentProvider,
            ContentType titleType = ContentType.MainText, ContentType contentType = ContentType.MainText)
        {
            try
            {
                // 如果有标题，无论内容是否为空都应该插入
                if (!string.IsNullOrEmpty(title))
                {
                    T content = await contentProvider();
                    InsertTextToDocument(doc, title, content, titleType, contentType);
                }
                else
                {
                    // 如果没有标题，则检查内容
                    T content = await contentProvider();
                    if (content == null ||
                        (content is string str && string.IsNullOrEmpty(str)) ||
                        (content is IEnumerable<IEnumerable<string>> table && !table.Any()))
                    {
                        return;
                    }
                    InsertTextToDocument(doc, title, content, titleType, contentType);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"插入 '{title}' 部分时发生错误：{ex.Message}");
                throw;
            }
        }

        private void InsertTextToDocument<T>(Word.Document doc, string title, T content,
            ContentType titleType = ContentType.MainText, ContentType contentType = ContentType.MainText)
        {
            try
            {
                object what = Word.WdGoToItem.wdGoToLine;
                object which = Word.WdGoToDirection.wdGoToLast;
                doc.GoTo(ref what, ref which);

                // 处理标题（包含换行符的情况）
                if (title != null) // 改为 != null 而不是 !IsNullOrEmpty
                {
                    // 处理换行符
                    if (title.StartsWith("\n"))
                    {
                        int lineCount = title.TakeWhile(c => c == '\n').Count();
                        for (int i = 0; i < lineCount; i++)
                        {
                            Word.Paragraph emptyPara = doc.Content.Paragraphs.Add();
                            emptyPara.Range.Text = "";
                            emptyPara.Range.InsertParagraphAfter();
                        }
                        title = title.TrimStart('\n', '\r');
                    }

                    if (!string.IsNullOrWhiteSpace(title))
                    {
                        Word.Paragraph titlePara = doc.Content.Paragraphs.Add();
                        titlePara.Range.Text = title;
                        string titleStyle = GetStyleForContentType(titleType, title);
                        try
                        {
                            titlePara.Range.set_Style(titleStyle);

                        }
                        catch
                        {
                            titlePara.Range.set_Style("正文");
                            Console.WriteLine($"样式 '{titleStyle}' 应用失败，使用默认样式");
                        }
                        titlePara.Range.InsertParagraphAfter();
                    }
                }

                // 处理内容（只在内容不为空时处理）
                if (content != null && !string.IsNullOrWhiteSpace(content.ToString()))
                {
                    string contentText = content.ToString().Trim();
                    if (!string.IsNullOrEmpty(contentText))
                    {
                        Word.Paragraph contentPara = doc.Content.Paragraphs.Add();
                        contentPara.Range.Text = contentText;
                        string contentStyle = GetStyleForContentType(contentType);
                        try
                        {
                            contentPara.Range.set_Style(contentStyle);


                            // 特殊样式处理
                            if (contentType == ContentType.CoverFisrtTitle ||
                                contentType == ContentType.AuthorInfo)
                            {
                                contentPara.Range.ParagraphFormat.Alignment =
                                    Word.WdParagraphAlignment.wdAlignParagraphCenter;
                            }
                        }
                        catch
                        {
                            contentPara.Range.set_Style("正文");
                            Console.WriteLine($"样式 '{contentStyle}' 应用失败，使用默认样式");
                        }
                        contentPara.Range.InsertParagraphAfter();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"插入文本到文档时发生错误：{ex.Message}", ex);
            }
        }

        private void InsertTable(Word.Document doc, List<List<string>> tableData, ContentType style)
        {
            if (tableData == null || !tableData.Any()) return;

            var rows = tableData;
            var columns = rows.FirstOrDefault()?.Count ?? 0;

            if (columns == 0) return;

            // 创建表格
            Word.Paragraph tablePara = doc.Content.Paragraphs.Add();
            Word.Range tableRange = tablePara.Range;

            Word.Table table = doc.Tables.Add(
                tableRange,
                rows.Count,
                columns);

            // 填充表格数据
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                for (int j = 0; j < Math.Min(row.Count, columns); j++)
                {
                    var content = row[j]?.Trim('"', ' ', '\r', '\n') ?? string.Empty;
                    table.Cell(i + 1, j + 1).Range.Text = content;
                }
            }

            // 设置表格样式
            table.Borders.Enable = 1;
            table.Range.set_Style(GetStyleForContentType(style));

            // 设置表头样式（前两行）
            if (rows.Count > 0)
            {
                for (int i = 0; i < Math.Min(2, rows.Count); i++)
                {
                    Word.Range headerRow = table.Rows[i + 1].Range;
                    headerRow.Bold = 1;
                    headerRow.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }

            // 自动调整列宽
            table.AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitContent);

            // 设置表格整体居中
            table.Range.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;

            // 在表格后添加空行
            doc.Paragraphs.Add();
        }

        private string GetStyleForContentType(ContentType type, string text = "")
        {
            switch (type)
            {

                case ContentType.Font21:
                    return "font21";

                case ContentType.Font31:
                    return "font31";

                case ContentType.Font41:
                    return "font41";

                case ContentType.Font51:
                    return "font51";

                case ContentType.Title1:
                    return "标题 1";

                case ContentType.Title2:
                    return "标题 2";

                case ContentType.Title3:
                    return "标题 3";

                case ContentType.Title4:
                    return "标题 4";

                case ContentType.Title5:
                    return "标题 5";

                case ContentType.Title6:
                    return "标题 6";

                case ContentType.Title7:
                    return "标题 7";

                case ContentType.Title8:
                    return "标题 8";

                case ContentType.Title9:
                    return "标题 9";

                case ContentType.StandardBody:
                    return "标准正文";

                case ContentType.TableText:
                    return "表格文字样式";

                case ContentType.DepartmentText:
                    return "部门正文";

                case ContentType.AuthorInfo:
                    return "封面“EPC联合体”中文文字";

                case ContentType.CoverSecondTitle:
                    return "封面二级标题文字";

                case ContentType.CoverSubtitle:
                    return "封面副标题";

                case ContentType.CoverDateLocTime:
                    return "封面日期地点时间";

                case ContentType.CoverFisrtTitle:
                    return "封面一级标题文字";

                case ContentType.GraphTitle:
                    return "附图标题";

                case ContentType.SubTitle:
                    return "副标题";

                case ContentType.Overview:
                    return "概述";

                case ContentType.Comment:
                    return "批注框文本";

                case ContentType.Emphasize:
                    return "强调";

                case ContentType.Annotation:
                    return "题注";

                case ContentType.NormalTableText:
                    return "通用表格字体";

                case ContentType.Pivot:
                    return "要点";

                case ContentType.Header:
                    return "页眉";

                case ContentType.MainText:
                    return "正文";

                case ContentType.MainText2:
                    return "正文文本";

                case ContentType.MainTextChar:
                    return "正文文本Char";

                case ContentType.MainTextFirstLine:
                    return "正文文本首行";

                case ContentType.MainText0Indent:
                    return "正文无缩进";

                case ContentType.ListParagraph:
                    return "列表段落";

                case ContentType.MainTitle:
                    return "标题";

                case ContentType.UnobviousEmphasize:
                    return "不明显强调";

                case ContentType.ObviousEmphasize:
                    return "明显强调";

                case ContentType.UnobviousReference:
                    return "不明显参考";

                case ContentType.ObviousReference:
                    return "明显参考";

                case ContentType.BookTitle:
                    return "书籍标题";

                case ContentType.TableTextLeft:
                    return "表格文字 居左";

                case ContentType.ListParagraph1:
                    return "列出段落1";

                case ContentType.ListParagraph11:
                    return "列出段落11";

                case ContentType.TOC1:
                    return "TOC 1";

                case ContentType.TOC2:
                    return "TOC 2";

                case ContentType.TOC3:
                    return "TOC 3";

                case ContentType.TOC4:
                    return "TOC 4";

                case ContentType.TOC5:
                    return "TOC 5";

                case ContentType.TOC6:
                    return "TOC 6";

                case ContentType.TOC7:
                    return "TOC 7";

                case ContentType.TOC8:
                    return "TOC 8";

                case ContentType.TOC9:
                    return "TOC 9";

                case ContentType.AnnotationReference:
                    return "批注引用";

                case ContentType.Footer:
                    return "页脚";

                default:
                    return "正文";
            }
        }

        public void SetHeaderAndFooter(Word.Document doc)
        {
            try
            {
                foreach (Word.Section section in doc.Sections)
                {
                    Word.Range footerRange = section.Footers[Word.WdHeaderFooterIndex.wdHeaderFooterPrimary].Range;
                    object pageNumber = Word.WdFieldType.wdFieldPage;
                    footerRange.Fields.Add(footerRange, ref pageNumber);

                    footerRange.set_Style("页脚");
                    //居中
                    //footerRange.ParagraphFormat.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"设置页眉页脚时发生错误：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 插入检查报告内容
        /// </summary>
        /// <param name="doc">Word文档对象</param>
        /// <param name="reportItems">检查报告项列表</param>
        public void InsertCheckReport(Word.Document doc, List<CheckReportApiService.CheckReportItem> reportItems)
        {
            try
            {
                if (reportItems == null || reportItems.Count == 0)
                    return;

                // 移动到文档末尾
                object what = Word.WdGoToItem.wdGoToLine;
                object which = Word.WdGoToDirection.wdGoToLast;
                doc.GoTo(ref what, ref which);

                // 插入检查报告标题
                Word.Paragraph titlePara = doc.Content.Paragraphs.Add();
                titlePara.Range.Text = "检查报告";
                try
                {
                    titlePara.Range.set_Style("标题 1");
                }
                catch
                {
                    titlePara.Range.set_Style("正文");
                    titlePara.Range.Font.Size = 16;
                    titlePara.Range.Font.Bold = 1;
                }
                titlePara.Range.InsertParagraphAfter();

                // 插入每个检查报告项
                foreach (var item in reportItems)
                {
                    Word.Paragraph para = doc.Content.Paragraphs.Add();
                    para.Range.Text = item.Content;

                    try
                    {
                        // 应用正文样式
                        para.Range.set_Style("正文");

                        // 如果缺失标志为true，设置文字颜色为红色
                        if (item.MissingFlag)
                        {
                            para.Range.Font.Color = Word.WdColor.wdColorRed;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"设置检查报告项样式时出错: {ex.Message}");
                        // 如果样式设置失败，至少确保缺失项为红色
                        if (item.MissingFlag)
                        {
                            try
                            {
                                para.Range.Font.Color = Word.WdColor.wdColorRed;
                            }
                            catch (Exception colorEx)
                            {
                                Console.WriteLine($"设置红色字体时出错: {colorEx.Message}");
                            }
                        }
                    }

                    para.Range.InsertParagraphAfter();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"插入检查报告时发生错误：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 插入分节符
        /// </summary>
        /// <param name="doc">Word文档对象</param>
        /// <param name="breakType">分节符类型，默认为下一页</param>
        public void InsertSectionBreak(Word.Document doc, Word.WdBreakType breakType = Word.WdBreakType.wdSectionBreakNextPage)
        {
            try
            {
                object what = Word.WdGoToItem.wdGoToLine;
                object which = Word.WdGoToDirection.wdGoToLast;
                doc.GoTo(ref what, ref which);
                Word.Range range = doc.Range(doc.Content.End - 1, doc.Content.End - 1);
                range.InsertBreak(breakType);
            }
            catch (Exception ex)
            {
                throw new Exception($"插入分节符时发生错误：{ex.Message}", ex);
            }
        }
    }
}
