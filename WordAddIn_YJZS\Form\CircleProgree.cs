using System;
using System.Windows.Forms;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using Task = System.Threading.Tasks.Task;

namespace WordAddIn_YJZS
{
    public partial class CircleProgree : Form
    {
        // 注释掉遮罩相关字段 - 移除遮罩功能但保留进度条
        // private Form blockForm;
        private Timer positionTimer;
        private IntPtr wordHandle;
        private IntPtr excelHandle;
        private ArcProgressBar arcProgressBar;
        // private bool isUpdatingZOrder = false;
        // private DateTime lastZOrderUpdate = DateTime.MinValue;

        // 保留Excel覆盖功能字段以备将来使用，当前在非遮罩模式下暂未使用
#pragma warning disable CS0414 // 字段已被赋值但从未使用过它的值
        private bool shouldCoverExcel = false;
#pragma warning restore CS0414

        // 添加取消操作相关的字段
        private Button cancelButton;
        public event EventHandler CancelRequested;
        private bool isCancelled = false;

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, ref RECT rect);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern int GetWindowThreadProcessId(IntPtr hWnd, out int processId);

        [DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr SetParent(IntPtr hWndChild, IntPtr hWndNewParent);

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern bool GetWindowPlacement(IntPtr hWnd, ref WINDOWPLACEMENT lpwndpl);

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        private struct WINDOWPLACEMENT
        {
            public int length;
            public int flags;
            public int showCmd;
            public POINT ptMinPosition;
            public POINT ptMaxPosition;
            public RECT rcNormalPosition;
        }

        private struct POINT
        {
            public int x;
            public int y;
        }

        private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
        private static readonly IntPtr HWND_TOP = new IntPtr(0);
        private static readonly IntPtr HWND_BOTTOM = new IntPtr(1);
        private const uint SWP_NOSIZE = 0x0001;
        private const uint SWP_NOMOVE = 0x0002;
        private const uint SWP_SHOWWINDOW = 0x0040;
        private const uint SWP_NOACTIVATE = 0x0010;
        private const uint SWP_ASYNCWINDOWPOS = 0x4000;
        private const int SW_SHOWMAXIMIZED = 3;

        public CircleProgree()
        {
            InitializeComponent();
            InitializeArcProgressBar();
            // 注释掉遮罩初始化 - 移除遮罩功能但保留进度条
            // InitializeBlockForm();
        }

        private void InitializeArcProgressBar()
        {
            arcProgressBar = new ArcProgressBar
            {
                Name = "arcProgressBar",
                StartColor = Color.FromArgb(0, 122, 204),
                EndColor = Color.FromArgb(0, 122, 204),
                TextColor = Color.FromArgb(51, 51, 51),
                StatusText = "处理中...",
                Size = new Size(320, 320),
                Font = new Font("Microsoft YaHei", 10F),
                Value = 0,
            };

            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                         ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint, true);
            this.DoubleBuffered = true;

            // 使用一个特殊的颜色作为透明键，而不是直接设置透明背景
            Color transparentColor = Color.FromArgb(1, 2, 3); // 一个几乎不可能在界面中出现的颜色
            this.BackColor = transparentColor;
            this.TransparencyKey = transparentColor;

            this.FormBorderStyle = FormBorderStyle.None;
            this.Size = new Size(400, 400);
            this.TopMost = false; // 初始设置为false，将在同步逻辑中动态设置
            this.ShowInTaskbar = false;

            arcProgressBar.Location = new Point(
                (this.ClientSize.Width - arcProgressBar.Width) / 2,
                (this.ClientSize.Height - arcProgressBar.Height) / 2
            );

            this.Controls.Add(arcProgressBar);



            // 初始化取消按钮
            cancelButton = new Button
            {
                Text = "取消生成",
                Size = new Size(150, 30),
                BackColor = Color.FromArgb(220, 220, 220), // 修改为亮灰色
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Microsoft YaHei", 10F),
                Cursor = Cursors.Hand
            };

            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Click += CancelButton_Click;

            // 设置按钮位置在进度条下方
            cancelButton.Location = new Point(
                (this.ClientSize.Width - cancelButton.Width) / 2,
                arcProgressBar.Bottom
            );

            this.Controls.Add(cancelButton);
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("确定要取消生成吗？\n\n取消操作可能需要几秒钟时间，请耐心等待。", "确认取消",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                isCancelled = true;

                // 优化：立即更新按钮状态，提供更好的用户反馈
                cancelButton.Enabled = false;
                cancelButton.Text = "正在取消...";
                cancelButton.BackColor = Color.FromArgb(200, 200, 200); // 灰色背景表示正在处理

                // 立即更新进度条显示取消状态
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await UpdateProgress(0, "正在取消生成...\n\r请稍等，正在安全停止所有操作");

                        // 触发取消事件
                        CancelRequested?.Invoke(this, EventArgs.Empty);

                        // 给用户一些视觉反馈，显示取消正在进行
                        for (int i = 0; i < 3; i++)
                        {
                            if (this.IsDisposed || !this.IsHandleCreated) break;

                            await Task.Delay(500);
                            if (this.InvokeRequired)
                            {
                                this.Invoke(new Action(() =>
                                {
                                    if (!this.IsDisposed && cancelButton != null)
                                    {
                                        cancelButton.Text = $"正在取消{'.' + new string('.', i + 1)}";
                                    }
                                }));
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // 忽略异常，避免在取消过程中出现错误
                    }
                });
            }
        }

        // 添加一个公共方法来检查是否已请求取消
        public bool IsCancellationRequested()
        {
            return isCancelled;
        }

        // 添加一个方法来更新取消状态的显示
        public async Task UpdateCancellationStatus(string message)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(async () => await UpdateCancellationStatus(message)));
                return;
            }

            try
            {
                if (!this.IsDisposed && cancelButton != null)
                {
                    cancelButton.Text = "已取消";
                    cancelButton.BackColor = Color.FromArgb(220, 220, 220);
                }

                // 更新进度条显示
                await UpdateProgress(0, message);
            }
            catch (Exception)
            {
                // 忽略异常，避免在取消过程中出现错误
            }
        }

        // 添加一个方法来重置取消按钮状态（如果需要重新启用）
        public void ResetCancelButton()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(ResetCancelButton));
                return;
            }

            try
            {
                if (!this.IsDisposed && cancelButton != null)
                {
                    isCancelled = false;
                    cancelButton.Enabled = true;
                    cancelButton.Text = "取消生成";
                    cancelButton.BackColor = Color.FromArgb(180, 180, 180); // 恢复原来的灰色
                }
            }
            catch (Exception)
            {
                // 忽略异常
            }
        }

        // 启用Excel覆盖功能
        public void EnableExcelCoverage()
        {
            shouldCoverExcel = true;
            FindExcelWindow();
            System.Diagnostics.Debug.WriteLine($"启用Excel覆盖功能，Excel窗口句柄: {excelHandle}");

            // 立即更新一次窗口状态以适应Excel
            if (excelHandle != IntPtr.Zero)
            {
                UpdateWindowPositionsForExcel();
            }
        }

        // 禁用Excel覆盖功能
        public void DisableExcelCoverage()
        {
            shouldCoverExcel = false;
            excelHandle = IntPtr.Zero;
        }

        // 查找Excel窗口
        private void FindExcelWindow()
        {
            excelHandle = IntPtr.Zero;
            EnumWindows(EnumWindowCallback, IntPtr.Zero);
        }

        private bool EnumWindowCallback(IntPtr hWnd, IntPtr lParam)
        {
            if (!IsWindowVisible(hWnd)) return true;

            StringBuilder className = new StringBuilder(256);
            GetClassName(hWnd, className, className.Capacity);
            string windowClass = className.ToString();

            StringBuilder windowText = new StringBuilder(256);
            GetWindowText(hWnd, windowText, windowText.Capacity);
            string windowTitle = windowText.ToString();

            // 检查是否是Excel窗口
            if (windowClass.Contains("XLMAIN") || windowClass.Contains("Excel") ||
                windowTitle.Contains("Microsoft Excel") || windowTitle.Contains(".xls"))
            {
                excelHandle = hWnd;
                System.Diagnostics.Debug.WriteLine($"找到Excel窗口: {windowTitle} (类名: {windowClass})");
                return false; // 停止枚举
            }

            return true; // 继续枚举
        }

        // 注释掉遮罩初始化方法 - 移除遮罩功能但保留进度条
        /*
        private void InitializeBlockForm()
        {
            blockForm = new Form
            {
                FormBorderStyle = FormBorderStyle.None,
                ShowInTaskbar = false,
                BackColor = Color.Black,
                Opacity = 0.5,
                MinimizeBox = false,
                MaximizeBox = false,
                ControlBox = false,
                Text = string.Empty,
                TopMost = true,
                StartPosition = FormStartPosition.Manual,
                ShowIcon = false,
                TransparencyKey = Color.Empty
            };

            blockForm.Paint += (s, e) =>
            {
                using (SolidBrush brush = new SolidBrush(Color.FromArgb(128, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(brush, blockForm.ClientRectangle);
                }
            };

            Timer focusTimer = new Timer { Interval = 200 };
            focusTimer.Tick += HandleFocusTimerTick;
            focusTimer.Start();

            blockForm.FormClosing += (s, e) => focusTimer.Dispose();
        }
        */

        // 注释掉焦点处理方法 - 移除遮罩功能但保留进度条
        /*
        private void HandleFocusTimerTick(object sender, EventArgs e)
        {
            if (isUpdatingZOrder) return;

            IntPtr foregroundWindow = GetForegroundWindow();
            if (foregroundWindow != IntPtr.Zero)
            {
                StringBuilder className = new StringBuilder(256);
                GetClassName(foregroundWindow, className, className.Capacity);
                string windowClass = className.ToString();

                StringBuilder windowText = new StringBuilder(256);
                GetWindowText(foregroundWindow, windowText, windowText.Capacity);
                string windowTitle = windowText.ToString();

                int foregroundProcessId;
                GetWindowThreadProcessId(foregroundWindow, out foregroundProcessId);

                int wordProcessId;
                GetWindowThreadProcessId(wordHandle, out wordProcessId);

                // 检查是否是MessageBox或错误对话框
                bool isMessageBox = windowClass.Contains("#32770") || windowClass.Contains("MessageBox") ||
                                   windowTitle.Contains("错误") || windowTitle.Contains("警告") ||
                                   windowTitle.Contains("提示") || windowTitle.Contains("确认");

                // 如果是MessageBox，隐藏遮罩但保持进度条显示
                if (isMessageBox)
                {
                    if (blockForm.Visible)
                        blockForm.Hide();
                    return;
                }

                // 判断Word是否最小化
                WINDOWPLACEMENT placement = new WINDOWPLACEMENT();
                placement.length = System.Runtime.InteropServices.Marshal.SizeOf(typeof(WINDOWPLACEMENT));
                bool isWordMinimized = false;
                if (GetWindowPlacement(wordHandle, ref placement))
                {
                    isWordMinimized = (placement.showCmd == 2);
                }

                // 检查Excel是否最小化（如果启用了Excel覆盖）
                bool isExcelMinimized = false;
                if (shouldCoverExcel && excelHandle != IntPtr.Zero)
                {
                    WINDOWPLACEMENT excelPlacement = new WINDOWPLACEMENT();
                    excelPlacement.length = System.Runtime.InteropServices.Marshal.SizeOf(typeof(WINDOWPLACEMENT));
                    if (GetWindowPlacement(excelHandle, ref excelPlacement))
                    {
                        isExcelMinimized = (excelPlacement.showCmd == 2);
                    }
                }

                // 1. Word最小化，进度条和遮罩窗体也最小化并隐藏
                if (isWordMinimized && (!shouldCoverExcel || isExcelMinimized))
                {
                    if (this.Visible)
                        this.Hide();
                    if (blockForm.Visible)
                        blockForm.Hide();
                    return;
                }

                // 2. 检查当前前台窗口是否是Word或Excel
                bool isWordWindow = foregroundProcessId == wordProcessId || foregroundWindow == wordHandle || windowClass.Contains("OpusApp");
                bool isExcelWindow = false;

                if (shouldCoverExcel)
                {
                    // 更新Excel窗口句柄（可能有新的Excel窗口打开）
                    if (excelHandle == IntPtr.Zero || !IsWindowVisible(excelHandle))
                    {
                        FindExcelWindow();
                    }

                    int excelProcessId = 0;
                    if (excelHandle != IntPtr.Zero)
                    {
                        GetWindowThreadProcessId(excelHandle, out excelProcessId);
                        isExcelWindow = foregroundProcessId == excelProcessId || foregroundWindow == excelHandle ||
                                       windowClass.Contains("XLMAIN") || windowClass.Contains("Excel") ||
                                       windowTitle.Contains("Microsoft Excel") || windowTitle.Contains(".xls");
                    }
                }

                // 3. 当前前台窗口是Word或Excel，显示进度条和遮罩窗体
                if (isWordWindow || isExcelWindow)
                {
                    if (!this.Visible)
                        this.Show();
                    if (!blockForm.Visible)
                        blockForm.Show();

                    // 根据当前窗口更新位置
                    if (isExcelWindow && excelHandle != IntPtr.Zero)
                    {
                        UpdateWindowPositionsForExcel();
                    }
                    else
                    {
                        UpdateWindowPositions();
                    }
                }
                else
                {
                    // 4. 当前前台窗口不是Word或Excel，隐藏进度条和遮罩窗体
                    if (this.Visible)
                        this.Hide();
                    if (blockForm.Visible)
                        blockForm.Hide();
                }
            }
        }
        */

        // 改进的窗口位置和状态同步方法
        private void UpdateWindowPositions()
        {
            UpdateWindowPositionsAndSync(wordHandle);
        }

        private void UpdateWindowPositionsForExcel()
        {
            // 当启用Excel覆盖时，需要同时检查Word和Excel窗口状态
            if (shouldCoverExcel && excelHandle != IntPtr.Zero)
            {
                UpdateWindowPositionsAndSyncMultiple();
            }
            else
            {
                UpdateWindowPositionsAndSync(excelHandle);
            }
        }

        // 处理多窗口同步的方法（Word + Excel）
        private void UpdateWindowPositionsAndSyncMultiple()
        {
            try
            {
                // 检查Word窗口状态
                bool isWordMinimized = false;
                bool isWordInForeground = false;

                if (wordHandle != IntPtr.Zero)
                {
                    WINDOWPLACEMENT wordPlacement = new WINDOWPLACEMENT();
                    wordPlacement.length = System.Runtime.InteropServices.Marshal.SizeOf(typeof(WINDOWPLACEMENT));
                    if (GetWindowPlacement(wordHandle, ref wordPlacement))
                    {
                        isWordMinimized = (wordPlacement.showCmd == 2);
                    }

                    IntPtr foregroundWindow = GetForegroundWindow();
                    if (foregroundWindow != IntPtr.Zero)
                    {
                        int foregroundProcessId;
                        GetWindowThreadProcessId(foregroundWindow, out foregroundProcessId);

                        int wordProcessId;
                        GetWindowThreadProcessId(wordHandle, out wordProcessId);

                        isWordInForeground = (foregroundWindow == wordHandle) ||
                                           (foregroundProcessId == wordProcessId) ||
                                           IsOfficeWindow(foregroundWindow);
                    }
                }

                // 检查Excel窗口状态
                bool isExcelMinimized = false;
                bool isExcelInForeground = false;

                if (excelHandle != IntPtr.Zero)
                {
                    WINDOWPLACEMENT excelPlacement = new WINDOWPLACEMENT();
                    excelPlacement.length = System.Runtime.InteropServices.Marshal.SizeOf(typeof(WINDOWPLACEMENT));
                    if (GetWindowPlacement(excelHandle, ref excelPlacement))
                    {
                        isExcelMinimized = (excelPlacement.showCmd == 2);
                    }

                    IntPtr foregroundWindow = GetForegroundWindow();
                    if (foregroundWindow != IntPtr.Zero)
                    {
                        int foregroundProcessId;
                        GetWindowThreadProcessId(foregroundWindow, out foregroundProcessId);

                        int excelProcessId;
                        GetWindowThreadProcessId(excelHandle, out excelProcessId);

                        isExcelInForeground = (foregroundWindow == excelHandle) ||
                                            (foregroundProcessId == excelProcessId) ||
                                            IsOfficeWindow(foregroundWindow);
                    }
                }

                // 决定显示逻辑：任一Office应用在前台且未最小化时显示进度框
                bool shouldShow = (!isWordMinimized && isWordInForeground) ||
                                 (!isExcelMinimized && isExcelInForeground);

                if (shouldShow)
                {
                    if (!this.Visible)
                    {
                        this.Show();
                        System.Diagnostics.Debug.WriteLine("Word或Excel在前台，显示进度框");
                    }

                    if (this.WindowState == FormWindowState.Minimized)
                    {
                        this.WindowState = FormWindowState.Normal;
                    }

                    if (!this.TopMost)
                    {
                        this.TopMost = true;
                    }

                    // 根据当前前台窗口更新位置
                    IntPtr targetHandle = isExcelInForeground ? excelHandle : wordHandle;
                    UpdateProgressBarPosition(targetHandle);
                }
                else
                {
                    if (this.Visible)
                    {
                        this.Hide();
                        System.Diagnostics.Debug.WriteLine("Word和Excel都不在前台，隐藏进度框");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"多窗口同步时出错: {ex.Message}");
            }
        }

        // 单独的位置更新方法
        private void UpdateProgressBarPosition(IntPtr targetHandle)
        {
            if (targetHandle == IntPtr.Zero) return;

            try
            {
                RECT rect = new RECT();
                if (GetWindowRect(targetHandle, ref rect))
                {
                    int centerX = rect.Left + (rect.Right - rect.Left) / 2;
                    int centerY = rect.Top + (rect.Bottom - rect.Top) / 2;

                    Point newLocation = new Point(
                        centerX - this.Width / 2,
                        centerY - this.Height / 2
                    );

                    // 只在位置有明显变化时才更新
                    if (Math.Abs(this.Location.X - newLocation.X) > 5 ||
                        Math.Abs(this.Location.Y - newLocation.Y) > 5)
                    {
                        this.Location = newLocation;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新进度框位置时出错: {ex.Message}");
            }
        }

        private void UpdateWindowPositionsAndSync(IntPtr targetHandle)
        {
            if (targetHandle == IntPtr.Zero) return;

            try
            {
                // 1. 检查目标窗口是否最小化
                WINDOWPLACEMENT placement = new WINDOWPLACEMENT();
                placement.length = System.Runtime.InteropServices.Marshal.SizeOf(typeof(WINDOWPLACEMENT));
                bool isTargetMinimized = false;

                if (GetWindowPlacement(targetHandle, ref placement))
                {
                    isTargetMinimized = (placement.showCmd == 2); // SW_SHOWMINIMIZED
                }

                // 2. 检查当前前台窗口
                IntPtr foregroundWindow = GetForegroundWindow();
                bool isTargetInForeground = false;

                if (foregroundWindow != IntPtr.Zero)
                {
                    // 检查前台窗口是否是目标窗口或其子窗口
                    int foregroundProcessId;
                    GetWindowThreadProcessId(foregroundWindow, out foregroundProcessId);

                    int targetProcessId;
                    GetWindowThreadProcessId(targetHandle, out targetProcessId);

                    // 检查是否是同一个进程的窗口或直接是目标窗口
                    isTargetInForeground = (foregroundWindow == targetHandle) ||
                                         (foregroundProcessId == targetProcessId) ||
                                         IsOfficeWindow(foregroundWindow);
                }

                // 3. 根据窗口状态决定进度框的显示状态
                if (isTargetMinimized)
                {
                    // 目标窗口最小化，隐藏并最小化进度框
                    if (this.Visible)
                    {
                        this.Hide();
                        System.Diagnostics.Debug.WriteLine("目标窗口最小化，隐藏进度框");
                    }
                    return;
                }
                else if (!isTargetInForeground)
                {
                    // 目标窗口不在前台，隐藏进度框但不最小化
                    if (this.Visible)
                    {
                        this.Hide();
                        System.Diagnostics.Debug.WriteLine("目标窗口不在前台，隐藏进度框");
                    }
                    return;
                }
                else
                {
                    // 目标窗口在前台且未最小化，显示进度框
                    if (!this.Visible)
                    {
                        this.Show();
                        System.Diagnostics.Debug.WriteLine("目标窗口在前台，显示进度框");
                    }

                    // 恢复窗口状态
                    if (this.WindowState == FormWindowState.Minimized)
                    {
                        this.WindowState = FormWindowState.Normal;
                    }

                    // 动态设置TopMost属性
                    if (!this.TopMost)
                    {
                        this.TopMost = true;
                    }
                }

                // 4. 更新进度框位置（只在显示状态下）
                if (this.Visible)
                {
                    UpdateProgressBarPosition(targetHandle);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新窗口位置和状态时出错: {ex.Message}");
            }
        }

        // 检查窗口是否是Office相关窗口
        private bool IsOfficeWindow(IntPtr hWnd)
        {
            try
            {
                StringBuilder className = new StringBuilder(256);
                GetClassName(hWnd, className, className.Capacity);
                string windowClass = className.ToString();

                StringBuilder windowText = new StringBuilder(256);
                GetWindowText(hWnd, windowText, windowText.Capacity);
                string windowTitle = windowText.ToString();

                // 检查是否是Word窗口
                bool isWordWindow = windowClass.Contains("OpusApp") ||
                                   windowTitle.Contains("Microsoft Word") ||
                                   windowTitle.Contains("Word");

                // 检查是否是Excel窗口
                bool isExcelWindow = windowClass.Contains("XLMAIN") ||
                                    windowClass.Contains("Excel") ||
                                    windowTitle.Contains("Microsoft Excel") ||
                                    windowTitle.Contains(".xls");

                return isWordWindow || isExcelWindow;
            }
            catch
            {
                return false;
            }
        }

        public void Show(IntPtr? specificWordHandle = null)
        {
            wordHandle = specificWordHandle ?? (IntPtr)Globals.ThisAddIn.Application.ActiveWindow.Hwnd;
            System.Diagnostics.Debug.WriteLine($"显示进度框，目标窗口句柄: {wordHandle}");

            RECT rect = new RECT();
            GetWindowRect(wordHandle, ref rect);

            this.Size = new Size(400, 400);

            int centerX = rect.Left + (rect.Right - rect.Left) / 2;
            int centerY = rect.Top + (rect.Bottom - rect.Top) / 2;

            this.Location = new Point(
                centerX - this.Width / 2,
                centerY - this.Height / 2
            );

            // 检查目标窗口是否在前台，决定是否设置TopMost
            IntPtr foregroundWindow = GetForegroundWindow();
            bool shouldShowTopMost = false;

            if (foregroundWindow != IntPtr.Zero)
            {
                int foregroundProcessId;
                GetWindowThreadProcessId(foregroundWindow, out foregroundProcessId);

                int targetProcessId;
                GetWindowThreadProcessId(wordHandle, out targetProcessId);

                shouldShowTopMost = (foregroundWindow == wordHandle) ||
                                   (foregroundProcessId == targetProcessId) ||
                                   IsOfficeWindow(foregroundWindow);
            }

            this.TopMost = shouldShowTopMost;
            base.Show();

            UpdateWindowPositions();
            StartPositionMonitor();
        }

        // 新增：显示并启用Excel覆盖
        public void ShowWithExcelCoverage(IntPtr? specificWordHandle = null)
        {
            Show(specificWordHandle);
            EnableExcelCoverage();
        }

        public Task UpdateProgress(double progress, string status)
        {
            if (this.IsDisposed || arcProgressBar == null || arcProgressBar.IsDisposed)
                return Task.CompletedTask;

            if (InvokeRequired)
                return (Task)Invoke(new Func<double, string, Task>(UpdateProgress), progress, status);

            try
            {
                int progressValue = (int)Math.Max(0, Math.Min(100, progress));
                arcProgressBar.SuspendLayout();
                arcProgressBar._value = progressValue;
                arcProgressBar._currentAngle = 360f * progressValue / 100f;
                arcProgressBar.StatusText = status;

                if (arcProgressBar._animationTimer != null && arcProgressBar._animationTimer.Enabled)
                    arcProgressBar._animationTimer.Stop();

                arcProgressBar.ResumeLayout();
                arcProgressBar.Refresh();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新进度条时出错: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        private void StartPositionMonitor()
        {
            positionTimer?.Dispose();
            positionTimer = new Timer { Interval = 150 }; // 提高响应性，从200ms改为150ms
            positionTimer.Tick += (s, e) => UpdateWindowPositions();
            positionTimer.Start();
            System.Diagnostics.Debug.WriteLine("启动窗口位置监控，间隔150ms");
        }

        // 添加方法来优雅地隐藏进度框（取消TopMost状态）
        public new void Hide()
        {
            if (this.TopMost)
            {
                this.TopMost = false;
                System.Diagnostics.Debug.WriteLine("隐藏进度框并取消TopMost状态");
            }
            base.Hide();
        }

        public new void Close()
        {
            positionTimer?.Dispose();
            if (this.TopMost)
            {
                this.TopMost = false;
            }
            System.Diagnostics.Debug.WriteLine("关闭进度框");
            base.Close();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                components?.Dispose();
                positionTimer?.Dispose();
                // 移除遮罩释放
                // blockForm?.Dispose();
            }
            base.Dispose(disposing);
        }

        public static DialogResult ShowMessageBox(string text, string caption = "",
            MessageBoxButtons buttons = MessageBoxButtons.OK,
            MessageBoxIcon icon = MessageBoxIcon.None)
        {
            return MessageBox.Show(text, caption, buttons, icon,
                MessageBoxDefaultButton.Button1,
                MessageBoxOptions.DefaultDesktopOnly);
        }
    }
}