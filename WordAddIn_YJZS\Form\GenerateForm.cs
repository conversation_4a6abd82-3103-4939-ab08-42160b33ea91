﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;
using Task = System.Threading.Tasks.Task;

namespace WordAddIn_YJZS
{
    public partial class GenerateForm : Form
    {
        private List<string> uploadedFiles;
        // 添加事件委托
        public delegate void CheckboxStateChangedHandler(List<string> checkedItems);
        public event CheckboxStateChangedHandler EnterpriseStateChanged;
        public event CheckboxStateChangedHandler MenuStateChanged;
        private string selectedProjectName;  // 添加项目名称字段

        private Ribbon_YJZS ribbonReference;  // 添加对 Ribbon 的引用

        public GenerateForm(string projectName, Ribbon_YJZS ribbon)
        {
            InitializeComponent();
            InitializeWebView2();       
            selectedProjectName = projectName;
            ribbonReference = ribbon;

            // 获取已上传文件列表
            if (ribbonReference != null)
            {
                uploadedFiles = ribbonReference.GetUploadedFiles();
            }
            else
            {
                uploadedFiles = new List<string>();
            }
        }

        // 页面加载完成后初始化
        private async void GenerateForm_Load(object sender, EventArgs e)
        {
            // 等待 WebView2 初始化完成
            if (webView21 != null && webView21.CoreWebView2 != null)
            {
                // 页面加载完成后获取文件列表
                await Task.Delay(1000); // 给页面一些加载时间
                await GetAndUpdateFileList();
            }
        }

        private async void InitializeWebView2()
        {
            try
            {
                string userDataFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "WebView2UserData"
                );
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);
                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "resource/web",
                    "Generate.html"
                );
                webView21.Source = new Uri(htmlFilePath);

                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                webView21.NavigationCompleted += WebView_NavigationCompleted;

                
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private async void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                try
                {
                    // 同步 checkbox1 和 editbox1 的初始状态
                    UpdateEstimateCheckboxState(ribbonReference.GetCheckBox1Pressed(null));
                    UpdateExpectMoneyValue(ribbonReference.GetEditBox1Text(null));

                    // 设置项目名称
                    if (webView21?.CoreWebView2 != null)
                    {
                        string escapedProjectName = selectedProjectName?.Replace("'", "\\'") ?? string.Empty;
                        await webView21.CoreWebView2.ExecuteScriptAsync($"updateProjectName('{escapedProjectName}')");
                    }

                    if (uploadedFiles != null)
                    {
                        await UpdateFileList(uploadedFiles);
                    }

                    // 同步企业复选框状态
                    SyncEnterpriseCheckboxes();

                    // 同步菜单复选框状态
                    SyncMenuCheckboxes();

                    // 恢复表单状态
                    if (GlobalParameters.CalculateData != null)
                    {
                        UpdateFormData(GlobalParameters.CalculateData);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"WebView_NavigationCompleted 出错: {ex.Message}");
                }
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                string messageJson = e.WebMessageAsJson;
                Console.WriteLine($"收到WebView消息: {messageJson}");

                // 尝试解析为简单的字典格式
                var simpleMessage = JsonConvert.DeserializeObject<Dictionary<string, string>>(messageJson);
                if (simpleMessage != null && simpleMessage.ContainsKey("action"))
                {
                    string action = simpleMessage["action"];
                    Console.WriteLine($"处理WebView动作: {action}");

                    switch (action)
                    {
                        case "uploadFiles":
                            HandleFileUploadRequest();
                            return;
                        case "getUploadedFiles":
                            SendUploadedFilesToWebView();
                            return;
                        case "deleteFile":
                            if (simpleMessage.ContainsKey("fileName"))
                            {
                                DeleteFile(simpleMessage["fileName"]);
                            }
                            return;
                    }
                }


                var message = JsonConvert.DeserializeObject<dynamic>(e.WebMessageAsJson);

                if (message == null)
                {
                    MessageBox.Show($"无法解析WebView消息: {messageJson}", "解析错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 检查message是否包含type属性
                if (message.type == null)
                {
                    MessageBox.Show($"WebView消息缺少type属性: {messageJson}", "消息格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                string type = message.type.ToString();

                // 处理项目类型更新
                if (type == "projectUpdate" && message.projectType != null)
                {
                    string projectType = message.projectType.ToString();
                    this.Invoke((MethodInvoker)delegate
                    {
                        ribbonReference.UpdateProjectFromGenerate(projectType);
                    });
                    return; // 处理完项目更新消息后直接返回
                }
                
                // 处理获取初始状态的请求
                if (type == "getInitialState")
                {
                    // 立即同步当前的所有状态
                    SyncEnterpriseCheckboxes();
                    SyncMenuCheckboxes();
                    if (GlobalParameters.CalculateData != null)
                    {
                        UpdateFormData(GlobalParameters.CalculateData);
                    }
                    return;
                }

                switch (type)
                {
                    case "enterprise":
                        {
                            string id = message.id.ToString();
                            bool isChecked = message.@checked;
                            string label = message.label.ToString();

                            // 更新状态管理器
                            EnterpriseCheckboxStateManager.UpdateCheckboxState(id, isChecked, label);

                            // 触发企业选择状态变更事件
                            EnterpriseStateChanged?.Invoke(EnterpriseCheckboxStateManager.GetCheckedItems());

                            // 同步到其他窗体
                            foreach (Form form in Application.OpenForms)
                            {
                                if (form is EnterprisesForm enterprisesForm && !form.IsDisposed)
                                {
                                    enterprisesForm.Invoke(new Action(() => {
                                        if (id.StartsWith("checkbox"))
                                        {
                                            // 直接使用checkbox ID更新
                                            enterprisesForm.UpdateCheckboxState(id, isChecked);
                                        }
                                        else
                                        {
                                            // 尝试通过标签名称查找对应的checkbox
                                            string enterpriseId = GetEnterpriseCheckboxId(label);
                                            if (!string.IsNullOrEmpty(enterpriseId))
                                            {
                                                enterprisesForm.UpdateCheckboxState(enterpriseId, isChecked);
                                            }
                                            else
                                            {
                                                // 如果找不到，直接使用标签
                                                enterprisesForm.UpdateCheckboxState(label, isChecked);
                                            }
                                        }
                                    }));
                                }
                            }
                        }
                        break;

                    case "menu":
                        {
                            string id = message.id.ToString();
                            bool isChecked = message.@checked;
                            string label = message.label.ToString();

                            MenuCheckboxStateManager.UpdateCheckboxState(id, isChecked, label);

                            // 触发菜单选择状态变更事件
                            MenuStateChanged?.Invoke(MenuCheckboxStateManager.GetCheckedItems());

                            // 同步到其他窗体
                            foreach (Form form in Application.OpenForms)
                            {
                                if (form is MenuForm menuForm && !form.IsDisposed)
                                {
                                    menuForm.Invoke(new Action(() => {
                                        if (id.StartsWith("checkbox"))
                                        {
                                            // 直接使用checkbox ID更新
                                            menuForm.UpdateCheckboxState(id, isChecked);
                                        }
                                        else
                                        {
                                            // 尝试通过标签名称查找对应的checkbox
                                            string menuId = GetMenuCheckboxId(label);
                                            if (!string.IsNullOrEmpty(menuId))
                                            {
                                                menuForm.UpdateCheckboxState(menuId, isChecked);
                                            }
                                            else
                                            {
                                                // 如果找不到，直接使用标签
                                                menuForm.UpdateCheckboxState(label, isChecked);
                                            }
                                        }
                                    }));
                                }
                            }
                        }
                        break;

                    case "estimate":
                        {
                            bool isChecked = (bool)message.@checked;
                            ribbonReference.UpdateCheckBox1FromGenerate(isChecked);
                        }
                        break;

                    case "expectMoney":
                        {
                            string value = message.value.ToString();
                            ribbonReference.UpdateEditBox1FromGenerate(value);
                        }
                        break;

                    case "generate":
                        {
                            // 调用 Ribbon 的生成方法
                            if (ribbonReference != null)
                            {
                                ribbonReference.Button29_Click(null);
                            }
                        }
                        break;

                    case "saveFormState":
                        {
                            try
                            {
                                // 首先验证数据的完整性
                                if (message.data == null)
                                {
                                    Console.WriteLine("表单数据不完整，请检查输入");
                                    return;
                                }

                                Console.WriteLine("接收到saveFormState消息，开始处理...");

                                // 安全地解析数据
                                var formData = new CalculateFormData
                                {
                                    Select3 = message.data.select_3?.ToString() ?? "",
                                    Select12 = message.data.select_12?.ToString() ?? "",
                                    Select1 = message.data.select_1?.ToString() ?? "",
                                    Select7 = message.data.select_7?.ToString() ?? "",
                                    Select9 = message.data.select_9?.ToString() ?? "",
                                    Input10 = message.data.input_10 != null && !string.IsNullOrEmpty(message.data.input_10.ToString())
                                        ? Convert.ToDouble(message.data.input_10.ToString())
                                        : 0,
                                    Select11 = message.data.select_11?.ToString() ?? ""
                                };

                                // 验证必填字段
                                if (string.IsNullOrEmpty(formData.Select3))
                                {
                                    Console.WriteLine("请选择项目分类");
                                    return;
                                }

                                // 如果是信息系统建设与升级改造，验证其他必填字段
                                if (formData.Select3 == "信息系统建设与升级改造")
                                {
                                    if (string.IsNullOrEmpty(formData.Select12))
                                    {
                                        MessageBox.Show("请选择建设周期", "必填项验证",
                                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                        return;
                                    }
                                }

                                // 更新全局参数
                                GlobalParameters.CalculateData = formData;
                                Console.WriteLine("全局表单数据已更新");

                                // 同步到EnterprisesForm窗体
                                foreach (Form form in Application.OpenForms)
                                {
                                    if (form is EnterprisesForm enterprisesForm && !form.IsDisposed && form != this)
                                    {
                                        Console.WriteLine("准备同步到EnterprisesForm");
                                        enterprisesForm.Invoke(new Action(() => {
                                            enterprisesForm.UpdateFormData(formData);
                                        }));
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"处理表单数据时出错：{ex.Message}");
                            }
                        }
                        break;

                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理消息时出错: {ex.Message}\n{e.WebMessageAsJson}");
            }
        }

        // 添加更新项目名称标签的方法
        public void UpdateProjectNameLabel(string projectName)
        {
            if (webView21 != null && webView21.CoreWebView2 != null)
            {
                string script = $"document.getElementById('projectNameLabel').textContent = '{projectName}';";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        public void UpdateFormData(CalculateFormData formData)
        {
            if (webView21?.CoreWebView2 == null || formData == null) return;

            try
            {
                Console.WriteLine("GenerateForm.UpdateFormData: 正在更新表单数据");
                
                // 构建完整的状态对象，确保传递所有值，即使是空值
                var stateJson = JsonConvert.SerializeObject(new
                {
                    select_3 = formData.Select3 ?? "",
                    select_12 = formData.Select12 ?? "",
                    select_1 = formData.Select1 ?? "",
                    select_7 = formData.Select7 ?? "",
                    select_9 = formData.Select9 ?? "",
                    input_10 = formData.Input10,
                    select_11 = formData.Select11 ?? ""
                });

                // 构建并执行JavaScript脚本
                string script = $"updateFormState({stateJson});";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
                Console.WriteLine("GenerateForm.UpdateFormData: 表单数据更新请求已发送");
                
                // 同步到其他窗体
                foreach (Form form in Application.OpenForms)
                {
                    if (form is EnterprisesForm enterprisesForm && !form.IsDisposed && form != this)
                    {
                        enterprisesForm.Invoke(new Action(() => {
                            enterprisesForm.UpdateFormData(formData);
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GenerateForm.UpdateFormData: 更新表单数据时出错: {ex.Message}");
            }
        }

        public void UpdateEstimateCheckboxState(bool isChecked)
        {
            if (webView21?.CoreWebView2 != null)
            {
                string script = $"updateEstimateCheckbox({isChecked.ToString().ToLower()})";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        public void UpdateExpectMoneyValue(string value)
        {
            if (webView21?.CoreWebView2 != null)
            {
                string script = $"updateExpectMoney('{value}')";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        public void UpdateEnterpriseCheckboxState(string id, bool isChecked)
        {
            if (webView21?.CoreWebView2 != null)
            {
                string script = $@"
                    var checkbox = document.getElementById('{id}');
                    if (checkbox) {{
                        checkbox.checked = {isChecked.ToString().ToLower()};
                    }}";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        public void UpdateMenuCheckboxState(string id, bool isChecked)
        {
            if (webView21?.CoreWebView2 != null)
            {
                string script = $@"
                    var checkbox = document.getElementById('{id}');
                    if (checkbox) {{
                        checkbox.checked = {isChecked.ToString().ToLower()};
                    }}";
                webView21.CoreWebView2.ExecuteScriptAsync(script);
            }
        }

        // 添加文件上传处理方法
        private async void HandleFileUploadRequest()
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Multiselect = true;
                    openFileDialog.Filter = "Word文档和Excel文件|*.docx;*.doc;*.xlsx;*.xls|Word文档|*.docx;*.doc|Excel文件|*.xlsx;*.xls";
                    openFileDialog.Title = "选择要上传的文件";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示上传进度
                        ProgressForm progressForm = new ProgressForm("正在上传文件");
                        progressForm.Show();

                        try
                        {
                            // 创建进度报告对象
                            var progress = new Progress<int>(percent =>
                            {
                                progressForm.UpdateProgress(percent);
                            });

                            // 调用 Ribbon 中的上传方法
                            var result = await ribbonReference.UploadFilesAsync(openFileDialog.FileNames, progress);

                            if (result != null && result.ContainsKey("status") && result["status"].ToString() == "success")
                            {
                                // 上传成功后，等待一下再获取最新的文件列表
                                await Task.Delay(500);

                                // 获取最新的文件列表
                                uploadedFiles = await ribbonReference.GetUploadedFilesAsync();

                                // 更新 WebView 中的文件列表
                                await UpdateFileListInWebView(uploadedFiles);

                                MessageBox.Show("文件上传成功！", "上传完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            else
                            {
                                string errorMessage = result != null && result.ContainsKey("message")
                                    ? result["message"].ToString()
                                    : "上传失败，请重试";
                                MessageBox.Show(errorMessage, "上传失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                        finally
                        {
                            progressForm.Close();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"上传文件时出错: {ex.Message}", "上传错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 更新 WebView 中的文件列表
        private async Task UpdateFileListInWebView(List<string> files)
        {
            if (webView21?.CoreWebView2 != null)
            {
                string filesJson = JsonConvert.SerializeObject(files);
                await webView21.CoreWebView2.ExecuteScriptAsync($"updateFileList({filesJson});");
            }
        }

        // 获取并更新文件列表
        private async Task GetAndUpdateFileList()
        {
            if (ribbonReference != null)
            {
                uploadedFiles = await ribbonReference.GetUploadedFilesAsync();
                await UpdateFileListInWebView(uploadedFiles);
            }
        }

        // 向 WebView 发送已上传文件列表
        private async void SendUploadedFilesToWebView()
        {
            try
            {
                if (ribbonReference == null)
                {
                    MessageBox.Show("无法访问功能区，请从功能区获取文件列表。", "获取失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 从 Ribbon 获取最新的已上传文件列表
                uploadedFiles = await ribbonReference.GetUploadedFilesAsync();

                if (uploadedFiles != null)
                {
                    string filesJson = JsonConvert.SerializeObject(uploadedFiles);
                    Console.WriteLine($"发送文件列表到WebView: {filesJson}");
                    string script = $"updateFileList({filesJson});";
                    await webView21.CoreWebView2.ExecuteScriptAsync(script);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取已上传文件列表时出错: {ex.Message}");
            }
        }

        // 删除文件
        private async void DeleteFile(string fileName)
        {
            try
            {
                if (ribbonReference == null)
                {
                    MessageBox.Show("无法访问功能区，请从功能区删除文件。", "删除失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示确认对话框
                if (MessageBox.Show($"确定要删除文件 {fileName} 吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // 显示进度条
                    ProgressForm progressForm = new ProgressForm("正在删除文件");
                    progressForm.Show();
                    progressForm.UpdateProgress(50);

                    try
                    {
                        // 调用 FileApiService 删除文件
                        await ribbonReference.DeleteFileAsync(fileName);

                        // 删除成功后更新文件列表
                        await Task.Delay(500); // 等待一下再获取文件列表
                        await GetAndUpdateFileList();

                        // 通知 WebView 删除成功
                        await webView21.CoreWebView2.ExecuteScriptAsync("window.dispatchEvent(new CustomEvent('fileDeleteSuccess'));");

                        progressForm.UpdateProgress(100);
                    }
                    finally
                    {
                        progressForm.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文件时出错: {ex.Message}", "删除错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 更新文件列表的方法
        public async Task UpdateFileList(List<string> files)
        {
            uploadedFiles = new List<string>(files);
            await UpdateFileListInWebView(files);
        }

        // 添加公共方法，用于设置文件列表
        public async void SetFileList(List<string> files)
        {
            try
            {
                uploadedFiles = files;
                if (webView21 != null && webView21.CoreWebView2 != null)
                {
                    string filesJson = JsonConvert.SerializeObject(files);
                    Console.WriteLine($"设置WebView文件列表: {filesJson}");
                    string script = $"updateFileList({filesJson});";
                    await webView21.CoreWebView2.ExecuteScriptAsync(script);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置文件列表时出错: {ex.Message}");
            }
        }

        // 新增方法：同步企业复选框状态
        public void SyncEnterpriseCheckboxes()
        {
            if (webView21?.CoreWebView2 == null) return;

            // 创建企业名称到复选框ID的映射
            var enterpriseToCheckboxId = new Dictionary<string, string>
            {
                { "南方电网", "checkbox1" },
                { "广东电网公司", "checkbox2" },
                { "广西电网公司", "checkbox3" },
                { "云南电网公司", "checkbox4" },
                { "贵州电网公司", "checkbox5" },
                { "海南电网公司", "checkbox6" },
                { "深圳供电局", "checkbox7" },
                { "南网超高压公司", "checkbox8" },
                { "南网储能公司", "checkbox50" },
                { "南网产业投资集团", "checkbox51" },
                { "鼎元资产公司", "checkbox52" },
                { "南网能源公司", "checkbox53" },
                { "南网国际（香港）公司", "checkbox54" },
                { "南网澜湄国际公司", "checkbox55" },
                { "南网资本控股公司", "checkbox56" },
                { "南网财务公司", "checkbox57" },
                { "鼎和保险公司", "checkbox58" },
                { "南网党校", "checkbox59" },
                { "南网北京分公司", "checkbox60" },
                { "南网共享公司", "checkbox61" },
                { "南网生态运营公司", "checkbox62" },
                { "南网数字集团", "checkbox63" },
                { "南网供应链集团", "checkbox64" },
                { "南网能源院", "checkbox65" },
                { "南网科研院", "checkbox66" },
                { "广州电力交易中心", "checkbox67" },
                { "南网传媒公司", "checkbox68" },
                { "北京研究院", "checkbox69" }
            };

            // 创建要传递给JavaScript的复选框状态映射
            var checkboxMapping = new Dictionary<string, bool>();

            // 获取所有状态
            foreach (var state in EnterpriseCheckboxStateManager.CheckboxStates)
            {
                string id = state.Key;
                bool isChecked = state.Value;

                // 如果是checkbox开头的ID，直接使用
                if (id.StartsWith("checkbox"))
                {
                    checkboxMapping[id] = isChecked;
                }
                // 否则尝试查找对应的checkbox ID
                else
                {
                    // 如果已有标签信息，使用标签查找对应的checkbox ID
                    if (EnterpriseCheckboxStateManager.CheckboxLabels.ContainsKey(id))
                    {
                        string label = EnterpriseCheckboxStateManager.CheckboxLabels[id];
                        if (enterpriseToCheckboxId.ContainsKey(label))
                        {
                            string checkboxId = enterpriseToCheckboxId[label];
                            checkboxMapping[checkboxId] = isChecked;
                        }
                    }
                    // 如果ID本身就是企业名称，直接查找对应的checkbox ID
                    else if (enterpriseToCheckboxId.ContainsKey(id))
                    {
                        checkboxMapping[enterpriseToCheckboxId[id]] = isChecked;
                    }
                }
            }

            // 创建恢复状态的JavaScript代码
            string statesJson = JsonConvert.SerializeObject(checkboxMapping);
            string script = $@"
            try {{
                // 恢复复选框状态
                var states = {statesJson};
                for (let id in states) {{
                    const checkbox = document.getElementById(id);
                    if (checkbox) {{
                        checkbox.checked = states[id];
                    }}
                }}
            }} catch(e) {{
                console.error('同步企业复选框状态出错:', e);
            }}";

            // 执行JavaScript代码
            webView21.CoreWebView2.ExecuteScriptAsync(script);
        }

        // 新增方法：同步菜单复选框状态
        public void SyncMenuCheckboxes()
        {
            if (webView21?.CoreWebView2 == null) return;

            // 创建菜单名称到复选框ID的映射
            var menuToCheckboxId = new Dictionary<string, string>
            {
                { "1.1项目背景", "checkbox9" },
                { "1.2项目依据", "checkbox10" },
                { "1.3项目目标", "checkbox11" },
                { "1.4项目范围", "checkbox12" },
                { "2.1现状分析", "checkbox13" },
                { "2.2需求分析", "checkbox14" },
                { "2.3必要性结论", "checkbox15" },
                { "3.1业务架构", "checkbox16" },
                { "3.2应用架构", "checkbox17" },
                { "3.3数据架构", "checkbox18" },
                { "3.4技术架构", "checkbox19" },
                { "3.5系统部署方式及软硬件资源需求", "checkbox20" },
                { "3.6安全技术方案", "checkbox21" },
                { "3.7项目实施需求", "checkbox22" },
                { "4.1投资依据说明", "checkbox23" },
                { "4.2总投资", "checkbox24" },
                { "4.3资金计划建议", "checkbox25" },
                { "5.1管理效益分析", "checkbox26" },
                { "5.2经济效益分析", "checkbox27" },
                { "5.3社会效益分析", "checkbox28" },
                { "6.1项目风险分析", "checkbox29" },
                { "7.1项目可研结论", "checkbox30" }
            };

            // 创建要传递给JavaScript的复选框状态映射
            var checkboxMapping = new Dictionary<string, bool>();

            // 获取所有状态
            foreach (var state in MenuCheckboxStateManager.CheckboxStates)
            {
                string id = state.Key;
                bool isChecked = state.Value;

                // 如果是checkbox开头的ID，直接使用
                if (id.StartsWith("checkbox"))
                {
                    checkboxMapping[id] = isChecked;
                }
                // 否则尝试查找对应的checkbox ID
                else
                {
                    // 如果已有标签信息，使用标签查找对应的checkbox ID
                    if (MenuCheckboxStateManager.CheckboxLabels.ContainsKey(id))
                    {
                        string label = MenuCheckboxStateManager.CheckboxLabels[id];
                        if (menuToCheckboxId.ContainsKey(label))
                        {
                            string checkboxId = menuToCheckboxId[label];
                            checkboxMapping[checkboxId] = isChecked;
                        }
                    }
                    // 如果ID本身就是菜单名称，直接查找对应的checkbox ID
                    else if (menuToCheckboxId.ContainsKey(id))
                    {
                        checkboxMapping[menuToCheckboxId[id]] = isChecked;
                    }
                }
            }

            // 创建恢复状态的JavaScript代码
            string statesJson = JsonConvert.SerializeObject(checkboxMapping);
            string script = $@"
            try {{
                // 恢复复选框状态
                var states = {statesJson};
                for (let id in states) {{
                    const checkbox = document.getElementById(id);
                    if (checkbox) {{
                        checkbox.checked = states[id];
                    }}
                }}
            }} catch(e) {{
                console.error('同步菜单复选框状态出错:', e);
            }}";

            // 执行JavaScript代码
            webView21.CoreWebView2.ExecuteScriptAsync(script);
        }

        // 辅助方法：根据企业名称查找对应的checkbox ID
        private string GetEnterpriseCheckboxId(string enterpriseName)
        {
            var enterpriseLabels = new Dictionary<string, string>
            {
                { "南方电网", "checkbox1" },
                { "广东电网公司", "checkbox2" },
                { "广西电网公司", "checkbox3" },
                { "云南电网公司", "checkbox4" },
                { "贵州电网公司", "checkbox5" },
                { "海南电网公司", "checkbox6" },
                { "深圳供电局", "checkbox7" },
                { "南网超高压公司", "checkbox8" },
                { "南网储能公司", "checkbox50" },
                { "南网产业投资集团", "checkbox51" },
                { "鼎元资产公司", "checkbox52" },
                { "南网能源公司", "checkbox53" },
                { "南网国际（香港）公司", "checkbox54" },
                { "南网澜湄国际公司", "checkbox55" },
                { "南网资本控股公司", "checkbox56" },
                { "南网财务公司", "checkbox57" },
                { "鼎和保险公司", "checkbox58" },
                { "南网党校", "checkbox59" },
                { "南网北京分公司", "checkbox60" },
                { "南网共享公司", "checkbox61" },
                { "南网生态运营公司", "checkbox62" },
                { "南网数字集团", "checkbox63" },
                { "南网供应链集团", "checkbox64" },
                { "南网能源院", "checkbox65" },
                { "南网科研院", "checkbox66" },
                { "广州电力交易中心", "checkbox67" },
                { "南网传媒公司", "checkbox68" },
                { "北京研究院", "checkbox69" }
            };
            
            return enterpriseLabels.ContainsKey(enterpriseName) ? enterpriseLabels[enterpriseName] : string.Empty;
        }

        // 辅助方法：根据菜单项名称查找对应的checkbox ID
        private string GetMenuCheckboxId(string menuName)
        {
            var menuLabels = new Dictionary<string, string>
            {
                { "1.1项目背景", "checkbox9" },
                { "1.2项目依据", "checkbox10" },
                { "1.3项目目标", "checkbox11" },
                { "1.4项目范围", "checkbox12" },
                { "2.1现状分析", "checkbox13" },
                { "2.2需求分析", "checkbox14" },
                { "2.3必要性结论", "checkbox15" },
                { "3.1业务架构", "checkbox16" },
                { "3.2应用架构", "checkbox17" },
                { "3.3数据架构", "checkbox18" },
                { "3.4技术架构", "checkbox19" },
                { "3.5系统部署方式及软硬件资源需求", "checkbox20" },
                { "3.6安全技术方案", "checkbox21" },
                { "3.7项目实施需求", "checkbox22" },
                { "4.1投资依据说明", "checkbox23" },
                { "4.2总投资", "checkbox24" },
                { "4.3资金计划建议", "checkbox25" },
                { "5.1管理效益分析", "checkbox26" },
                { "5.2经济效益分析", "checkbox27" },
                { "5.3社会效益分析", "checkbox28" },
                { "6.1项目风险分析", "checkbox29" },
                { "7.1项目可研结论", "checkbox30" }
            };
            
            return menuLabels.ContainsKey(menuName) ? menuLabels[menuName] : string.Empty;
        }
    }
}