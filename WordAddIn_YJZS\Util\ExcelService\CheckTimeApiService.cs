﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace WordAddIn_YJZS
{
    public class CheckTimeApiService
    {
        private readonly HttpClient client;

        public CheckTimeApiService()
        {
            client = HttpClientManager.Client;
        }

        // 表3-1数据请求模型
        public class TimeRequest
        {
            [JsonProperty("username")]
            public string Username { get; set; }

            [JsonProperty("project_name")]
            public string ProjectName { get; set; }

            [JsonProperty("start_row")]
            public int StartRow { get; set; }

            [JsonProperty("start_col")]
            public int StartCol { get; set; }
        }

        // 表3-1响应模型
        public class TimeResponse
        {
            [JsonProperty("status_code")]
            public int StatusCode { get; set; }

            [JsonProperty("message")]
            public string Message { get; set; }

            [JsonProperty("data")]
            public string Data { get; set; }
        }

        // 主方法：获取预估时间
        public async Task<TimeResponse> EvalTimeAsync(string username, string projectName, int startRow = 1, int startCol = 1)
        {
            var request = new TimeRequest
            {
                Username = username,
                ProjectName = projectName,
                StartRow = startRow,
                StartCol = startCol
            };

            try
            {
                System.Diagnostics.Debug.WriteLine($"请求表3-1: 用户={username}, 项目={projectName}, 起始行={startRow}, 起始列={startCol}");
                return await PostJsonAsync(AllApi.CheckTimeUrl, request);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成表3-1出错: {ex.Message}");
                throw new Exception($"生成表3-1失败: {ex.Message}", ex);
            }
        }

        // 验证响应是否有效
        public bool ValidateResponse(TimeResponse response)
        {
            return response != null && !string.IsNullOrWhiteSpace(response.Data);
        }

        // 统一 POST 方法，反序列化完整响应
        private async Task<TimeResponse> PostJsonAsync(string url, object data)
        {
            using (var jsonContent = new StringContent(JsonConvert.SerializeObject(data)))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    var responseString = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var result = JsonConvert.DeserializeObject<TimeResponse>(responseString);
                        if (result == null || result.StatusCode != 200)
                            throw new Exception($"API错误: {result?.Message ?? "未知错误"}");

                        return result;
                    }
                    else
                    {
                        throw new Exception($"HTTP错误 {response.StatusCode}: {responseString}");
                    }
                }
                catch (TaskCanceledException)
                {
                    throw new Exception("请求超时");
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception("网络错误：" + ex.Message);
                }
            }
        }
    }
}
