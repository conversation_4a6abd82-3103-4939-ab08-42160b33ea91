﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Diagnostics;

namespace WordAddIn_YJZS
{
    public partial class AboutForm : Form
    {
        private readonly string currentVersion = "0.9.4"; // 当前版本号（仅用于显示）

        public AboutForm()
        {
            InitializeComponent();
            InitializeWebView2();      
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                // 注册WebMessage处理程序
                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "About.html");
                webView21.Source = new Uri(htmlFilePath);

                webView21.NavigationCompleted += WebView_NavigationCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private async void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                try
                {
                    // 注入通信脚本
                    await webView21.CoreWebView2.ExecuteScriptAsync($@"
                        // 初始化当前版本
                        document.getElementById('currentVersion').textContent = '{currentVersion}';
                        
                        // 添加版本检查函数
                        window.checkVersionFromCSharp = function() {{
                            window.chrome.webview.postMessage({{
                                action: 'checkVersion',
                                currentVersion: '{currentVersion}'
                            }});
                        }};
                        
                        // 通知页面桥接已准备好
                        if (window.onBridgeReady) {{
                            window.onBridgeReady();
                        }}
                    ");

                    // 自动检查版本更新
                    await AutoCheckVersion();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"注入JavaScript时出错: {ex.Message}");
                }
            }
            else
            {
                MessageBox.Show("关于页面加载失败");
            }
        }

        private async void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // 解析从网页接收到的消息
                var message = JsonConvert.DeserializeObject<dynamic>(e.WebMessageAsJson);

                if (message.action.ToString() == "checkVersion")
                {
                    Debug.WriteLine("收到手动检查更新请求");
                    await PerformVersionCheck(true);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理WebView消息时出错: {ex.Message}");
            }
        }

        // 自动检查版本更新
        private async Task AutoCheckVersion()
        {
            try
            {
                Debug.WriteLine("开始自动检查版本更新");
                await PerformVersionCheck(false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"自动检查版本更新时出错: {ex.Message}");
                // 自动检查失败时，静默处理，不显示错误信息
                await webView21.CoreWebView2.ExecuteScriptAsync(@"
                    document.getElementById('latestVersion').textContent = '检查失败';
                ");
            }
        }

        // 执行版本检查的核心方法
        private async Task PerformVersionCheck(bool isManualCheck)
        {
            try
            {
                var versionApiService = new VersionApiService();
                var result = await versionApiService.CheckVersionAsync(currentVersion);

                if (result != null)
                {
                    // 更新最新版本显示
                    await webView21.CoreWebView2.ExecuteScriptAsync($@"
                        document.getElementById('latestVersion').textContent = '{result.LatestVersion}';
                    ");

                    if (result.UpdateAvailable)
                    {
                        // 有更新可用
                        if (isManualCheck)
                        {
                            // 手动检查时显示模态框
                            await ShowUpdateModal(result);
                        }
                        else
                        {
                            // 自动检查时直接打开固定的下载链接
                            await OpenDownloadUrl();
                        }
                    }
                    else
                    {
                        // 没有更新
                        if (isManualCheck)
                        {
                            // 手动检查时显示"当前已是最新版本"
                            await ShowNoUpdateModal();
                        }
                        // 自动检查时不显示任何信息
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"版本检查失败: {ex.Message}");
                if (isManualCheck)
                {
                    await ShowErrorModal(ex.Message);
                }
            }
        }

        // 显示有更新可用的模态框
        private async Task ShowUpdateModal(VersionCheckResult result)
        {
            string changelogHtml = !string.IsNullOrEmpty(result.Changelog)
                ? $"<div class='changelog'>{result.Changelog.Replace("'", "\\'").Replace("\n", "<br>")}</div>"
                : "";

            string finalHtml = $@"
                <div id='updateResult'>
                    <p id='updateMessage'>
                        <span class='update-available'>
                            🎉 发现新版本 {result.LatestVersion}！<br>
                            <small>当前版本: {result.CurrentVersion}</small>
                        </span>
                    </p>
                    {changelogHtml}
                </div>
            ";

            await webView21.CoreWebView2.ExecuteScriptAsync($@"
                document.getElementById('modalContent').innerHTML = `{finalHtml.Replace("`", "\\`")}`;
            ");

            // 自动打开固定的下载链接
            await OpenDownloadUrl();
        }

        // 显示没有更新的模态框
        private async Task ShowNoUpdateModal()
        {
            string finalHtml = @"
                <div id='updateResult'>
                    <p id='updateMessage'>
                        <span class='no-update'>
                            ✅ 当前已是最新版本<br>
                        </span>
                    </p>
                </div>
            ";

            await webView21.CoreWebView2.ExecuteScriptAsync($@"
                document.getElementById('modalContent').innerHTML = `{finalHtml.Replace("`", "\\`")}`;
            ");
        }

        // 显示错误信息的模态框
        private async Task ShowErrorModal(string errorMessage)
        {
            string finalHtml = $@"
                <div id='updateResult'>
                    <p id='updateMessage'>
                        <span class='error'>
                            ❌ 检查更新失败<br>
                            <small>{errorMessage.Replace("'", "\\'")}</small>
                        </span>
                    </p>
                </div>
            ";

            await webView21.CoreWebView2.ExecuteScriptAsync($@"
                document.getElementById('modalContent').innerHTML = `{finalHtml.Replace("`", "\\`")}`;
            ");
        }

        // 打开固定的下载链接
        private Task OpenDownloadUrl()
        {
            try
            {
                // 使用固定的下载页面
                const string downloadUrl = "http://***************:12010/downloads";
                Debug.WriteLine($"打开下载链接: {downloadUrl}");
                Process.Start(new ProcessStartInfo
                {
                    FileName = downloadUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"打开下载链接失败: {ex.Message}");
            }

            return Task.CompletedTask;
        }
    }
}
