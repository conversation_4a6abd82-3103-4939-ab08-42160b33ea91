﻿using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;

namespace WordAddIn_YJZS
{
    public class ProgressStream : Stream
    {
        private const int bufferSize = 4096;
        private readonly Stream stream;
        private readonly IProgress<int> progress;
        private readonly long totalBytes;
        private long uploadedBytes;

        public ProgressStream(Stream stream, IProgress<int> progress, long uploadedBytes, long totalBytes)
        {
            this.stream = stream;
            this.progress = progress;
            this.uploadedBytes = uploadedBytes;
            this.totalBytes = totalBytes;
        }

        public override bool CanRead => stream.CanRead;
        public override bool CanSeek => stream.CanSeek;
        public override bool CanWrite => stream.CanWrite;
        public override long Length => stream.Length;
        public override long Position
        {
            get => stream.Position;
            set => stream.Position = value;
        }

        public override void Flush() => stream.Flush();

        public override int Read(byte[] buffer, int offset, int count)
        {
            int bytesRead = stream.Read(buffer, offset, count);
            uploadedBytes += bytesRead;

            // 报告进度
            int percentComplete = (int)((double)uploadedBytes / totalBytes * 100);
            progress?.Report(percentComplete);

            return bytesRead;
        }

        public override long Seek(long offset, SeekOrigin origin) => stream.Seek(offset, origin);
        public override void SetLength(long value) => stream.SetLength(value);
        public override void Write(byte[] buffer, int offset, int count) => stream.Write(buffer, offset, count);
    }
}
