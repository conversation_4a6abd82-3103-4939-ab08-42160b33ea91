﻿using System;
using System.Net.Http;
using System.Threading;

namespace WordAddIn_YJZS
{
    public static class HttpClientManager
    {
        private static readonly Lazy<HttpClient> _httpClient = new Lazy<HttpClient>(() =>
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromMinutes(200); // 设置200分钟超时
            return client;
        });

        public static HttpClient Client => _httpClient.Value;

        // 创建支持取消的HttpClient实例
        public static HttpClient CreateCancellableClient(TimeSpan? timeout = null)
        {
            var client = new HttpClient();
            client.Timeout = timeout ?? TimeSpan.FromSeconds(30); // 默认30秒超时，比原来的200分钟短很多
            return client;
        }

        // 创建与取消令牌关联的CancellationTokenSource
        public static CancellationTokenSource CreateCancellationTokenSource(TimeSpan? timeout = null)
        {
            var cts = new CancellationTokenSource();
            if (timeout.HasValue)
            {
                cts.CancelAfter(timeout.Value);
            }
            return cts;
        }
    }
}
