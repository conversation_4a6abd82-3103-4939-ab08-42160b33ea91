﻿using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public class WriterApiService
    {
        private readonly HttpClient client;

        public WriterApiService()
        {
            client = HttpClientManager.Client;
        }

        public async Task<string> ProcessParagraphAsync(string paragraph, string option, IProgress<int> progress = null)
        {
            var requestBody = new
            {
                paragraph,
                option
            };

            var jsonContent = new StringContent(JsonConvert.SerializeObject(requestBody));
            jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            try
            {
                using (var response = await client.PostAsync(AllApi.WriterUrl, jsonContent))
                {
                    response.EnsureSuccessStatusCode();

                    var contentLength = response.Content.Headers.ContentLength ?? -1;
                    var responseStream = await response.Content.ReadAsStreamAsync();

                    // 包装响应流以报告进度
                    var progressStream = new ProgressStream(responseStream, progress, 0, contentLength);

                    using (var reader = new StreamReader(progressStream))
                    {
                        var result = await reader.ReadToEndAsync();
                        return result;
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception("请求发生错误：" + ex.Message);
            }
        }
    }
}
