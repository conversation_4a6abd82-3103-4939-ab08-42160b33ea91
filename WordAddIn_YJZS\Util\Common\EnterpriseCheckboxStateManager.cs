﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace WordAddIn_YJZS
{
    public static class EnterpriseCheckboxStateManager
    {
        private static readonly string STATE_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "enterprise_state.json");
        public static Dictionary<string, bool> CheckboxStates { get; set; } = new Dictionary<string, bool>();
        public static Dictionary<string, string> CheckboxLabels { get; set; } = new Dictionary<string, string>();

        static EnterpriseCheckboxStateManager()
        {
            LoadState();
        }

        public static void UpdateCheckboxState(string id, bool isChecked, string label)
        {
            CheckboxStates[id] = isChecked;
            CheckboxLabels[id] = label;
            SaveState();
        }

        public static List<string> GetCheckedItems()
        {
            return CheckboxStates
                .Where(kvp => kvp.Value)
                .Select(kvp => CheckboxLabels.ContainsKey(kvp.Key) ? CheckboxLabels[kvp.Key] : kvp.Key)
                .ToList();
        }

        // 添加 ClearCheckedItems 方法
        public static void ClearCheckedItems()
        {
            // 将所有复选框状态设置为未选中
            var keys = CheckboxStates.Keys.ToList();
            foreach (var key in keys)
            {
                CheckboxStates[key] = false;
            }
            SaveState();
        }

        public static string GetCheckboxStatesJson()
        {
            return JsonConvert.SerializeObject(CheckboxStates);
        }

        public static void Clear()
        {
            CheckboxStates.Clear();
            CheckboxLabels.Clear();
            SaveState();
        }

        // 保存状态到文件
        public static void SaveState()
        {
            try
            {
                var stateData = new
                {
                    CheckboxStates,
                    CheckboxLabels
                };

                string json = JsonConvert.SerializeObject(stateData, Formatting.Indented);
                File.WriteAllText(STATE_FILE_PATH, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存企业状态时出错: {ex.Message}");
            }
        }

        // 从文件加载状态
        public static void LoadState()
        {
            try
            {
                if (File.Exists(STATE_FILE_PATH))
                {
                    string json = File.ReadAllText(STATE_FILE_PATH);
                    var stateData = JsonConvert.DeserializeObject<dynamic>(json);
                    
                    CheckboxStates.Clear();
                    CheckboxLabels.Clear();

                    foreach (var item in stateData.CheckboxStates)
                    {
                        string key = item.Name;
                        bool value = item.Value;
                        CheckboxStates[key] = value;
                    }

                    foreach (var item in stateData.CheckboxLabels)
                    {
                        string key = item.Name;
                        string value = item.Value;
                        CheckboxLabels[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载企业状态时出错: {ex.Message}");
            }
        }
    }
}