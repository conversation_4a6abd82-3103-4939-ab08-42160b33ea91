﻿using System;
using Microsoft.Office.Interop.Word;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WordAddIn_YJZS;
using Task = System.Threading.Tasks.Task;

public class WriterApiParse
{
    // WriterApiService 实例
    private readonly WriterApiService writerApiService = new WriterApiService();
    private string MsgboxTitle = "消息框的标题";

    //解析response的"processed_paragraph" 字段的文字
    public static string ParseProcessedParagraph(string jsonResult)
    {
        try
        {
            // 将 JSON 字符串解析为 JObject
            JObject jsonObject = JObject.Parse(jsonResult);

            // 输出完整的 JSON 响应以进行调试
            string fullResponse = $"完整响应: {jsonObject.ToString(Formatting.Indented)}";

            // 检查 statescode 是否为 200
            int status_code = jsonObject.Value<int>("status_code");
            string message = jsonObject.Value<string>("message");

            if (status_code != 200)
            {
                return $"错误: 状态码 {status_code}, 消息: {message}\n\n{fullResponse}";
            }

            // 获取 data 对象中的 text 字段的值
            if (jsonObject["data"] is JObject dataObject && dataObject.TryGetValue("text", out JToken textToken))
            {
                string text = textToken.ToString();
                //return $"成功: {text}\n\n调试信息:\n{fullResponse}";
                return text;
            }
            else
            {
                return $"未找到 'data.text' 字段\n\n{fullResponse}";
            }
        }
        catch (JsonException ex)
        {
            // 如果 JSON 解析失败，返回错误信息和原始响应
            return $"JSON 解析错误: {ex.Message}\n\n原始响应:\n{jsonResult}";
        }
    }

    //处理选中文本+优化扩写精简接口方法response+解析response+替换选中的文本
    public async Task HandleSelectedTextAsync(Document document, string option)
    {
        ProgressForm progressForm = null;
        try
        {
            // 创建并显示进度条
            if (option == "polish")
            {
                MsgboxTitle = "优化";
                progressForm = new ProgressForm("优化");
            }
            else if (option == "expand")
            {
                MsgboxTitle = "扩写";
                progressForm = new ProgressForm("扩写");
            }
            else if (option == "shorten")
            {
                MsgboxTitle = "精简";
                progressForm = new ProgressForm("精简");
            }

            progressForm.Show();

            string selectedText = GetSelectedText(document);

            if (string.IsNullOrEmpty(selectedText))
            {
                MessageBox.Show("请先选择要处理的文本内容！");
                return;
            }

            // 更新进度条
            progressForm.UpdateProgress(10);

            // 调用API处理文本
            string result = await writerApiService.ProcessParagraphAsync(selectedText, option);

            // 更新进度条
            progressForm.UpdateProgress(80);

            string processedParagraph = WriterApiParse.ParseProcessedParagraph(result);

            // 更新进度条到100%
            progressForm.UpdateProgress(100);

            // 关闭进度条
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.Close();
                progressForm.Dispose();
                progressForm = null;
            }

            // 显示替换确认对话框
            DialogResult dialogResult = ReplaceMsgBox.Show(processedParagraph, $"{MsgboxTitle}完成");

            // 当用户点击"确定"后，替换选中的文本
            if (dialogResult == DialogResult.OK)
            {
                ReplaceSelectedText(document, processedParagraph);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("操作过程中发生错误: " + ex.Message);
        }
        finally
        {
            // 确保进度条被关闭
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.Close();
                progressForm.Dispose();
            }
        }
    }

    //实现替换选中的文本
    public string GetSelectedText(Document document)
    {
        Selection selection = document.Application.Selection;
        return selection.Text.Trim();
    }

    public void ReplaceSelectedText(Document document, string newText)
    {
        Selection selection = document.Application.Selection;

        // 替换选中的文本
        if (!string.IsNullOrEmpty(newText))
        {
            selection.Text = newText;
        }
    }
}