﻿using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace WordAddIn_YJZS
{
    public class ExcelCoverApiService
    {
        private readonly HttpClient client;

        public ExcelCoverApiService()
        {
            client = HttpClientManager.Client;
        }

        // 封面数据请求模型
        public class ExcelCoverRequest
        {
            [JsonProperty("username")]
            public string Username { get; set; }

            [JsonProperty("project_name")]
            public string ProjectName { get; set; }
        }

        public class ExcelCoverResponse
        {
            [JsonProperty("content_list")]
            public string ContentList { get; set; }
        }

        // 获取封面投资估算数据
        public async Task<ExcelCoverResponse> GetCoverEstimateCostAsync(string username, string projectName)
        {
            try
            {
                var request = new ExcelCoverRequest
                {
                    Username = username,
                    ProjectName = projectName
                };

                var response = await PostJsonAsync<ExcelCoverResponse>(
                    AllApi.ExcelCoverUrl,
                    request,
                    "获取封面投资估算数据"
                );

                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取封面投资估算数据时出错: {ex.Message}");
                return null;
            }
        }

        // 通用的JSON POST方法
        private async Task<T> PostJsonAsync<T>(string url, object data, string operationName)
        {
            try
            {
                string jsonContent = JsonConvert.SerializeObject(data);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                var response = await client.PostAsync(url, content);
                string responseBody = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonConvert.DeserializeObject<ApiResponse<T>>(responseBody);
                    if (jsonResponse.StatusCode == 200)
                    {
                        return jsonResponse.Data;
                    }
                    else
                    {
                        Console.WriteLine($"{operationName}失败: {jsonResponse.Message}");
                        return default;
                    }
                }
                else
                {
                    Console.WriteLine($"{operationName}请求失败: {response.StatusCode}");
                    return default;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{operationName}时发生异常: {ex.Message}");
                return default;
            }
        }

        // API响应通用模型
        private class ApiResponse<T>
        {
            [JsonProperty("status_code")]
            public int StatusCode { get; set; }

            [JsonProperty("message")]
            public string Message { get; set; }

            [JsonProperty("data")]
            public T Data { get; set; }
        }
    }
}
