﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class MenuForm : Form
    {
        public MenuForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                string userDataFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "WebView2UserData"
                );
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);
                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    "resource/web",
                    "Set_menu.html"
                );
                webView21.Source = new Uri(htmlFilePath);

                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                webView21.NavigationCompleted += WebView_NavigationCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                try 
                {
                    // 创建HTML元素ID到状态的映射
                    var checkboxMapping = new Dictionary<string, bool>();
                    
                    // 获取checkbox ID和对应的标签
                    foreach (var pair in MenuCheckboxStateManager.CheckboxLabels)
                    {
                        string id = pair.Key;
                        string label = pair.Value;
                        
                        // 如果ID是"checkbox开头"，直接使用这个ID
                        if (id.StartsWith("checkbox"))
                        {
                            if (MenuCheckboxStateManager.CheckboxStates.ContainsKey(id))
                            {
                                checkboxMapping[id] = MenuCheckboxStateManager.CheckboxStates[id];
                            }
                        }
                        // 否则查找对应标签的checkbox ID
                        else
                        {
                            // 查找label对应的HTML checkbox ID (checkbox9-checkbox30)
                            string checkboxId = FindCheckboxIdByLabel(label);
                            if (!string.IsNullOrEmpty(checkboxId) && MenuCheckboxStateManager.CheckboxStates.ContainsKey(id))
                            {
                                checkboxMapping[checkboxId] = MenuCheckboxStateManager.CheckboxStates[id];
                            }
                        }
                    }
                    
                    // 以JSON格式将映射传递给JavaScript
                    string statesJson = JsonConvert.SerializeObject(checkboxMapping);
                    // 添加try-catch到JavaScript代码中
                    string script = $@"
                    try {{
                        restoreCheckboxStates({statesJson});
                    }} catch(e) {{
                        // 静默处理错误
                    }}";
                    webView21.CoreWebView2.ExecuteScriptAsync(script);
                }
                catch (Exception ex)
                {
                    // 记录错误但继续执行，不要阻止窗体加载
                    Console.WriteLine($"更新复选框状态时出错: {ex.Message}");
                }
            }
        }
        
        // 根据标签查找对应的checkbox ID
        private string FindCheckboxIdByLabel(string label)
        {
            var menuLabels = new Dictionary<string, string>
            {
                { "1.1项目背景", "checkbox9" },
                { "1.2项目依据", "checkbox10" },
                { "1.3项目目标", "checkbox11" },
                { "1.4项目范围", "checkbox12" },
                { "2.1现状分析", "checkbox13" },
                { "2.2需求分析", "checkbox14" },
                { "2.3必要性结论", "checkbox15" },
                { "3.1业务架构", "checkbox16" },
                { "3.2应用架构", "checkbox17" },
                { "3.3数据架构", "checkbox18" },
                { "3.4技术架构", "checkbox19" },
                { "3.5系统部署方式及软硬件资源需求", "checkbox20" },
                { "3.6安全技术方案", "checkbox21" },
                { "3.7项目实施需求", "checkbox22" },
                { "4.1投资依据说明", "checkbox23" },
                { "4.2总投资", "checkbox24" },
                { "4.3资金计划建议", "checkbox25" },
                { "5.1管理效益分析", "checkbox26" },
                { "5.2经济效益分析", "checkbox27" },
                { "5.3社会效益分析", "checkbox28" },
                { "6.1项目风险分析", "checkbox29" },
                { "7.1项目可研结论", "checkbox30" }
            };
            
            return menuLabels.ContainsKey(label) ? menuLabels[label] : string.Empty;
        }

        // 添加新方法：重置用户修改状态
        public void ResetUserModificationState()
        {
            GlobalParameters.SetMenuSelectionModified(false);
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // 先获取原始消息
                string jsonMessage = e.WebMessageAsJson;

                // 检查是否包含closeForm关键字，这是一种更直接的方式
                if (jsonMessage.Contains("closeForm"))
                {
                    // 直接关闭表单
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                    return;
                }

                // 检查是否是初始状态请求消息
                if (jsonMessage.Contains("getInitialState"))
                {
                    // 不需要任何处理，因为WebView_NavigationCompleted已经处理了状态初始化
                    return;
                }

                // 如果不是关闭消息，再尝试解析为JSON对象
                var message = JsonConvert.DeserializeObject<dynamic>(jsonMessage);
                
                // 检查消息类型
                if (message.type != null)
                {
                    if (message.type.ToString() == "closeForm")
                    {
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                        return;
                    }
                    else if (message.type.ToString() == "getInitialState")
                    {
                        // 已在WebView_NavigationCompleted处理
                        return;
                    }
                }

                // 确保必要的属性存在
                if (message.id == null || message.@checked == null)
                {
                    // 如果缺少必要属性，仅记录日志，不显示弹窗
                    Console.WriteLine("收到的消息格式不正确，缺少必要的属性");
                    return;
                }

                string id = message.id.ToString();
                bool isChecked = message.@checked;
                string label = message.label.ToString();

                // 清除之前的所有选择状态（仅当是用户第一次修改时）
                if (!GlobalParameters.MenuSelectionModifiedByUser)
                {
                    MenuCheckboxStateManager.Clear();
                }

                // 检查状态管理器中是否已存在相同的标签，避免重复
                bool existingLabelFound = false;
                string existingId = null;

                // 查找是否已有相同标签的记录
                foreach (var pair in MenuCheckboxStateManager.CheckboxLabels)
                {
                    // 如果找到相同标签的记录
                    if (pair.Value == label)
                    {
                        existingLabelFound = true;
                        existingId = pair.Key;
                        break;
                    }
                }

                // 如果找到相同标签的ID但不是当前ID，则移除旧记录
                if (existingLabelFound && existingId != id && existingId != null)
                {
                    // 删除旧的记录
                    if (MenuCheckboxStateManager.CheckboxStates.ContainsKey(existingId))
                    {
                        MenuCheckboxStateManager.CheckboxStates.Remove(existingId);
                    }
                    if (MenuCheckboxStateManager.CheckboxLabels.ContainsKey(existingId))
                    {
                        MenuCheckboxStateManager.CheckboxLabels.Remove(existingId);
                    }
                }

                // 更新状态
                MenuCheckboxStateManager.UpdateCheckboxState(id, isChecked, label);

                // 标记为用户手动修改
                GlobalParameters.SetMenuSelectionModified(true);

                // 同步到其他窗体
                foreach (Form form in Application.OpenForms)
                {
                    if (form is GenerateForm generateForm && !form.IsDisposed)
                    {
                        generateForm.Invoke(new Action(() => {
                            generateForm.UpdateMenuCheckboxState(id, isChecked);
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误到控制台而不是显示弹窗
                Console.WriteLine($"处理消息时出错: {ex.Message}");
            }
        }

        // 更新checkbox状态的公共方法
        public void UpdateCheckboxState(string idOrLabel, bool isChecked)
        {
            if (webView21?.CoreWebView2 == null) return;

            try
            {
                // 尝试查找ID对应的checkbox
                string checkboxId = idOrLabel;

                // 如果不是以checkbox开头，则认为是标签，尝试查找对应的ID
                if (!idOrLabel.StartsWith("checkbox"))
                {
                    checkboxId = FindCheckboxIdByLabel(idOrLabel);
                    
                    // 如果找不到对应ID，可能是直接使用了菜单名称
                    if (string.IsNullOrEmpty(checkboxId))
                    {
                        Console.WriteLine($"未找到菜单名称 '{idOrLabel}' 对应的复选框ID");
                        return;
                    }
                }

                // 更新状态管理器
                string label = ""; 
                
                // 从复选框ID反查标签
                if (checkboxId.StartsWith("checkbox"))
                {
                    label = GetLabelFromCheckboxId(checkboxId);
                    if (!string.IsNullOrEmpty(label))
                    {
                        MenuCheckboxStateManager.UpdateCheckboxState(checkboxId, isChecked, label);
                    }
                    else
                    {
                        MenuCheckboxStateManager.UpdateCheckboxState(checkboxId, isChecked, idOrLabel);
                    }
                }

                // 更新UI
                string script = $@"
                try {{
                    const checkbox = document.getElementById('{checkboxId}');
                    if (checkbox) {{
                        checkbox.checked = {isChecked.ToString().ToLower()};
                    }}
                }} catch (e) {{
                    console.error('更新复选框状态出错:', e);
                }}";

                webView21.CoreWebView2.ExecuteScriptAsync(script);

                // 同步到其他窗体
                foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
                {
                    if (form is GenerateForm generateForm && !form.IsDisposed)
                    {
                        generateForm.Invoke(new Action(() => {
                            generateForm.UpdateMenuCheckboxState(checkboxId, isChecked);
                            // 手动触发重新同步
                            generateForm.SyncMenuCheckboxes();
                        }));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新复选框状态时出错: {ex.Message}");
            }
        }

        // 辅助方法：根据复选框ID获取对应的标签
        private string GetLabelFromCheckboxId(string checkboxId)
        {
            var idToLabel = new Dictionary<string, string>
            {
                { "checkbox9", "1.1项目背景" },
                { "checkbox10", "1.2项目依据" },
                { "checkbox11", "1.3项目目标" },
                { "checkbox12", "1.4项目范围" },
                { "checkbox13", "2.1现状分析" },
                { "checkbox14", "2.2需求分析" },
                { "checkbox15", "2.3必要性结论" },
                { "checkbox16", "3.1业务架构" },
                { "checkbox17", "3.2应用架构" },
                { "checkbox18", "3.3数据架构" },
                { "checkbox19", "3.4技术架构" },
                { "checkbox20", "3.5系统部署方式及软硬件资源需求" },
                { "checkbox21", "3.6安全技术方案" },
                { "checkbox22", "3.7项目实施需求" },
                { "checkbox23", "4.1投资依据说明" },
                { "checkbox24", "4.2总投资" },
                { "checkbox25", "4.3资金计划建议" },
                { "checkbox26", "5.1管理效益分析" },
                { "checkbox27", "5.2经济效益分析" },
                { "checkbox28", "5.3社会效益分析" },
                { "checkbox29", "6.1项目风险分析" },
                { "checkbox30", "7.1项目可研结论" }
            };
            
            return idToLabel.ContainsKey(checkboxId) ? idToLabel[checkboxId] : string.Empty;
        }

        public List<string> GetSelectedMenu()
        {
            return MenuCheckboxStateManager.GetCheckedItems();
        }
    }
}