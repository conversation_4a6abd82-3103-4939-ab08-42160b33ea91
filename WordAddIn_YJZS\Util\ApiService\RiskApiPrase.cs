﻿using System;
using Microsoft.Office.Interop.Word;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WordAddIn_YJZS;
using Task = System.Threading.Tasks.Task;
using System.Collections.Generic;

namespace WordAddIn_YJZS
{
    public class RiskApiPrase
    {
        private readonly RiskApiService riskApiService = new RiskApiService();

        public class RiskItem
        {
            public string Title { get; set; }
            public string Content { get; set; }
        }

        public static List<RiskItem> PraseRiskAnalyse(string jsonResult)
        {
            try
            {
                if (string.IsNullOrEmpty(jsonResult))
                {
                    throw new Exception("接收到的数据为空");
                }

                // 首先尝试直接解析为 JArray
                var riskItems = new List<RiskItem>();
                var riskList = JArray.Parse(jsonResult);

                foreach (JObject risk in riskList)
                {
                    if (risk == null)
                    {
                        continue;
                    }

                    riskItems.Add(new RiskItem
                    {
                        Title = risk["title"]?.ToString() ?? "无标题",
                        Content = risk["content"]?.ToString() ?? "无内容"
                    });
                }

                if (riskItems.Count == 0)
                {
                    throw new Exception("未找到任何风险项");
                }

                return riskItems;
            }
            catch (JsonException ex)
            {
                throw new Exception($"JSON解析错误: {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new Exception($"处理风险分析数据时出错: {ex.Message}");
            }
        }
    }
}
