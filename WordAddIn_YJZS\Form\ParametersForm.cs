﻿using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace WordAddIn_YJZS
{
    public partial class ParametersForm : Form
    {
        private CalculateFormData currentFormData;
        public bool IsConfirmed { get; private set; }

        public ParametersForm()
        {
            InitializeComponent();
            InitializeWebView2();
            IsConfirmed = false;
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "calculate.html");
                webView21.Source = new Uri(htmlFilePath);

                webView21.NavigationCompleted += WebView_NavigationCompleted;
                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                // 解析接收到的JSON数据
                currentFormData = JsonConvert.DeserializeObject<CalculateFormData>(e.WebMessageAsJson);

                if (currentFormData != null)
                {
                    IsConfirmed = true;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理表单数据时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (!e.IsSuccess)
            {
                MessageBox.Show("页面加载失败");
            }
        }

        // 获取表单数据的公共方法
        public CalculateFormData GetFormData()
        {
            return currentFormData;
        }
    }
}