<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>生成预览</title>
	<style>
		@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

		body {
			font-family: 'Roboto', sans-serif;
			background-color: #f0f2f5;
			margin: 0;
			padding: 0;
			color: #333;
		}

		.dashboard {
			display: flex;
			width: 1400px;
			margin: 0px auto;
			background-color: #ffffff;
			border-radius: 10px;
			box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
			overflow: hidden;
		}

		.main-content {
			flex-grow: 1;
			padding: 30px;
			overflow-y: auto;
			max-height: calc(100vh - 40px);
		}

		.sidebar {
			width: 250px;
			background-color: #ffffff;
			color: #333333;
			padding: 30px 20px;
			border-radius: 10px 0 0 10px;
			box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
			position: sticky;
			top: 20px;
			height: calc(100vh - 40px);
			overflow-y: auto;
		}

		.sidebar ul {
			list-style-type: none;
			padding: 0;
		}

		.sidebar li {
			margin-bottom: 15px;
		}

		.sidebar a {
			color: #333333;
			text-decoration: none;
			font-size: 16px;
			display: block;
			padding: 12px 15px;
			border-radius: 5px;
			transition: all 0.3s ease;
		}

		.sidebar a:hover {
			background-color: #f0f0f0;
		}

		.sidebar a.active {
			background: linear-gradient(to bottom, #3498db, #2980b9);
			color: #ffffff;
		}

		.card {
			background-color: #fff;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			padding: 20px;
			margin-bottom: 30px;
			position: relative;
		}

		.card h3 {
			margin-top: 0;
			margin-bottom: 20px;
			color: #1a237e;
		}

		.button-group {
			display: flex;
			gap: 10px;
			position: absolute;
			top: 20px;
			right: 20px;
		}

		.button {
			padding: 8px 15px;
			background: linear-gradient(45deg, #4a90e2, #63b8ff);
			color: #fff;
			border: none;
			border-radius: 5px;
			cursor: pointer;
			transition: background-color 0.3s;
		}

		.button:hover {
			background: linear-gradient(45deg, #3a80d2, #53a8ff);
		}

		/* Section 1 优化 */
		#section1 .suggest-container {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20px;
			padding: 15px;
		}

		#section1 .dashboard-item {
			background-color: #ffffff;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			padding: 20px;
			transition: box-shadow 0.3s ease;
		}

		#section1 .dashboard-item:hover {
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
		}

		#section1 .dashboard-item h3 {
			margin-top: 0;
			margin-bottom: 15px;
			color: #4a90e2;
			font-size: 18px;
		}

		#section1 .checkbox-item {
			margin-bottom: 10px;
			padding: 8px;
			background-color: #f9f9f9;
			border-radius: 5px;
			transition: background-color 0.3s ease;
		}

		#section1 .checkbox-item:hover {
			background-color: #f0f0f0;
		}

		#section1 .checkbox-item input[type="checkbox"] {
			margin-right: 10px;
			transform: scale(1.2);
		}

		#section1 .checkbox-item label {
			display: inline-block;
			vertical-align: middle;
			font-size: 16px;
			line-height: 1.4;
			color: #333;
			cursor: pointer;
		}

		/* 其他部分保持不变 */
		.dashboard-container {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 20px;
			padding: 15px;
		}

		.dashboard-item {
			background-color: #ffffff;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			padding: 20px;
			transition: box-shadow 0.3s ease;
		}

		.dashboard-item:hover {
			box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
		}

		.dashboard-item h3 {
			margin-top: 0;
			margin-bottom: 15px;
			color: #4a90e2;
			font-size: 18px;
		}

		.checkbox-group {
			display: flex;
			flex-direction: column;
		}

		.checkbox-group .checkbox-item {
			margin-bottom: 10px;
			padding: 8px;
			background-color: #f9f9f9;
			border-radius: 5px;
			transition: background-color 0.3s ease;
		}

		.checkbox-group .checkbox-item:hover {
			background-color: #f0f0f0;
		}

		.checkbox-item input[type="checkbox"] {
			margin-right: 10px;
			transform: scale(1.2);
		}

		.checkbox-item label {
			display: inline-block;
			vertical-align: middle;
			font-size: 16px;
			line-height: 1.4;
			color: #333;
			cursor: pointer;
		}

		.file-list {
			margin-top: 20px;
		}

		.file-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px;
			background-color: #f5f5f5;
			border-radius: 5px;
			margin-bottom: 10px;
		}

		.file-item button {
			padding: 5px 10px;
			background-color: #f44336;
			color: #fff;
			border: none;
			border-radius: 3px;
			cursor: pointer;
		}

		.upload-wrapper {
			margin-top: 20px;
		}

		.upload-button {
			padding: 10px 20px;
			background: linear-gradient(45deg, #4a90e2, #63b8ff);
			color: #fff;
			border: none;
			border-radius: 5px;
			cursor: pointer;
			transition: background-color 0.3s;
		}

		.upload-button:hover {
			background: linear-gradient(45deg, #3a80d2, #53a8ff);
		}

		#fileUpload {
			display: none;
		}

		.submit-button {
			display: block;
			width: 100%;
			padding: 15px;
			background: linear-gradient(45deg, #4a90e2, #63b8ff);
			color: #fff;
			border: none;
			border-radius: 5px;
			font-size: 18px;
			cursor: pointer;
			transition: background-color 0.3s;
		}

		.submit-button:hover {
			background: linear-gradient(45deg, #3a80d2, #53a8ff);
		}
		
		/* dashboard-title 样式 */
		.dashboard-title {
			display: flex;
			align-items: center;
		}

		.dashboard-title h3 {
			margin: 0;
			font-size: 28px;
			color: #4a90e2;
		}

		.dashboard-title label {
			margin-left: 10px;
			font-size: 18px;
			color: #4a90e2;
			font-weight: 500;
		}
		
		/* 在现有样式中添加 */
		#section1_1 {
			margin: 30px 0;
		}

		#section1_1 .form-container {
			padding: 20px;
		}

		#section1_1 .two-columns-container {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20px;
			margin-bottom: 20px;
		}

		#section1_1 .blocks {
			position: relative;
			width: calc(100% - 20px);
			display: flex;
			align-items: center;
			min-height: 36px;
			margin-bottom: 20px;
		}

		#section1_1 .titles {
			color: #4a90e2;
			width: 120px;
			font-size: 14px;
			flex-shrink: 0;
			text-align: left;
			margin-right: 10px;
		}

		#section1_1 .selects,
		#section1_1 .inputs {
			flex: 1;
			height: 36px;
			border: 1px solid #e0e0e0;
			border-radius: 4px;
			padding: 0 12px;
			font-size: 14px;
			color: #333;
			background-color: #fff;
			outline: none;
			transition: all 0.3s;
		}

		#section1_1 .selects {
			appearance: none;
			background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
			background-repeat: no-repeat;
			background-position: right 8px center;
			background-size: 16px;
			padding-right: 30px;
		}

		#section1_1 select:disabled,
		#section1_1 input:disabled {
			background-color: #f5f5f5;
			border-color: #e0e0e0;
			color: #999;
			cursor: not-allowed;
		}

		#section1_1 .selected-item .selects,
		#section1_1 .selected-item .inputs {
			border-color: #4a90e2;
			background-color: #f8f9ff;
		}
		
		.project-container {
			padding: 20px;
			background-color: #fff;
			border-radius: 5px;
		}

		.project-block {
			margin-bottom: 15px;
		}

		.project-title {
			margin-bottom: 5px;
			font-weight: bold;
			color: #333;
		}

		.project-select {
			width: 100%;
			padding: 8px;
			border: 1px solid #ddd;
			border-radius: 4px;
			background-color: white;
		}

		.project-input {
			width: 100%;
			padding: 8px;
			border: 1px solid #ddd;
			border-radius: 4px;
		}

		.project-submit {
			padding: 8px 20px;
			background-color: #007bff;
			color: white;
			border: none;
			border-radius: 4px;
			cursor: pointer;
		}

		.project-submit:hover {
			background-color: #0056b3;
		}
		
		/* 添加以下样式来隐藏数字输入框的上下箭头 */
		input[type="number"]::-webkit-inner-spin-button,
		input[type="number"]::-webkit-outer-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}

		input[type="number"] {
			-moz-appearance: textfield;
		}

		/* 期望预算金额输入框样式 */
		#expect-money {
			width: 100%;
			height: 36px;
			border: 1px solid #e0e0e0;
			border-radius: 4px;
			padding: 0 12px;
			font-size: 14px;
			color: #333;
			background-color: #fff;
			outline: none;
			transition: all 0.3s;
			box-sizing: border-box;
		}

		#expect-money:focus {
			border-color: #4a90e2;
			box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
		}
	</style>
</head>

<body>
	<div class="dashboard">
		<aside class="sidebar">
			<ul>
				<li><a href="#section5" class="active">项目名称</a></li>
				<li><a href="#section1">选择建设单位</a></li>
				<li><a href="#section1_1">调整必要参数</a></li>
				<li><a href="#section2">选择生成章节</a></li>
				<li><a href="#section3">上传参考文件</a></li>
				<li><a href="#section4">同步生成估算书</a></li>
			</ul>
		</aside>
		<main class="main-content">
			<section id="section5" class="card">
				<div class="dashboard-title">
					<h3>项目名称：</h3>
					<label id="projectNameLabel"></label>
				</div>
				
				<!-- 添加项目选择相关内容 -->
				<div class="project-container">
					<div class="project-block">
						<div class="project-title">一级分类</div>
						<select class="project-select select-1">
							<option value="" disabled selected>选择一个选项</option>
							<option value="两大基础">两大基础</option>
							<option value="四位一体赋能">四位一体赋能</option>
							<option value="两项能力">两项能力</option>
							<option value="1个生态">1个生态</option>
							<option value="数字化管理体系">数字化管理体系</option>
						</select>
					</div>
					<div class="project-block">
						<div class="project-title">二级分类</div>
						<select class="project-select select-2">
							<option value="" disabled selected>选择一个选项</option>
						</select>
					</div>
					<div class="project-block">
						<div class="project-title">三级分类</div>
						<select class="project-select select-3">
							<option value="" disabled selected>选择一个选项</option>
						</select>
					</div>
					<div class="project-block">
						<div class="project-title">项目类</div>
						<select class="project-select select-4">
							<option value="" disabled selected>选择一个选项</option>
						</select>
					</div>
					<div class="project-block">
						<div class="project-title">项目名称</div>
						<input type="text" class="project-input input-1" placeholder="请输入项目名称">
					</div>
					<div class="project-block">
						<button class="project-submit" onclick="submit()">确定</button>
					</div>
				</div>
				
			</section>

			<section id="section1" class="card">
				<h3>一、选择建设单位</h3>
				<div class="button-group">
					<button class="button select-all-suggest">全选</button>
					<button class="button invert-selection-suggest">反选</button>
				</div>
				<div class="dashboard-container suggest-container">
					
					<div class="dashboard-item">
						<h3>管制业务单位</h3>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox1">
							<label for="checkbox1">南方电网</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox2">
							<label for="checkbox2">广东电网公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox3">
							<label for="checkbox3">广西电网公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox4">
							<label for="checkbox4">云南电网公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox5">
							<label for="checkbox5">贵州电网公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox6">
							<label for="checkbox6">海南电网公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox7">
							<label for="checkbox7">深圳供电局</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox8">
							<label for="checkbox8">南网超高压公司</label>
						</div>
					</div>
					
					<div class="dashboard-item">
						<h3>新兴业务单位</h3>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox50">
							<label for="checkbox50">南网储能公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox51">
							<label for="checkbox51">南网产业投资集团</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox52">
							<label for="checkbox52">鼎元资产公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox53">
							<label for="checkbox53">南网能源公司</label>
						</div>
					</div>
					
					<div class="dashboard-item">
						<h3>国际业务单位</h3>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox54">
							<label for="checkbox54">南网国际（香港）公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox55">
							<label for="checkbox55">南网澜湄国际公司</label>
						</div>
					</div>
					
					<div class="dashboard-item">
						<h3>金融业务单位</h3>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox56">
							<label for="checkbox56">南网资本控股公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox57">
							<label for="checkbox57">南网财务公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox58">
							<label for="checkbox58">鼎和保险公司</label>
						</div>
					</div>
					
					<div class="dashboard-item">
						<h3>共享平台单位</h3>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox59">
							<label for="checkbox59">南网党校</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox60">
							<label for="checkbox60">南网北京分公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox61">
							<label for="checkbox61">南网共享公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox62">
							<label for="checkbox62">南网生态运营公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox63">
							<label for="checkbox63">南网数字集团</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox64">
							<label for="checkbox64">南网供应链集团</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox65">
							<label for="checkbox65">南网能源院</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox66">
							<label for="checkbox66">南网科研院</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox67">
							<label for="checkbox67">广州电力交易中心</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox68">
							<label for="checkbox68">南网传媒公司</label>
						</div>
						<div class="checkbox-item">
							<input type="checkbox" id="checkbox69">
							<label for="checkbox69">北京研究院</label>
						</div>
					</div>
				</div>
			</section>
			
			<!-- 新增的必要参数 section -->
			<section id="section1_1" class="card">
				<h3>选择必要参数</h3>
				<div class="form-container">
					<div class="two-columns-container">
						<div class="blocks block-select-3">
							<div class="titles">项目分类</div>
							<select class="selects select-3">
								<option value="" disabled selected>选择一个选项</option>
								<option value="信息系统建设与升级改造">信息系统建设与升级改造</option>
								<option value="信息基础设施建设与升级改造">信息基础设施建设与升级改造</option>
								<option value="信息安全防护体系建设与升级改造">信息安全防护体系建设与升级改造</option>
								<option value="运行维护">运行维护</option>
								<option value="信息专题研究">信息专题研究</option>
							</select>
						</div>

						<div class="blocks block-select-12">
							<div class="titles">建设周期T（月）</div>
							<select class="selects select-12">
								<option value="" disabled selected>选择一个选项</option>
								<option value="一年内">一年内</option>
								<option value="一年以上两年内">一年以上两年内</option>
								<option value="两年以上">两年以上</option>
							</select>
						</div>

						<div class="blocks block-select-1">
							<div class="titles">项目类型</div>
							<select class="selects select-1">
								<option value="" disabled selected>选择一个选项</option>
								<option value="应用系统类">应用系统类</option>
								<option value="技术支持平台">技术支持平台</option>
								<option value="数据分析应用">数据分析应用</option>
								<option value="移动应用类">移动应用类</option>
								<option value="其他">其他</option>
							</select>
						</div>

						<div class="blocks block-select-7">
							<div class="titles">建设性质</div>
							<select class="selects select-7">
								<option value="" disabled selected>选择一个选项</option>
								<option value="新建">新建</option>
								<option value="升级改造">升级改造</option>
							</select>
						</div>

						<div class="blocks block-select-9">
							<div class="titles">系统等保级别</div>
							<select class="selects select-9">
								<option value="" disabled selected>选择一个选项</option>
								<option value="一级">一级（含互联网用户）</option>
								<option value="一级">一级（不含互联网用户）</option>
								<option value="二级">二级（含互联网用户）</option>
								<option value="二级">二级（不含互联网用户）</option>
								<option value="三级">三级</option>
								<option value="四级">四级</option>
							</select>
						</div>

						<div class="blocks block-input-10">
							<div class="titles">系统用户数量</div>
							<input class="inputs input-10" type="number" min="1" placeholder="请输入系统用户数量">
						</div>

						<div class="blocks block-select-11">
							<div class="titles">系统部署方式</div>
							<select class="selects select-11">
								<option value="" disabled selected>选择一个选项</option>
								<option value="网一级部署模式">网一级部署模式</option>
								<option value="网省两级部署模式">网省两级部署模式</option>
								<option value="省一级部署模式">省一级部署模式</option>
								<option value="省地两级部署模式">省地两级部署模式</option>
								<option value="省地县三级部署模式">省地县三级部署模式</option>
								<option value="网一级管理节点和省一级分节点部署模式">网一级管理节点和省一级分节点部署模式</option>
							</select>
						</div>
					</div>
				</div>
			</section>

			<section id="section2" class="card">
				<h3>二、选择生成章节</h3>
				<div class="button-group">
					<button class="button select-all-menu">全选</button>
					<button class="button invert-selection-menu">反选</button>
				</div>
				<div class="dashboard-container">
					<div class="dashboard-item">
						<h3>1. 概述</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox9">
								<label for="checkbox9">1.1项目背景</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox10">
								<label for="checkbox10">1.2项目依据</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox11">
								<label for="checkbox11">1.3项目目标</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox12">
								<label for="checkbox12">1.4项目范围</label>
							</div>
						</div>
						
						<h3>2.项目现状及必要性分析</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox13">
								<label for="checkbox13">2.1现状分析</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox14">
								<label for="checkbox14">2.2需求分析</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox15">
								<label for="checkbox15">2.3必要性结论</label>
							</div>
						</div>
						
						<h3>3.项目方案</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox16">
								<label for="checkbox16">3.1业务架构</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox17">
								<label for="checkbox17">3.2应用架构</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox18">
								<label for="checkbox18">3.3数据架构</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox19">
								<label for="checkbox19">3.4技术架构</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox20">
								<label for="checkbox20">3.5系统部署方式及软硬件资源需求</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox21">
								<label for="checkbox21">3.6安全技术方案</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox22">
								<label for="checkbox22">3.7项目实施需求</label>
							</div>
						</div>
						
						<h3>4.项目投资估算</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox23">
								<label for="checkbox23">4.1投资依据说明</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox24">
								<label for="checkbox24">4.2总投资</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox25">
								<label for="checkbox25">4.3资金计划建议</label>
							</div>
						</div>
						
						<h3>5.项目效益分析</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox26">
								<label for="checkbox26">5.1管理效益分析</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox27">
								<label for="checkbox27">5.2经济效益分析</label>
							</div>
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox28">
								<label for="checkbox28">5.3社会效益分析</label>
							</div>
						</div>
						
						<h3>6.项目风险分析</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox29">
								<label for="checkbox29">6.1项目风险分析</label>
							</div>
						</div>
						
						<h3>7.项目可研结论</h3>
						<div class="checkbox-group">
							<div class="checkbox-item">
								<input type="checkbox" id="checkbox30">
								<label for="checkbox30">7.1项目可研结论</label>
							</div>
						</div>
					</div>
					
				</div>
			</section>

			<section id="section3" class="card">
				<h3>三、上传参考文件</h3>
				<div class="file-list" id="fileList">
					<!-- 文件列表将通过 JavaScript 动态生成 -->
				</div>
				<div class="upload-wrapper">
					<button id="uploadButton" class="upload-button">选择文件</button>
				</div>
			</section>

			<section id="section4" class="card">
				<h3>四、同步生成估算书</h3>
				<div class="checkbox-group">
					<div class="checkbox-item">
						<input type="checkbox" id="checkbox49" checked>
						<label for="checkbox49">同步生成估算书</label>
					</div>
				</div>
				<input type="number" id="expect-money" placeholder="期望预算金额（万元）：" class="search-input">
			</section>

			<button id="submit" class="submit-button">确认生成</button>
		</main>
	</div>

	<script>
		const projectData = {
			"两大基础": {
                    "数字基础设施": {
                        "“3+1+X”数字基础设施建设": [
                            "网级数据中心建设",
                            "海外数据中心建设",
                            "省级分布式数据中心升级改造",
                            "边缘数据中心升级改造",
                            "资源租赁"
                        ],
                        "算力建设": [
                            " 南网云（经营管理节点）IaaS及PaaS采购",
                            "南网云（调度节点）IaaS及PaaS采购",
                            "南网公有云IaaS及PaaS采购"
                        ],
                        "管": [
                            "局域网建设及升级改造",
                            "通信网络建设"
                        ],
                        "边": [
                            "边缘算力节点建设"
                        ],
                        "端": [
                            "终端设备采购",
                            "终端软件采购",
                            "传感及智能网关采购",
                            "计量表计采购"
                        ]
                    },
                    "数据资源体系": {
                        "数据资产顶层设计": [
                            "数据资产顶层设计"
                        ],
                        "数据资产治理": [
                            "数据资产治理"
                        ],
                        "数据资产运营": [
                            "数据资产运营"
                        ],
                        "数据流通": [
                            "数据流通建设"
                        ]
                    }
                },
                "四位一体赋能": {
                    "数字技术平台": {
                        "南网云平台": [
                            "南网云平台建设"
                        ],
                        "物联网平台": [
                            "物联网平台建设"
                        ],
                        "人工智能平台": [
                            "人工智能平台建设"
                        ],
                        "移动应用平台": [
                            "移动应用平台建设"
                        ],
                        "区块链平台": [
                            "区块链平台建设"
                        ],
                        "南网智瞰（电网GIS平台）": [
                            "南网智瞰（电网GIS平台）建设"
                        ],
                        "南网智瞰（个性化应用）": [
                            "南网智瞰（个性化应用）建设"
                        ],
                        "研发平台": [
                            "研发平台建设"
                        ],
                        "数字身份与访问管理平台": [
                            "数字身份与访问管理平台建设"
                        ],
                        "统一密码服务平台": [
                            "统一密码服务平台建设"
                        ],
                        "云化数据中心": [
                            "云化数据中心建设",
                            "自主空间",
                            "南网智搜"
                        ],
                        "北斗": [
                            "北斗运营服务应用建设"
                        ],
                        "技术平台推广": [
                            "技术平台推广应用"
                        ]
                    },
                    "企业级中台": {
                        "应用中台": [
                            "应用中台建设"
                        ],
                        "技术中台": [
                            "技术中台建设"
                        ],
                        "数据中台": [
                            "数据中台建设"
                        ],
                        "安全中台": [
                            "安全中台建设"
                        ],
                        "中台管理平台": [
                            "中台运营管理应用建设"
                        ],
                        "个性化中台建设及推广": [
                            "中台个性化建设及推广应用"
                        ]
                    },
                    "数字电网应用": {
                        "资产全生命（规划）类": [
                            "电网规划管理应用建设",
                            "线损数字化管控应用建设",
                            "新型电力系统示范应用建设",
                            "新型电力系统综合分析及展示建设"
                        ],
                        "资产全生命（建设运维）类": [
                            "电网建设应用建设",
                            "电网安全生产应用建设",
                            "生产技术支持系统建设",
                            "生产指挥系统建设"
                        ],
                        "能量全过程（电网运行）类": [
                            "云边融合的智能调度运行平台建设",
                            "调度运行平台（电网运行管理）建设",
                            "调度运行平台（电网运行管理）个性化应，用建设"
                        ],
                        "能量全过程（电力交易）类": [
                            "数字量测应用建设",
                            "电力交易应用建设",
                            "电力需求响应应用建设"
                        ],
                        "个性化应用": [
                            "数字电网（个性化应用）建设"
                        ]
                    },
                    "数字服务": {
                        "服务全方位类": [
                            "数字服务运营应用建设",
                            "数字渠道建设",
                            "营销现场作业应用建设",
                            "数字营业应用建设",
                            "数字评价应用建设",
                            "产融服务生态建设"
                        ],
                        "个性化应用": [
                            "数字服务（个性化）建设"
                        ]
                    },
                    "数字运营": {
                        "战略发展类": [
                            "战略管理应用建设",
                            "管理体系管理应用建设",
                            "架构管理应用建设",
                            "政策研究应用建设",
                            "深化改革应用建设",
                            "节能环保应用建设",
                            "农电管理应用建设"
                        ],
                        "资产全生命（投资类）": [
                            "前期管理应用建设",
                            "投资计划应用建设"
                        ],
                        "资源集约化类": [
                            "财务管理应用建设",
                            "集团司库管理应用建设",
                            "运营监控应用建设",
                            "人资管理应用建设",
                            "创新管理应用建设",
                            "项目管理应用建设",
                            "数字化项目全过程管理应用建设",
                            "品牌管理应用建设"
                        ],
                        "基础支撑类": [
                            "指标体系管理应用建设",
                            "标准管理应用建设",
                            "质量管理应用建设",
                            "统计管理应用建设",
                            "供应链管理应用建设",
                            "工会管理应用建设",
                            "行政办公应用建设",
                            "后勤管理应用建设",
                            "档案管理应用建设",
                            "年金管理应用建设",
                            "法治应用建设",
                            "风控数智化管理应用建设",
                            "合同管理应用建设",
                            "数字审计应用建设",
                            "电网管理平台（外部门户）建设",
                            "即时通信应用建设"
                        ],
                        "党建与保障类": [
                            "数字党建应用建设",
                            "数字巡视巡察应用建设",
                            "监督执纪应用建设",
                            "国资监管系统建设"
                        ],
                        "业务多元化类": [
                            "通用类统建通用类应用推广",
                            "统推统建应用境外延伸",
                            "国际业务特色应用",
                            "数字金融服务平台",
                            "产业金融业务数字化建设",
                            "“乐学南网”建设",
                            "智库建设",
                            "数字孪生仿真平台",
                            "南方电网知识共享服务平台",
                            "智慧检测平台建设",
                            "数字电网智能算法库",
                            "网络安全靶场建设",
                            "融媒业务应用建设",
                            "网络信息情报态势感知应用建设",
                            "科技家园应用推广",
                            "资讯管理应用",
                            "共享服务支撑体系个性化应用建设"
                        ],
                        "运营管控类": [
                            "云景数字化运营管控平台公共应用建设",
                            "云景数字化运营管控平台管制业务个性化应用建设",
                            "云景数字化运营管控平台管制业务个性化应用建设"
                        ]
                    },
                    "数字产业": {
                        "绿色用能产业": [
                            "绿色用能产品研发及推广"
                        ],
                        "数据产品产业": [
                            "数据中心对外门户",
                            "能源数据产品",
                            "面向数字政府及行业机构应用"
                        ],
                        "技术装备产业": [
                            "数字电网智能技术及装备产品研发"
                        ],
                        "产业协同": [
                            "规划协同应用",
                            "设计协同应用",
                            "建设产业生态应用",
                            "智能制造应用",
                            "开源社区建设"
                        ]
                    }
                },
                "两项能力": {
                    "网络安全运行保护": {
                        "保障单元": [
                            "服务管理",
                            "数字化运行服务平台维护及技术服务",
                            "IT资产维护",
                            "网络安全运维",
                            "运维工器具建设",
                            "应用系统版本发布测试",
                            "运维服务",
                            "资源管理",
                            "数字化安全运行调控中心建设",
                            "数字化安全运行调控中心技术服务",
                            "网络安全运行技术服务",
                            "网络安全运行技术服务"
                        ],
                        "技术单元": [
                            "网络安全技术防护",
                            "网络安全技术防护",
                            "互联网应用安全监测系统建设",
                            "统一威胁情报平台建设",
                            "物联网安全建设",
                            "数据安全管理",
                            "桌面级移动终端安全防护",
                            "网络安全弹性系统",
                            "各安全域服务平台及安全能力建设",
                            "商业秘密安全保护平台建设",
                            "电力监控安全态势感知平台建设及应用",
                            "电力监控系统商用密码保障系统",
                            "数字化运行服务平台建设",
                            "运维工具采购",
                            "保底系统建设",
                            "应用级灾备建设",
                            "南网云平台灾备建设",
                            "云化数据中心灾备建设",
                            "中台灾备建设",
                            "灾备建设",
                            "云盾平台建设"
                        ],
                        "监督单元": [
                            "网络安全运行监督"
                        ]
                    },
                    "数字技术创新": {
                        "技术创新": [
                            "前瞻技术研究",
                            "智能产品研发",
                            "重大工程",
                            "自主创新",
                            "自主可控改造",
                            "技术创新发展新模式"
                        ]
                    }
                },
                "1个生态": {
                    "数字生态": {
                        "数字生态": [
                            "内部生态建设",
                            "国资行业云建设",
                            "能源行业生态建设",
                            "数字化转型机制研究",
                            "数字化转型宣传"
                        ]
                    }
                },
                "数字化管理体系": {
                    "数字化管理体系": {
                        "数字化管理体系": [
                            "数字化管理体系机制完善"
                        ]
                    },
                    "数字化规划管理": {
                        "规划管理": [
                            "数字化规划编制与修编",
                            "数字化规划实施计划编制与修编"
                        ],
                        "架构管理": [
                            "企业架构资产更新及管控",
                            "企业架构资产更新及管控"
                        ],
                        "技术管理": [
                            "数字化标准体系建设",
                            "技术管控",
                            "技术监督",
                            "数据技术专题研究及示范应用",
                            "数据技术专题研究及示范应用"
                        ],
                        "前期管理": [
                            "信息化项目前期专项",
                            "数字化项目前期专项"
                        ],
                        "投资后评价": [
                            "数字化项目后评价",
                            "数字化年度投资评价"
                        ],
                        "考核评价": [
                            "数字化创新发展指数考核"
                        ],
                        "评测": [
                            "数字化水平评价",
                            "数字化测评"
                        ]
                    },
                    "建设管理": {
                        "质量管理": [
                            "全过程质量管控体系建设",
                            "实验室建设",
                            "自主可控适配测试",
                            "统一研发体系建设",
                            "数字化测评体系建设"
                        ],
                        "数字化宣传与人才培养": [
                            "数字化素养培训"
                        ]
                    }
                }
            }
		
		// 确保 DOM 加载完成后再执行
		document.addEventListener('DOMContentLoaded', function() {
			// 获取所有的 select 元素
			const select1 = document.querySelector('.project-select.select-1');
			const select2 = document.querySelector('.project-select.select-2');
			const select3 = document.querySelector('.project-select.select-3');
			const select4 = document.querySelector('.project-select.select-4');

			// 为 select 元素添加事件监听器
			select1.addEventListener('change', changeSelect1);
			select2.addEventListener('change', changeSelect2);
			select3.addEventListener('change', changeSelect3);
		});

		function changeSelect1() {
			const select1 = document.querySelector('.project-select.select-1');
			const select2 = document.querySelector('.project-select.select-2');
			const select3 = document.querySelector('.project-select.select-3');
			const select4 = document.querySelector('.project-select.select-4');

			// 清空后续选择框
			select2.innerHTML = '<option value="" disabled selected>选择一个选项</option>';
			select3.innerHTML = '<option value="" disabled selected>选择一个选项</option>';
			select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

			const value1 = select1.value;
			if (value1 && projectData[value1]) {
				Object.keys(projectData[value1]).forEach(key => {
					const option = document.createElement('option');
					option.value = key;
					option.textContent = key;
					select2.appendChild(option);
				});
			}
		}

		function changeSelect2() {
			const select1 = document.querySelector('.project-select.select-1');
			const select2 = document.querySelector('.project-select.select-2');
			const select3 = document.querySelector('.project-select.select-3');
			const select4 = document.querySelector('.project-select.select-4');

			select3.innerHTML = '<option value="" disabled selected>选择一个选项</option>';
			select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

			const value1 = select1.value;
			const value2 = select2.value;
			if (value1 && value2 && projectData[value1][value2]) {
				Object.keys(projectData[value1][value2]).forEach(key => {
					const option = document.createElement('option');
					option.value = key;
					option.textContent = key;
					select3.appendChild(option);
				});
			}
		}

		function changeSelect3() {
			const select1 = document.querySelector('.project-select.select-1');
			const select2 = document.querySelector('.project-select.select-2');
			const select3 = document.querySelector('.project-select.select-3');
			const select4 = document.querySelector('.project-select.select-4');

			select4.innerHTML = '<option value="" disabled selected>选择一个选项</option>';

			const value1 = select1.value;
			const value2 = select2.value;
			const value3 = select3.value;
			if (value1 && value2 && value3 && projectData[value1][value2][value3]) {
				projectData[value1][value2][value3].forEach(item => {
					const option = document.createElement('option');
					option.value = item;
					option.textContent = item;
					select4.appendChild(option);
				});
			}
		}

		function submit() {
			const select1 = document.querySelector('.project-select.select-1');
			const select2 = document.querySelector('.project-select.select-2');
			const select3 = document.querySelector('.project-select.select-3');
			const select4 = document.querySelector('.project-select.select-4');
			const input1 = document.querySelector('.project-input.input-1');

			if (!select1.value || !select2.value || !select3.value || !select4.value || !input1.value) {
				alert('请填写所有必填项！');
				return;
			}

			const projectType = `${select4.value}（${input1.value}）`;

			try {
				// 更新项目名称标签
				document.getElementById('projectNameLabel').textContent = projectType;

				// 发送消息到 C#，使用 projectUpdate 类型
				window.chrome.webview.postMessage({
					type: 'projectUpdate',
					projectType: projectType
				});

				// 清空选择框和输入框
				select1.value = '';
				select2.value = '';
				select3.value = '';
				select4.value = '';
				input1.value = '';

			} catch (e) {
				console.error('发送消息失败:', e);
				alert('提交失败，请重试！');
			}
		}
		
		
        // 文件上传相关代码
        document.addEventListener('DOMContentLoaded', function () {
            const uploadButton = document.getElementById('uploadButton');
            if (uploadButton) {
                uploadButton.addEventListener('click', function () {
                    console.log("触发文件上传");
                    window.chrome.webview.postMessage({
                        action: 'uploadFiles'
                    });
                });
            }

            // 页面加载完成后请求文件列表
            setTimeout(function () {
                requestUploadedFiles();
            }, 1000);
        });

        // 请求已上传文件列表
        function requestUploadedFiles() {
            try {
                console.log("请求已上传文件列表");
                window.chrome.webview.postMessage({
                    action: 'getUploadedFiles'
                });
            } catch (error) {
                console.error('请求文件列表时出错:', error);
            }
        }

        // 更新文件列表显示
        function updateFileList(files) {
            console.log("更新文件列表:", files);
            const fileListElement = document.getElementById('fileList');
            if (!fileListElement) return;

            fileListElement.innerHTML = '';

            if (files && files.length > 0) {
                const list = document.createElement('ul');
                list.className = 'file-items';
                list.style.listStyle = 'none';
                list.style.padding = '0';

                files.forEach(file => {
                    const item = document.createElement('li');
                    item.className = 'file-item';

                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'file-info';
                    fileInfo.style.display = 'flex';
                    fileInfo.style.alignItems = 'center';
                    fileInfo.style.flex = '1';

                    const fileIcon = document.createElement('span');
                    fileIcon.className = 'file-icon';
                    fileIcon.innerHTML = '📄';
                    fileIcon.style.marginRight = '10px';

                    const fileName = document.createElement('span');
                    fileName.className = 'file-name';
                    fileName.textContent = file;

                    fileInfo.appendChild(fileIcon);
                    fileInfo.appendChild(fileName);

                    const deleteButton = document.createElement('button');
                    deleteButton.className = 'delete-button';
                    deleteButton.textContent = '删除';
                    deleteButton.style.padding = '5px 10px';
                    deleteButton.style.backgroundColor = '#f44336';
                    deleteButton.style.color = '#fff';
                    deleteButton.style.border = 'none';
                    deleteButton.style.borderRadius = '3px';
                    deleteButton.style.cursor = 'pointer';
                    deleteButton.onclick = function () {
                        deleteFile(file);
                    };

                    item.appendChild(fileInfo);
                    item.appendChild(deleteButton);
                    list.appendChild(item);
                });

                fileListElement.appendChild(list);
            } else {
                const emptyMessage = document.createElement('p');
                emptyMessage.className = 'empty-message';
                emptyMessage.textContent = '暂无已上传文件';
                emptyMessage.style.textAlign = 'center';
                emptyMessage.style.color = '#999';
                fileListElement.appendChild(emptyMessage);
            }
        }

        // 删除文件
        function deleteFile(fileName) {
            if (confirm(`确定要删除文件 "${fileName}" 吗？`)) {
                window.chrome.webview.postMessage({
                    action: 'deleteFile',
                    fileName: fileName
                });
            }
        }



		
        // 添加到现有的 script 标签中
        function updateProjectName(projectName) {
            const projectLabel = document.querySelector('#projectNameLabel');
            if (projectLabel) {
                projectLabel.textContent = projectName || ''; // 处理 null 或 undefined 的情况
            } else {
                console.warn('Project name label not found');
            }
        }

        // 注册消息处理函数，接收来自C#的消息
        window.chrome.webview.addEventListener('message', function (event) {
            if (event.data.type === 'projectName') {
                updateProjectName(event.data.value);
            }
        });

        // 全选功能
        function selectAll(containerClass) {
            const checkboxes = document.querySelectorAll(`#${containerClass} ${containerClass === 'section1' ? '.suggest-container' : '.checkbox-group'} input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: containerClass === 'section1' ? 'enterprise' : 'menu',
                    id: checkbox.id,
                    checked: true,
                    label: label
                });
            });
        }

        // 反选功能
        function invertSelection(containerClass) {
            const checkboxes = document.querySelectorAll(`#${containerClass} ${containerClass === 'section1' ? '.suggest-container' : '.checkbox-group'} input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = !checkbox.checked;
                const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: containerClass === 'section1' ? 'enterprise' : 'menu',
                    id: checkbox.id,
                    checked: checkbox.checked,
                    label: label
                });
            });
        }

        // 绑定事件到按钮
        document.querySelector('.select-all-suggest').addEventListener('click', () => selectAll('section1'));
        document.querySelector('.invert-selection-suggest').addEventListener('click', () => invertSelection('section1'));

        document.querySelector('.select-all-menu').addEventListener('click', () => selectAll('section2'));
        document.querySelector('.invert-selection-menu').addEventListener('click', () => invertSelection('section2'));

        // checkbox1 的特殊逻辑
        document.getElementById('checkbox1').addEventListener('change', function () {
            if (this.checked) {
                // 如果 checkbox1 被选中，自动选中 checkbox2 到 checkbox6
                for (let i = 2; i <= 6; i++) {
                    const checkbox = document.getElementById(`checkbox${i}`);
                    checkbox.checked = true;
                    // 为每个自动选中的复选框也发送消息
                    const label = document.querySelector(`label[for="checkbox${i}"]`).textContent;
                    window.chrome.webview.postMessage({
                        type: 'enterprise',
                        id: `checkbox${i}`,
                        checked: true,
                        label: label
                    });
                }
            }
        });

        // 文件上传和管理
        const fileUpload = document.getElementById('fileUpload');
        const fileList = document.getElementById('fileList');
        let uploadedFiles = [];
        let isFileDialogOpen = false;

        function showUploadedFiles() {
            fileList.innerHTML = '<h4>已上传文件列表</h4>';
            uploadedFiles.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = file;

                const deleteButton = document.createElement('button');
                deleteButton.textContent = '删除';
                deleteButton.addEventListener('click', () => {
                    uploadedFiles = uploadedFiles.filter(f => f !== file);
                    showUploadedFiles();
                    // 通知 C# 代码文件已被删除
                    window.chrome.webview.postMessage({ action: 'deleteFile', fileName: file });
                });

                fileItem.appendChild(deleteButton);
                fileList.appendChild(fileItem);
            });
        }

        // 更新文件列表的函数（由 C# 代码调用）
        function updateFileList(files) {
            uploadedFiles = files;
            showUploadedFiles();
        }

        document.querySelector('.upload-button').addEventListener('click', function (event) {
            if (!isFileDialogOpen) {
                isFileDialogOpen = true;
                document.getElementById('fileUpload').click();
            }
            event.preventDefault(); // 防止默认行为
        });

        fileUpload.addEventListener('change', () => {
            isFileDialogOpen = false;
            // 通知 C# 代码处理文件上传
            window.chrome.webview.postMessage({ action: 'uploadFiles', files: Array.from(fileUpload.files).map(f => f.name) });
        });

        // 添加一个点击事件监听器来重置isFileDialogOpen
        document.addEventListener('click', function () {
            isFileDialogOpen = false;
        });

        // 获取所有侧边栏链接
        const sidebarLinks = document.querySelectorAll('.sidebar a');

        // 为每个链接添加点击事件监听器
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function (e) {
                // 阻止默认的链接跳转行为
                e.preventDefault();

                // 移除所有链接的 active 类
                sidebarLinks.forEach(l => l.classList.remove('active'));

                // 为被点击的链接添加 active 类
                this.classList.add('active');

                // 获取目标部分的 ID
                const targetId = this.getAttribute('href').substring(1);

                // 滚动到目标部分
                document.getElementById(targetId).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // 页面加载时，根据当前 URL 设置初始 active 状态
        function setInitialActiveState() {
            const currentHash = window.location.hash;
            if (currentHash) {
                const activeLink = document.querySelector(`.sidebar a[href="${currentHash}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
            } else {
                // 如果没有 hash，默认激活第一个链接
                sidebarLinks[0].classList.add('active');
            }
        }

        // 页面加载完成后设置初始状态
        window.addEventListener('load', setInitialActiveState);

        function restoreCheckboxStates(states) {
            for (let id in states) {
                const checkbox = document.getElementById(id);
                if (checkbox) {
                    checkbox.checked = states[id];
                }
            }
        }

        // 同步复选框状态到 VSTO 外接程序
        document.querySelectorAll('#section1 .suggest-container input[type="checkbox"]').forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: 'enterprise',
                    id: this.id,
                    checked: this.checked,
                    label: label
                });
            });
        });

        document.querySelectorAll('#section2 .checkbox-group input[type="checkbox"]').forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                window.chrome.webview.postMessage({
                    type: 'menu',
                    id: this.id,
                    checked: this.checked,
                    label: label
                });
            });
        });

        document.getElementById('checkbox49').addEventListener('change', function () {
            window.chrome.webview.postMessage({
                type: 'estimate',
                checked: this.checked
            });
        });

        document.getElementById('expect-money').addEventListener('input', function () {
            window.chrome.webview.postMessage({
                type: 'expectMoney',
                value: this.value
            });
        });

        // 添加用于接收更新的函数
        function updateEstimateCheckbox(checked) {
            const checkbox = document.getElementById('checkbox49');
            if (checkbox) {
                checkbox.checked = checked;
            }
        }

        function updateExpectMoney(value) {
            const input = document.getElementById('expect-money');
            if (input) {
                input.value = value;
            }
        }

        document.getElementById('submit').addEventListener('click', function () {
            window.chrome.webview.postMessage({
                type: 'generate'
            });
        });

        function updateFormState(state) {
            if (!state) return;

            console.log("接收到表单状态:", state); // 添加日志
            
            try {
                // 更新项目分类
                const select3 = document.querySelector('.select-3');
                if (select3) {
                    select3.value = state.select_3 || "";
                    // 触发项目类型变化的处理函数
                    handleProjectTypeChange();
                }

                // 更新建设周期
                const select12 = document.querySelector('.select-12');
                if (select12) {
                    select12.value = state.select_12 || "";
                }

                // 强制显示所有依赖字段，然后设置值
                if (state.select_3 === '信息系统建设与升级改造') {
                    // 先显示所有依赖字段
                    const dependentBlocks = [
                        '.block-select-1',
                        '.block-select-7',
                        '.block-select-9',
                        '.block-input-10',
                        '.block-select-11'
                    ];

                    dependentBlocks.forEach(selector => {
                        const block = document.querySelector(selector);
                        if (!block) return;

                        const element = block.querySelector('select, input');
                        if (!element) return;

                        // 显示并启用元素
                        block.style.display = 'flex';
                        element.disabled = false;
                    });

                    // 现在设置各个字段的值
                    // 更新项目类型
                    const select1 = document.querySelector('.select-1');
                    if (select1) {
                        select1.value = state.select_1 || "";
                    }

                    // 更新建设性质
                    const select7 = document.querySelector('.select-7');
                    if (select7) {
                        select7.value = state.select_7 || "";
                    }

                    // 更新系统等保级别
                    const select9 = document.querySelector('.select-9');
                    if (select9) {
                        select9.value = state.select_9 || "";
                    }

                    // 更新系统用户数量
                    const input10 = document.querySelector('.input-10');
                    if (input10) {
                        input10.value = state.input_10 !== undefined ? state.input_10 : "";
                    }

                    // 更新系统部署方式
                    const select11 = document.querySelector('.select-11');
                    if (select11) {
                        select11.value = state.select_11 || "";
                    }
                } else {
                    // 如果不是信息系统建设与升级改造，隐藏依赖字段
                    const dependentBlocks = [
                        '.block-select-1',
                        '.block-select-7',
                        '.block-select-9',
                        '.block-input-10',
                        '.block-select-11'
                    ];

                    dependentBlocks.forEach(selector => {
                        const block = document.querySelector(selector);
                        if (!block) return;

                        const element = block.querySelector('select, input');
                        if (!element) return;

                        block.style.display = 'none';
                        element.disabled = true;
                        element.value = '';
                    });
                }

                // 更新所有选择框的样式
                document.querySelectorAll('.selects, .inputs').forEach(element => {
                    const parentBlock = element.closest('.blocks');
                    if (element.value) {
                        parentBlock?.classList.add('selected-item');
                    } else {
                        parentBlock?.classList.remove('selected-item');
                    }
                });

                console.log("表单状态更新完成");
            } catch (error) {
                console.error("更新表单状态出错:", error);
            }
        }

        // 在页面加载完成时恢复表单状态
        document.addEventListener('DOMContentLoaded', function () {
            console.log("页面加载完成，初始化表单");
            
            // 请求后端提供初始化数据
            try {
                window.chrome.webview.postMessage({
                    type: 'getInitialState'
                });
            } catch(e) {
                console.error("请求初始状态时出错:", e);
            }
            
            // 项目分类变化时的特殊处理
            const select3 = document.querySelector('.select-3');
            if (select3) {
                select3.addEventListener('change', handleProjectTypeChange);
                
                // 初始化时触发一次项目分类变化事件，确保依赖字段可见性正确
                setTimeout(() => {
                    console.log("自动触发项目分类change事件，初始值:", select3.value);
                    const event = new Event('change');
                    select3.dispatchEvent(event);
                }, 500);
            }
            
            // 添加表单元素的变化监听
            document.querySelectorAll('.selects, .inputs').forEach(element => {
                element.addEventListener('change', function () {
                    console.log(`表单元素 ${element.className} 值变化为: ${element.value}`);
                    
                    // 构建表单状态对象
                    const formState = {
                        type: 'saveFormState',
                        data: {
                            select_3: document.querySelector('.select-3')?.value || '',
                            select_12: document.querySelector('.select-12')?.value || '',
                            select_1: document.querySelector('.select-1')?.value || '',
                            select_7: document.querySelector('.select-7')?.value || '',
                            select_9: document.querySelector('.select-9')?.value || '',
                            input_10: document.querySelector('.input-10')?.value || '',
                            select_11: document.querySelector('.select-11')?.value || ''
                        }
                    };
                    
                    console.log("发送表单状态更新:", formState);
                    window.chrome.webview.postMessage(formState);
                });
            });

            // 企业和菜单复选框的变更监听
            document.querySelectorAll('#section1 .suggest-container input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                    console.log(`企业复选框 ${this.id} (${label}) 变更为: ${this.checked}`);
                    
                    window.chrome.webview.postMessage({
                        type: 'enterprise',
                        id: this.id,
                        checked: this.checked,
                        label: label
                    });
                });
            });
            
            document.querySelectorAll('#section2 .checkbox-group input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const label = document.querySelector(`label[for="${this.id}"]`).textContent;
                    console.log(`菜单复选框 ${this.id} (${label}) 变更为: ${this.checked}`);
                    
                    window.chrome.webview.postMessage({
                        type: 'menu',
                        id: this.id,
                        checked: this.checked,
                        label: label
                    });
                });
            });
        });

        // 处理项目类型变化
        function handleProjectTypeChange() {
            const select3 = document.querySelector('.select-3');
            const dependentBlocks = [
                '.block-select-1',
                '.block-select-7',
                '.block-select-9',
                '.block-input-10',
                '.block-select-11'
            ];

            const isInfoSystemType = select3.value === '信息系统建设与升级改造';

            dependentBlocks.forEach(selector => {
                const block = document.querySelector(selector);
                if (!block) return;

                const element = block.querySelector('select, input');
                if (!element) return;

                if (isInfoSystemType) {
                    block.style.display = 'flex';
                    element.disabled = false;
                } else {
                    block.style.display = 'none';
                    element.disabled = true;
                    element.value = '';
                    block.classList.remove('selected-item');
                }
            });
		}
	</script>
</body>

</html>