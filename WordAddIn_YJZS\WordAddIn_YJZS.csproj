﻿<Project ToolsVersion="17.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <!--
    This section defines project-level properties.

    AssemblyName
      Name of the output assembly.
    Configuration
      Specifies a default value for debug.
    OutputType
      Must be "Library" for VSTO.
    Platform
      Specifies what CPU the output of this project can run on.
    NoStandardLibraries
      Set to "false" for VSTO.
    RootNamespace
      In C#, this specifies the namespace given to new files. In VB, all objects are
      wrapped in this namespace at runtime.
  -->
  <PropertyGroup>
    <ProjectTypeGuids>{BAA0C2D2-18E2-41B9-852F-F413020CAA33};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FBB9D2AA-81D6-4B64-8AAA-0E61C1ADDC96}</ProjectGuid>
    <OutputType>Library</OutputType>
    <NoStandardLibraries>false</NoStandardLibraries>
    <RootNamespace>WordAddIn_YJZS</RootNamespace>
    <AssemblyName>WordAddIn_YJZS</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <DefineConstants>VSTO40;UseOfficeInterop</DefineConstants>
    <ResolveComReferenceSilent>true</ResolveComReferenceSilent>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <PublishUrl>publish\</PublishUrl>
    <InstallUrl />
    <TargetCulture>zh-chs</TargetCulture>
    <ApplicationVersion>*******</ApplicationVersion>
    <AutoIncrementApplicationRevision>true</AutoIncrementApplicationRevision>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>days</UpdateIntervalUnits>
    <IsWebBootstrapper>False</IsWebBootstrapper>
    <ProductName>WordAddIn_YJZS</ProductName>
    <PublisherName />
    <SupportUrl />
    <FriendlyName>WordAddIn_YJZS</FriendlyName>
    <OfficeApplicationDescription />
    <LoadBehavior>3</LoadBehavior>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.VSTORuntime.4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft Visual Studio 2010 Tools for Office Runtime %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <PropertyGroup>
    <!--
      OfficeApplication
        Add-in host application
    -->
    <OfficeApplication>Word</OfficeApplication>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Debug" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);DEBUG;TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>false</RegisterForComInterop>
  </PropertyGroup>
  <!--
    This section defines properties that are set when the "Release" configuration is selected.

    DebugSymbols
      If "true", create symbols (.pdb). If "false", do not create symbols.
    DefineConstants
      Constants defined for the preprocessor.
    EnableUnmanagedDebugging
      If "true", starting the debugger will attach both managed and unmanaged debuggers.
    Optimize
      If "true", optimize the build output. If "false", do not optimize.
    OutputPath
      Output path of project relative to the project file.
    WarningLevel
      Warning level for the compiler.
  -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <DefineConstants>$(DefineConstants);TRACE</DefineConstants>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <!--
    This section specifies references for the project.
  -->
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Core, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.WinForms, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.WebView2.Wpf, Version=1.0.3240.44, Culture=neutral, PublicKeyToken=2a8ab48044d2601e, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.WebView2.1.0.3240.44\lib\net462\Microsoft.Web.WebView2.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Bson, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.Bson.1.0.3\lib\net45\Newtonsoft.Json.Bson.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.1\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.5\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.3\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.6.0.0\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.Json.9.0.5\lib\net462\System.Net.Http.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.1\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.5\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.v4.0.Framework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.Tools.Applications.Runtime, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Common, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Tools.Word, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Office.Tools.Common.v4.0.Utilities, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <Choose>
    <When Condition="$([System.String]::Copy(&quot;;$(DefineConstants);&quot;).ToLower().Contains(';useofficeinterop;')) or $([System.String]::Copy(&quot;,$(DefineConstants),&quot;).ToLower().Contains(',useofficeinterop,'))">
      <ItemGroup>
        <Reference Include="Office, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
        <Reference Include="Microsoft.Office.Interop.Word, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c">
          <Private>False</Private>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </Reference>
      </ItemGroup>
    </When>
    <Otherwise>
      <ItemGroup>
        <COMReference Include="Microsoft.Office.Core">
          <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
          <VersionMajor>2</VersionMajor>
          <VersionMinor>7</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
        <COMReference Include="Microsoft.Office.Interop.Word">
          <Guid>{00020905-0000-0000-C000-000000000046}</Guid>
          <VersionMajor>8</VersionMajor>
          <VersionMinor>6</VersionMinor>
          <Lcid>0</Lcid>
          <WrapperTool>tlbimp</WrapperTool>
          <Isolated>False</Isolated>
          <EmbedInteropTypes>true</EmbedInteropTypes>
        </COMReference>
      </ItemGroup>
    </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="stdole, Version=7.0.3300.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <!--
    This section defines the user source files that are part of the project.
     
    A "Compile" element specifies a source file to compile.
    An "EmbeddedResource" element specifies an .resx file for embedded resources.
    A "None" element specifies a file that is not to be passed to the compiler (for instance, 
    a text file or XML file).
    The "AppDesigner" element specifies the directory where the application properties files
    can be found.
  -->
  <ItemGroup>
    <Compile Include="Util\ExcelService\CheckExcelApiService.cs" />
    <Compile Include="Util\ExcelService\CheckTimeApiService.cs" />
    <Compile Include="Util\ApiService\CheckWordApiService.cs" />
    <Compile Include="Util\ApiService\CheckReportApiService.cs" />
    <Compile Include="Util\ExcelService\OtherTableApiService.cs" />
    <Compile Include="Util\ExcelService\ParaSheetServices.cs" />
    <Compile Include="Util\Common\TokenManager.cs" />
    <Compile Include="Util\ExcelService\ExcelCoverApiService.cs" />
    <Compile Include="Util\ExcelService\ExcelExplainApiService.cs" />
    <Compile Include="Util\ExcelService\ParaSheetService.cs" />
    <Compile Include="Util\ExcelService\Sheet1ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet2ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet3ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet4ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet5ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet6ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet7ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet8ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet3_1ApiService.cs" />
    <Compile Include="Util\ExcelService\Sheet3_2ApiService.cs" />
    <Compile Include="Util\ExcelService\DocumentSyncService.cs" />
    <Compile Include="Form\CircleProgree.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\CircleProgree.Designer.cs">
      <DependentUpon>CircleProgree.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\ParametersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ParametersForm.Designer.cs">
      <DependentUpon>ParametersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\Common\LoginStateManager.cs" />
    <Compile Include="Util\Common\LlmHealthCheckConfig.cs" />
    <Compile Include="Util\Style\ImageAtCursor.cs" />
    <Compile Include="Util\ApiService\ParametersApiService.cs" />
    <Compile Include="Util\ApiService\HttpClientManager.cs" />
    <Compile Include="Util\ApiService\SchemeApiService.cs" />
    <Compile Include="Util\Style\ImageHelper.cs" />
    <Compile Include="Util\Common\CalculateFormData.cs" />
    <Compile Include="Util\ApiService\ConclusionApiService.cs" />
    <Compile Include="Form\ProjectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ProjectForm.Designer.cs">
      <DependentUpon>ProjectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\ApiService\RiskApiPrase.cs" />
    <Compile Include="Util\ApiService\RiskApiService.cs" />
    <Compile Include="Util\ApiService\BenefitApiService.cs" />
    <Compile Include="Util\ApiService\InvestmentApiService.cs" />
    <Compile Include="Util\Common\GlobalParameters.cs" />
    <Compile Include="Util\Common\MenuCheckboxStateManager.cs" />
    <Compile Include="Util\ApiService\DemandApiService.cs" />
    <Compile Include="Util\Common\EnterpriseCheckboxStateManager.cs" />
    <Compile Include="Form\GenerateForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\GenerateForm.Designer.cs">
      <DependentUpon>GenerateForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\ApiService\ApiResponse.cs" />
    <Compile Include="Form\ReplaceMsgBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ReplaceMsgBox.Designer.cs">
      <DependentUpon>ReplaceMsgBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\ApiService\OverviewApiService.cs" />
    <Compile Include="Util\ApiService\CoverApiService.cs" />
    <Compile Include="Util\ApiService\AllApi.cs" />
    <Compile Include="Util\ApiService\FormData.cs" />
    <Compile Include="Util\Common\ConfigurationManager.cs" />
    <Compile Include="Form\ArcProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Form\AboutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\AboutForm.Designer.cs">
      <DependentUpon>AboutForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\AdminForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\AdminForm.Designer.cs">
      <DependentUpon>AdminForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\AgreementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\AgreementForm.Designer.cs">
      <DependentUpon>AgreementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\EnterprisesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\EnterprisesForm.Designer.cs">
      <DependentUpon>EnterprisesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\FeedbackForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\FeedbackForm.Designer.cs">
      <DependentUpon>FeedbackForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\ProgressForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ProgressForm.Designer.cs">
      <DependentUpon>ProgressForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\Common\PictureConverter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Util\ApiService\FileApiService.cs" />
    <Compile Include="Form\ForgetPsdForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ForgetPsdForm.Designer.cs">
      <DependentUpon>ForgetPsdForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\HelpForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\HelpForm.Designer.cs">
      <DependentUpon>HelpForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\LlmStatusForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Util\ApiService\LoginApiService.cs" />
    <Compile Include="Util\ApiService\LlmHealthCheckService.cs" />
    <Compile Include="Util\ApiService\LlmHealthCheckModels.cs" />
    <Compile Include="Form\MenuForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\MenuForm.Designer.cs">
      <DependentUpon>MenuForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\Common\ProgressStream .cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Util\ApiService\WriterApiService.cs" />
    <Compile Include="Util\ApiService\WriterApiParse.cs" />
    <Compile Include="Form\VirtualAssistant.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Form\VirtualAssistant.Designer.cs">
      <DependentUpon>VirtualAssistant.cs</DependentUpon>
    </Compile>
    <Compile Include="Util\Style\WordDocumentHelper.cs" />
    <Compile Include="Util\Style\WordAtCursor.cs" />
    <Compile Include="Util\ApiService\VersionApiService.cs" />
    <Content Include="Style\注意事项.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Style\请尽量不要打开和修改模板文件.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Form\CircleProgree.resx">
      <DependentUpon>CircleProgree.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\AboutForm.resx">
      <DependentUpon>AboutForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\AdminForm.resx">
      <DependentUpon>AdminForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\AgreementForm.resx">
      <DependentUpon>AgreementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ArcProgressBar.resx">
      <DependentUpon>ArcProgressBar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\EnterprisesForm.resx">
      <DependentUpon>EnterprisesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\FeedbackForm.resx">
      <DependentUpon>FeedbackForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ForgetPsdForm.resx">
      <DependentUpon>ForgetPsdForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\HelpForm.resx">
      <DependentUpon>HelpForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\MenuForm.resx">
      <DependentUpon>MenuForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ProgressForm.resx">
      <DependentUpon>ProgressForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\GenerateForm.resx">
      <DependentUpon>GenerateForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ProjectForm.resx">
      <DependentUpon>ProjectForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ParametersForm.resx">
      <DependentUpon>ParametersForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Form\SettingForm.resx">
      <DependentUpon>SettingForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\VirtualAssistant.resx">
      <DependentUpon>VirtualAssistant.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Form\RegisterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\RegisterForm.Designer.cs">
      <DependentUpon>RegisterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Ribbon_YJZS.cs" />
    <Compile Include="Form\SettingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\SettingForm.Designer.cs">
      <DependentUpon>SettingForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ThisAddIn.cs">
      <SubType>Code</SubType>
    </Compile>
    <Content Include="resource\image\login-logo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Style\1、xx项目可行性研究投资估算书（公司总部）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\2、xx项目可行性研究投资估算书（南网超高压公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\3、xx项目可行性研究投资估算书（广东电网公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\4、xx项目可行性研究投资估算书（广西电网公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\5、xx项目可行性研究投资估算书（云南电网公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\6、xx项目可行性研究投资估算书（贵州电网公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\7、xx项目可行性研究投资估算书（海南电网公司）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\8、xx项目可行性研究投资估算书（深圳供电局）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\excel_template20250411.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\template1117.dotx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\xx项目可行性研究投资估算书-总表（含开发、实施工作量）.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Style\单公司_excel_template20250423.xls">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="ThisAddIn.Designer.xml">
      <DependentUpon>ThisAddIn.cs</DependentUpon>
    </None>
    <Compile Include="ThisAddIn.Designer.cs">
      <DependentUpon>ThisAddIn.Designer.xml</DependentUpon>
    </Compile>
    <AppDesigner Include="Properties\" />
    <None Include="Util\Style\template.dotx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Style\template1114.dotx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Style\template1116.dotx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Style\template1117.dotx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Table\4.1投资依据说明.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Table\4.2总投资.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Util\Table\4.3资金计划建议.docx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="WordAddIn_YJZS_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="Microsoft.Office.Core">
      <Guid>{2DF8D04C-5BFA-101B-BDE5-00AA0044DE52}</Guid>
      <VersionMajor>2</VersionMajor>
      <VersionMinor>8</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="Microsoft.Office.Interop.Excel">
      <Guid>{00020813-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>9</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="VBIDE">
      <Guid>{0002E157-0000-0000-C000-000000000046}</Guid>
      <VersionMajor>5</VersionMajor>
      <VersionMinor>3</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>primary</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="resource\image\CEEC.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\image\E_copilot.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\image\header_image.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\image\loading.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\image\login.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\image\NFDW.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="resource\ui\group1_home.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group1_logout.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group1_user.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group2_expand.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group2_generate.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group2_polish.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group2_shorten.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_budget.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_fileitem.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_filemenu.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_generate.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_list.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_preview.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_about.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_copilot.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_feedback.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_help.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_setting.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4_structure.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-document.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-enterprise.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-infoset.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-menu.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-upload.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_information.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_parameters.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3-nowgenerate.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group4-advisor.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_dashboard.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\logged_in_user.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_checked.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="resource\ui\group3_close.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="resource\web\About.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\agreement.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\calculate.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\chooseProject.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Choose_enterprises.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Feedback.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\forgot_password.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Generate.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Help.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Loginplus.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\merge.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\progress.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\register.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Setting.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Set_menu.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="resource\web\Usercenter.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Form\RegisterForm.resx">
      <DependentUpon>RegisterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ReplaceMsgBox.resx">
      <DependentUpon>ReplaceMsgBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ribbon_YJZS.xml" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>WordAddIn_YJZS_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>6FDE2DE9558BFA1E87DBBB541508DE0C6FD14DEE</ManifestCertificateThumbprint>
  </PropertyGroup>
  <!-- Include the build rules for a C# project. -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Include additional build rules for an Office application add-in. -->
  <Import Project="$(VSToolsPath)\OfficeTools\Microsoft.VisualStudio.Tools.Office.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- This section defines VSTO properties that describe the host-changeable project properties. -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{BAA0C2D2-18E2-41B9-852F-F413020CAA33}">
        <ProjectProperties HostName="Word" HostPackage="{29A7B9D7-A7F1-4328-8EF0-6B2D1A56B2C1}" OfficeVersion="15.0" VstxVersion="4.0" ApplicationType="Word" Language="cs" TemplatesPath="" DebugInfoExeName="#Software\Microsoft\Office\16.0\Word\InstallRoot\Path#WINWORD.EXE" DebugInfoCommandLine="/x" AddItemTemplatesGuid="{51063C3A-E220-4D12-8922-BDA915ACD783}" />
        <Host Name="Word" GeneratedCodeNamespace="WordAddIn_YJZS" IconIndex="0">
          <HostItem Name="ThisAddIn" Code="ThisAddIn.cs" CanonicalName="AddIn" CanActivate="false" IconIndex="1" Blueprint="ThisAddIn.Designer.xml" GeneratedCode="ThisAddIn.Designer.cs" />
        </Host>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets" Condition="Exists('..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Web.WebView2.1.0.3240.44\build\Microsoft.Web.WebView2.targets'))" />
    <Error Condition="!Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets'))" />
  </Target>
  <Import Project="..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('..\packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
</Project>