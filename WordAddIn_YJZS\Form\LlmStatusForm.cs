using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public partial class LlmStatusForm : Form
    {
        private LlmHealthCheckResponse _healthResponse;

        public LlmStatusForm(LlmHealthCheckResponse healthResponse)
        {
            _healthResponse = healthResponse;
            InitializeComponent();
            LoadHealthStatus();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form设置
            this.AutoScaleDimensions = new SizeF(9F, 18F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(500, 400);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "LLM服务状态检测";
            this.BackColor = Color.White;

            // 创建主面板
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };
            this.Controls.Add(mainPanel);

            // 标题标签
            var titleLabel = new Label
            {
                Text = "大语言模型服务状态",
                Font = new Font("Microsoft YaHei", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // 状态摘要标签
            var summaryLabel = new Label
            {
                Name = "summaryLabel",
                Font = new Font("Microsoft YaHei", 10F),
                AutoSize = true,
                Location = new Point(0, 40),
                MaximumSize = new Size(460, 0)
            };
            mainPanel.Controls.Add(summaryLabel);

            // 详细信息面板
            var detailPanel = new Panel
            {
                Name = "detailPanel",
                Location = new Point(0, 80),
                Size = new Size(460, 250),
                AutoScroll = true,
                BorderStyle = BorderStyle.FixedSingle
            };
            mainPanel.Controls.Add(detailPanel);

            // 确定按钮
            var okButton = new Button
            {
                Text = "确定",
                Size = new Size(80, 30),
                Location = new Point(380, 340),
                UseVisualStyleBackColor = true,
                DialogResult = DialogResult.OK
            };
            okButton.Click += (s, e) => this.Close();
            mainPanel.Controls.Add(okButton);

            this.ResumeLayout(false);
        }

        private void LoadHealthStatus()
        {
            var summaryLabel = this.Controls.Find("summaryLabel", true)[0] as Label;
            var detailPanel = this.Controls.Find("detailPanel", true)[0] as Panel;

            if (_healthResponse?.Data == null)
            {
                summaryLabel.Text = "❌ 无法获取LLM服务状态信息";
                summaryLabel.ForeColor = Color.Red;
                return;
            }

            var data = _healthResponse.Data;
            
            // 设置摘要信息
            summaryLabel.Text = data.Summary ?? "状态检测完成";
            summaryLabel.ForeColor = data.AvailableApis > 0 ? Color.Green : Color.Red;

            // 添加详细信息
            int yPosition = 10;
            
            if (data.Results != null && data.Results.Any())
            {
                foreach (var result in data.Results)
                {
                    var servicePanel = CreateServicePanel(result, yPosition);
                    detailPanel.Controls.Add(servicePanel);
                    yPosition += 80;
                }
            }
            else
            {
                var noDataLabel = new Label
                {
                    Text = "暂无详细服务信息",
                    Location = new Point(10, 10),
                    AutoSize = true,
                    ForeColor = Color.Gray
                };
                detailPanel.Controls.Add(noDataLabel);
            }
        }

        private Panel CreateServicePanel(ApiResult result, int yPosition)
        {
            var panel = new Panel
            {
                Location = new Point(10, yPosition),
                Size = new Size(420, 70),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = result.Available ? Color.FromArgb(240, 255, 240) : Color.FromArgb(255, 240, 240)
            };

            // 服务名称和状态图标
            var nameLabel = new Label
            {
                Text = $"{(result.Available ? "✅" : "❌")} {result.Name}",
                Font = new Font("Microsoft YaHei", 9F, FontStyle.Bold),
                Location = new Point(10, 8),
                AutoSize = true,
                ForeColor = result.Available ? Color.Green : Color.Red
            };
            panel.Controls.Add(nameLabel);

            // 模型信息
            var modelLabel = new Label
            {
                Text = $"模型: {result.Model ?? "未知"}",
                Font = new Font("Microsoft YaHei", 8F),
                Location = new Point(10, 28),
                AutoSize = true,
                ForeColor = Color.FromArgb(102, 102, 102)
            };
            panel.Controls.Add(modelLabel);

            // 响应时间或错误信息
            var statusLabel = new Label
            {
                Font = new Font("Microsoft YaHei", 8F),
                Location = new Point(10, 45),
                AutoSize = true,
                MaximumSize = new Size(400, 0)
            };

            if (result.Available && result.ResponseTimeMs.HasValue)
            {
                statusLabel.Text = $"响应时间: {result.ResponseTimeMs}ms";
                statusLabel.ForeColor = result.ResponseTimeMs < 2000 ? Color.Green : Color.Orange;
            }
            else if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                statusLabel.Text = $"错误: {result.ErrorMessage}";
                statusLabel.ForeColor = Color.Red;
            }
            else
            {
                statusLabel.Text = "状态未知";
                statusLabel.ForeColor = Color.Gray;
            }

            panel.Controls.Add(statusLabel);

            return panel;
        }
    }
}