﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public class ArcProgressBar : Control
    {
        public int _value = 0;
        private int _maximum = 100;
        public float _currentAngle = 0f;
        public Timer _animationTimer;
        private string _statusText = string.Empty;

        public Color StartColor { get; set; } = Color.DodgerBlue;
        public Color EndColor { get; set; } = Color.MediumSeaGreen;
        public Color TextColor { get; set; } = Color.DarkSlateGray;

        public string StatusText
        {
            get { return _statusText; }
            set
            {
                _statusText = value;
                this.Invalidate();
            }
        }

        // 在ArcProgressBar类中添加这个公共方法
        public void EnableOptimizedRendering()
        {
            // 在类内部调用SetStyle是允许的
            SetStyle(
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.UserPaint,
                true
            );
        }

        public ArcProgressBar()
        {
            SetStyle(ControlStyles.UserPaint |
                    ControlStyles.AllPaintingInWmPaint |
                    ControlStyles.OptimizedDoubleBuffer |
                    ControlStyles.SupportsTransparentBackColor, true);

            // 使用与父窗体相同的透明键颜色
            this.BackColor = Color.FromArgb(1, 2, 3);

            _animationTimer = new Timer { Interval = 16 };
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        public int Value
        {
            get { return _value; }
            set
            {
                _value = Math.Min(Math.Max(value, 0), _maximum);
                _animationTimer.Start();
            }
        }

        public int Maximum
        {
            get { return _maximum; }
            set
            {
                _maximum = value > 0 ? value : 100;
                this.Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            // 完全放弃双缓冲方法，直接在透明背景上绘制，但关闭抗锯齿
            e.Graphics.SmoothingMode = SmoothingMode.None; // 关键：完全关闭抗锯齿
            e.Graphics.PixelOffsetMode = PixelOffsetMode.Default;
            e.Graphics.CompositingQuality = CompositingQuality.Default;

            // 清除背景为透明键颜色
            e.Graphics.Clear(Color.FromArgb(1, 2, 3));

            // 绘制背景圆环 - 无抗锯齿，无锯齿边缘
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(220, 220, 220)))
            {
                e.Graphics.FillEllipse(backgroundBrush, new Rectangle(10, 10, this.Width - 20, this.Height - 20));
            }

            // 计算当前进度角度
            float progressAngle = 360f * _value / _maximum;
            float remainingAngle = 360f - progressAngle;

            // 计算边框绘制区域
            Rectangle borderRect = new Rectangle(15, 15, this.Width - 30, this.Height - 30);

            // 绘制深灰色边框（未完成部分）- 无抗锯齿
            if (remainingAngle > 0)
            {
                using (var borderPen = new Pen(Color.FromArgb(180, 180, 180), 12))
                {
                    // 不设置LineCap，使用默认的平端
                    float startAngle = -90 + progressAngle;
                    e.Graphics.DrawArc(borderPen, borderRect, startAngle, remainingAngle);
                }
            }

            // 绘制蓝色进度边框（已完成部分）- 无抗锯齿
            if (progressAngle > 0)
            {
                using (var gradientBrush = new LinearGradientBrush(this.ClientRectangle, StartColor, EndColor, LinearGradientMode.ForwardDiagonal))
                using (var progressPen = new Pen(gradientBrush, 12))
                {
                    // 不设置LineCap，使用默认的平端
                    e.Graphics.DrawArc(progressPen, borderRect, -90, progressAngle);
                }
            }

            // 绘制百分比文本
            string percentText = $"{(int)(_currentAngle / 360f * 100)}%";
            using (var percentFont = new Font("Microsoft YaHei", 14, FontStyle.Bold))
            using (var statusFont = new Font("Microsoft YaHei", 12, FontStyle.Regular))
            using (var textBrush = new SolidBrush(TextColor))
            {
                // 计算百分比文本位置
                SizeF percentSize = e.Graphics.MeasureString(percentText, percentFont);
                float totalHeight = percentSize.Height;
                if (!string.IsNullOrEmpty(_statusText))
                {
                    // 计算多行文本的总高度
                    string[] lines = _statusText.Split(new char[] { '\n', '\r', '，' }, StringSplitOptions.RemoveEmptyEntries);
                    float lineHeight = statusFont.GetHeight(e.Graphics);
                    totalHeight += (lines.Length * lineHeight) + 5;
                }

                float startY = (this.Height - totalHeight) / 2;

                // 绘制百分比
                PointF percentPosition = new PointF(
                    (this.Width - percentSize.Width) / 2,
                    startY
                );
                e.Graphics.DrawString(percentText, percentFont, textBrush, percentPosition);

                // 绘制状态文本（支持多行）
                if (!string.IsNullOrEmpty(_statusText))
                {
                    // 分割文本为多行
                    string[] lines = _statusText.Split(new char[] { '\n', '\r', '，' }, StringSplitOptions.RemoveEmptyEntries);

                    if (lines.Length == 1)
                    {
                        // 单行文本，使用原来的逻辑
                        SizeF statusSize = e.Graphics.MeasureString(_statusText, statusFont);
                        PointF statusPosition = new PointF(
                            (this.Width - statusSize.Width) / 2,
                            startY + percentSize.Height + 5
                        );
                        e.Graphics.DrawString(_statusText, statusFont, textBrush, statusPosition);
                    }
                    else
                    {
                        // 多行文本
                        float currentY = startY + percentSize.Height + 5;
                        float lineHeight = statusFont.GetHeight(e.Graphics);

                        foreach (string line in lines)
                        {
                            if (!string.IsNullOrWhiteSpace(line))
                            {
                                SizeF lineSize = e.Graphics.MeasureString(line, statusFont);
                                PointF linePosition = new PointF(
                                    (this.Width - lineSize.Width) / 2,
                                    currentY
                                );
                                e.Graphics.DrawString(line, statusFont, textBrush, linePosition);
                                currentY += lineHeight;
                            }
                        }
                    }
                }
            }
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            float targetAngle = 360f * _value / _maximum;
            if (Math.Abs(_currentAngle - targetAngle) < 0.5f)
            {
                _currentAngle = targetAngle;
                _animationTimer.Stop();
            }
            else
            {
                _currentAngle += (targetAngle - _currentAngle) * 0.2f;
            }
            this.Invalidate();
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.ResumeLayout(false);
        }
    }
}