﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace WordAddIn_YJZS
{
    public partial class ProgressForm : Form
    {
        private ArcProgressBar arcProgressBar;

        public ProgressForm(string operationType = "上传")
        {
            InitializeComponent();
            SetupFormStyle();
            SetupArcProgressBar(operationType);
        }

        private void SetupFormStyle()
        {
            // 参考生成文档进度条的窗体样式设置
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                         ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint, true);
            this.DoubleBuffered = true;

            // 使用透明背景（与生成文档进度条一致）
            Color transparentColor = Color.FromArgb(1, 2, 3);
            this.BackColor = transparentColor;
            this.TransparencyKey = transparentColor;

            // 设置窗体样式
            this.FormBorderStyle = FormBorderStyle.None;
            this.Size = new Size(400, 400);  // 与生成文档进度条相同的大小
            this.TopMost = true;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterScreen;
        }


        private void SetupArcProgressBar(string operationType)
        {
            // 移除原有的 ProgressBar 控件（如果存在）
            if (this.Controls.Contains(progressBar))
            {
                this.Controls.Remove(progressBar);
            }

            // 创建新的 ArcProgressBar - 参考生成文档进度条的样式
            arcProgressBar = new ArcProgressBar
            {
                Name = "arcProgressBar",
                StartColor = Color.FromArgb(0, 122, 204),  // 使用与生成文档进度条相同的蓝色
                EndColor = Color.FromArgb(0, 122, 204),    // 使用纯蓝色，不使用渐变
                TextColor = Color.FromArgb(51, 51, 51),    // 使用与生成文档进度条相同的深灰色文本
                StatusText = $"{operationType}中...",
                Size = new Size(320, 320),                 // 使用与生成文档进度条相同的固定大小
                Font = new Font("Microsoft YaHei", 10F),   // 使用与生成文档进度条相同的字体
                Value = 0
            };

            // 设置 ArcProgressBar 的位置（居中）
            arcProgressBar.Location = new Point(
                (this.ClientSize.Width - arcProgressBar.Width) / 2,
                (this.ClientSize.Height - arcProgressBar.Height) / 2
            );

            this.Controls.Add(arcProgressBar);
        }

        public void UpdateProgress(int percent)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int>(UpdateProgress), percent);
                return;
            }

            if (percent >= 0 && percent <= 100)
            {
                arcProgressBar.Value = percent;
            }
        }

        // 如果您的窗体大小可能会改变，可以添加以下方法来调整 ArcProgressBar 的位置
        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            if (arcProgressBar != null)
            {
                // 保持固定大小320x320，只更新位置（居中）
                arcProgressBar.Location = new Point(
                    (this.ClientSize.Width - arcProgressBar.Width) / 2,
                    (this.ClientSize.Height - arcProgressBar.Height) / 2
                );
            }
        }
    }
}
