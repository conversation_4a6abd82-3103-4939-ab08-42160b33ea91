﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public partial class ReplaceMsgBox : Form
    {
        public ReplaceMsgBox(string message, string title)
        {
            InitializeComponent();

            this.Text = title;  // 设置窗口标题
            this.Padding = new Padding(10); // 设置窗口内边距
            this.BackColor = Color.White; // 设置白色背景

            // 设置消息标签样式
            messageLabel = new Label();
            this.Controls.Add(messageLabel); // 添加到窗体中

            messageLabel.Text = message;
            messageLabel.MaximumSize = new Size(800, 0); // 适当减小最大宽度
            messageLabel.AutoSize = true;
            messageLabel.Font = new Font("Microsoft YaHei", 10F, FontStyle.Regular); // 使用微软雅黑字体
            messageLabel.ForeColor = Color.FromArgb(64, 64, 64); // 深灰色文字
            messageLabel.Location = new Point(20, 20);
            messageLabel.TextAlign = ContentAlignment.MiddleLeft;

            // 美化按钮样式
            replaceButton = new Button();
            this.Controls.Add(replaceButton);
            replaceButton.Click += new EventHandler(ReplaceButton_Click);
            replaceButton.Text = "替换";
            replaceButton.Size = new Size(100, 35); // 固定按钮大小
            replaceButton.Font = new Font("Microsoft YaHei", 9F, FontStyle.Regular);
            replaceButton.FlatStyle = FlatStyle.Flat; // 扁平化风格
            replaceButton.BackColor = Color.FromArgb(0, 120, 215); // Windows蓝
            replaceButton.ForeColor = Color.White;
            replaceButton.FlatAppearance.BorderSize = 0; // 移除边框
            replaceButton.Cursor = Cursors.Hand; // 鼠标悬停显示手型
            replaceButton.Location = new Point((this.ClientSize.Width - replaceButton.Width) / 2, messageLabel.Bottom + 20);

            // 取消按钮
            cancelButton = new Button();
            this.Controls.Add(cancelButton);
            cancelButton.Click += new EventHandler(CancelButton_Click);
            cancelButton.Text = "取消";
            cancelButton.Size = new Size(100, 35);
            cancelButton.Font = new Font("Microsoft YaHei", 9F, FontStyle.Regular);
            cancelButton.FlatStyle = FlatStyle.Flat;
            cancelButton.BackColor = Color.FromArgb(220, 53, 69);
            cancelButton.ForeColor = Color.White;
            cancelButton.FlatAppearance.BorderSize = 0;
            cancelButton.Cursor = Cursors.Hand;

            // 动态调整窗体大小
            this.ClientSize = new Size(
                Math.Max(messageLabel.Width + 40, 800), // 最小宽度500
                messageLabel.Height + replaceButton.Height + 60
            );

            // 计算按钮位置
            int buttonSpacing = 20;
            int totalButtonsWidth = replaceButton.Width * 2 + buttonSpacing;
            int startX = (this.ClientSize.Width - totalButtonsWidth) / 2;

            // 设置按钮位置
            replaceButton.Location = new Point(startX, messageLabel.Bottom + 20);
            cancelButton.Location = new Point(startX + replaceButton.Width + buttonSpacing, messageLabel.Bottom + 20);

            // 添加按钮悬停效果
            replaceButton.MouseEnter += (s, e) => {
                replaceButton.BackColor = Color.FromArgb(0, 102, 204); // 稍深的蓝色
            };
            replaceButton.MouseLeave += (s, e) => {
                replaceButton.BackColor = Color.FromArgb(0, 120, 215); // 恢复原色
            };
            // 取消按钮悬停效果
            cancelButton.MouseEnter += (s, e) => {
                cancelButton.BackColor = Color.FromArgb(200, 35, 51);
            };
            cancelButton.MouseLeave += (s, e) => {
                cancelButton.BackColor = Color.FromArgb(220, 53, 69);
            };
        }

        private void ReplaceButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.No;
            this.Close();
        }

        public static DialogResult Show(string message, string title)
        {
            ReplaceMsgBox box = new ReplaceMsgBox(message, title);
            return box.ShowDialog();
        }

        private void ReplaceMsgBox_Load(object sender, EventArgs e)
        {

        }
    }
}
