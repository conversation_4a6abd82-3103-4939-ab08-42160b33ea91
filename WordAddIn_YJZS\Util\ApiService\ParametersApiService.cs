﻿using Newtonsoft.Json;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System;
using WordAddIn_YJZS;
using System.Windows.Forms;

public class ParametersApiService
{
    private readonly HttpClient client;

    public ParametersApiService()
    {
        client = HttpClientManager.Client;
    }

    public async Task<string> ParaConfigAsync(
        string username,
        string project_name,
        string construction_unit,
        string construction_cycle,
        string project_category,
        string project_type,
        string construction_nature,
        string system_security_level,
        double system_user_number,
        string system_deployment_mode)
    {
        
            // 验证参数
            ValidateUserAndProject(username, project_name);

            var url = AllApi.ParaConfigUrl;

            // 创建请求对象并确保所有字符串参数不为null
            var requestBody = new
            {
                username = username ?? "",
                project_name = project_name ?? "",
                construction_unit = construction_unit ?? "",
                construction_cycle = construction_cycle ?? "",
                project_category = project_category ?? "",
                project_type = project_type ?? "",
                construction_nature = construction_nature ?? "",
                system_security_level = system_security_level ?? "",
                system_user_number = system_user_number,
                system_deployment_mode = system_deployment_mode ?? ""
            };

            // 打印请求内容以便调试
            //CircleProgree.ShowMessageBox($"ParaConfig Request URL: {url}");
            //CircleProgree.ShowMessageBox($"ParaConfig Request Body: {JsonConvert.SerializeObject(requestBody, Formatting.Indented)}");

            var jsonContent = new StringContent(
                JsonConvert.SerializeObject(requestBody),
                Encoding.UTF8,
                "application/json"
            );

            // 设置超时
            using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(60)))
            {
                try
                {
                    var response = await client.PostAsync(url, jsonContent, cts.Token);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    // 打印响应内容以便调试
                    //CircleProgree.ShowMessageBox($"ParaConfig Response Status: {response.StatusCode}");
                    //CircleProgree.ShowMessageBox($"ParaConfig Response Content: {responseContent}");

                    if (!response.IsSuccessStatusCode)
                    {
                        throw new HttpRequestException($"HTTP Error: {response.StatusCode} - {response.ReasonPhrase}\nResponse Content: {responseContent}");
                    }

                    try
                    {
                        var apiResponse = JsonConvert.DeserializeObject<ApiResponse<string>>(responseContent);
                        if (apiResponse?.status_code != 200)
                        {
                            throw new HttpRequestException($"API Error: {apiResponse?.status_code} - {apiResponse?.message}");
                        }

                        return apiResponse?.message ?? "Success";
                    }
                    catch (JsonException ex)
                    {
                        throw new Exception($"解析API响应失败: {ex.Message}\nResponse Content: {responseContent}", ex);
                    }
                }
                catch (TaskCanceledException)
                {
                    throw new TimeoutException("请求超时，请检查网络连接或服务器状态");
                }
            }
       
    }

    private void ValidateUserAndProject(string username, string project_name)
    {
        if (string.IsNullOrEmpty(username))
        {
            throw new ArgumentNullException(nameof(username), "用户名不能为空");
        }
        if (string.IsNullOrEmpty(project_name))
        {
            throw new ArgumentNullException(nameof(project_name), "项目名称不能为空");
        }
    }
}