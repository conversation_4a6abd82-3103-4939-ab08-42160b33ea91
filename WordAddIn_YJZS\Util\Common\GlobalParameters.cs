﻿using System;
using System.Collections.Generic;
using WordAddIn_YJZS;

public static class GlobalParameters
{
    public static CalculateFormData CalculateData { get; set; } = new CalculateFormData();

    // 添加用户修改状态跟踪
    private static bool _enterpriseSelectionModifiedByUser = false;
    private static bool _menuSelectionModifiedByUser = false;

    public static bool EnterpriseSelectionModifiedByUser { get { return _enterpriseSelectionModifiedByUser; } }
    public static bool MenuSelectionModifiedByUser { get { return _menuSelectionModifiedByUser; } }

    public static void SetEnterpriseSelectionModified(bool value)
    {
        _enterpriseSelectionModifiedByUser = value;
    }

    public static void SetMenuSelectionModified(bool value)
    {
        _menuSelectionModifiedByUser = value;
    }

    public static void InitializeCalculateData()
    {
        if (CalculateData == null)
        {
            CalculateData = new CalculateFormData();
        }
    }

    // 验证必要参数是否已设置
    public static bool ValidateCalculateData()
    {
        if (CalculateData == null)
        {
            CalculateData = new CalculateFormData();
            return false;
        }

        // 验证必填字段
        bool isValid = true;

        if (string.IsNullOrEmpty(CalculateData.Select3))  // 项目分类
        {
            Console.WriteLine("项目分类为空");
            isValid = false;
        }

        if (string.IsNullOrEmpty(CalculateData.Select12))   // 建设周期
        {
            Console.WriteLine("建设周期为空");
            isValid = false;
        }

        // 如果是信息系统建设与升级改造项目，验证额外字段
        if (CalculateData.Select3 == "信息系统建设与升级改造")
        {
            if (string.IsNullOrEmpty(CalculateData.Select1))   // 项目类型
            {
                Console.WriteLine("项目类型为空");
                isValid = false;
            }

            if (string.IsNullOrEmpty(CalculateData.Select7))   // 建设性质
            {
                Console.WriteLine("建设性质为空");
                isValid = false;
            }

            if (string.IsNullOrEmpty(CalculateData.Select9))   // 系统等保级别
            {
                Console.WriteLine("系统等保级别为空");
                isValid = false;
            }

            if (string.IsNullOrEmpty(CalculateData.Select11))    // 系统部署方式
            {
                Console.WriteLine("系统部署方式为空");
                isValid = false;
            }
        }

        return isValid;
    }

    // 更新菜单选择状态的方法
    public static void UpdateMenuSelection(List<string> chapters, bool isUserModified = false)
    {
        if (chapters == null || chapters.Count == 0)
            return;

        // 如果是用户手动修改，或者之前没有用户修改过
        if (isUserModified || !_menuSelectionModifiedByUser)
        {
            // 创建章节名称到菜单项的映射
            var chapterToMenuMap = new Dictionary<string, string>
            {
                {"项目背景", "1.1项目背景"},
                {"项目依据", "1.2项目依据"},
                {"项目目标", "1.3项目目标"},
                {"项目范围", "1.4项目范围"},
                {"现状分析", "2.1现状分析"},
                {"需求分析", "2.2需求分析"},
                {"必要性结论", "2.3必要性结论"},
                {"业务架构", "3.1业务架构"},
                {"应用架构", "3.2应用架构"},
                {"数据架构", "3.3数据架构"},
                {"技术架构", "3.4技术架构"},
                {"系统部署方式及软硬件资源需求", "3.5系统部署方式及软硬件资源需求"},
                {"安全技术方案", "3.6安全技术方案"},
                {"项目实施需求", "3.7项目实施需求"},
                {"投资依据说明", "4.1投资依据说明"},
                {"总投资", "4.2总投资"},
                {"资金计划建议", "4.3资金计划建议"},
                {"管理效益分析", "5.1管理效益分析"},
                {"经济效益分析", "5.2经济效益分析"},
                {"社会效益分析", "5.3社会效益分析"},
                {"项目风险分析", "6.1项目风险分析"},
                {"项目可研结论", "7.1项目可研结论"}
            };

            // 先清除现有的菜单选择状态
            MenuCheckboxStateManager.Clear();

            // 记录已添加的标签，防止重复添加
            HashSet<string> addedLabels = new HashSet<string>();

            // 为每个章节添加选择状态
            foreach (var chapter in chapters)
            {
                if (chapterToMenuMap.TryGetValue(chapter, out string menuItem) && !addedLabels.Contains(menuItem))
                {
                    // 使用菜单项作为ID和标签
                    MenuCheckboxStateManager.UpdateCheckboxState(menuItem, true, menuItem);
                    addedLabels.Add(menuItem);

                    // 同步到其他窗体
                    foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
                    {
                        // 更新MenuForm的勾选框状态
                        if (form is MenuForm menuForm && !form.IsDisposed)
                        {
                            form.Invoke(new Action(() => {
                                menuForm.UpdateCheckboxState(menuItem, true);
                            }));
                        }
                        // 更新GenerateForm的勾选框状态
                        else if (form is GenerateForm generateForm && !form.IsDisposed)
                        {
                            form.Invoke(new Action(() => {
                                // 获取ID和菜单名称的映射
                                string checkboxId = GetMenuCheckboxId(menuItem);
                                if (!string.IsNullOrEmpty(checkboxId))
                                {
                                    generateForm.UpdateMenuCheckboxState(checkboxId, true);
                                }
                                else
                                {
                                    // 如果找不到映射的ID，尝试直接使用名称作为ID
                                    generateForm.UpdateMenuCheckboxState(menuItem, true);
                                }
                            }));
                        }
                    }
                }
            }

            // 如果是用户手动修改，设置标志
            if (isUserModified)
            {
                _menuSelectionModifiedByUser = true;
            }

            // 完成后手动触发同步
            foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
            {
                if (form is GenerateForm generateForm && !form.IsDisposed)
                {
                    form.Invoke(new Action(() => {
                        // 调用新方法进行同步
                        generateForm.SyncMenuCheckboxes();
                    }));
                }
            }
        }
    }

    // 新增辅助方法，根据菜单项名称查找对应的checkbox ID
    private static string GetMenuCheckboxId(string menuName)
    {
        var menuLabels = new Dictionary<string, string>
        {
            { "1.1项目背景", "checkbox9" },
            { "1.2项目依据", "checkbox10" },
            { "1.3项目目标", "checkbox11" },
            { "1.4项目范围", "checkbox12" },
            { "2.1现状分析", "checkbox13" },
            { "2.2需求分析", "checkbox14" },
            { "2.3必要性结论", "checkbox15" },
            { "3.1业务架构", "checkbox16" },
            { "3.2应用架构", "checkbox17" },
            { "3.3数据架构", "checkbox18" },
            { "3.4技术架构", "checkbox19" },
            { "3.5系统部署方式及软硬件资源需求", "checkbox20" },
            { "3.6安全技术方案", "checkbox21" },
            { "3.7项目实施需求", "checkbox22" },
            { "4.1投资依据说明", "checkbox23" },
            { "4.2总投资", "checkbox24" },
            { "4.3资金计划建议", "checkbox25" },
            { "5.1管理效益分析", "checkbox26" },
            { "5.2经济效益分析", "checkbox27" },
            { "5.3社会效益分析", "checkbox28" },
            { "6.1项目风险分析", "checkbox29" },
            { "7.1项目可研结论", "checkbox30" }
        };
            
        return menuLabels.ContainsKey(menuName) ? menuLabels[menuName] : string.Empty;
    }

    // 更新企业选择状态的方法，添加isUserModified参数
    public static void UpdateEnterpriseSelection(List<string> enterprises, bool isUserModified = false)
    {
        if (enterprises == null || enterprises.Count == 0)
            return;

        // 如果是用户手动修改，或者之前没有用户修改过
        if (isUserModified || !_enterpriseSelectionModifiedByUser)
        {
            // 先清除现有的企业选择状态
            EnterpriseCheckboxStateManager.Clear();

            // 记录已添加的标签，防止重复添加
            HashSet<string> addedLabels = new HashSet<string>();

            // 为每个企业添加选择状态
            foreach (var enterprise in enterprises)
            {
                if (!string.IsNullOrEmpty(enterprise) && !addedLabels.Contains(enterprise))
                {
                    // 使用企业名称作为ID和标签
                    EnterpriseCheckboxStateManager.UpdateCheckboxState(enterprise, true, enterprise);
                    addedLabels.Add(enterprise);

                    // 如果当前有打开的 EnterprisesForm，立即更新其勾选框状态
                    foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
                    {
                        if (form is EnterprisesForm enterprisesForm && !form.IsDisposed)
                        {
                            form.Invoke(new Action(() => {
                                enterprisesForm.UpdateCheckboxState(enterprise, true);
                            }));
                        }
                        // 同时更新GenerateForm的勾选框状态
                        else if (form is GenerateForm generateForm && !form.IsDisposed)
                        {
                            form.Invoke(new Action(() => {
                                // 获取ID和企业名称的映射
                                string checkboxId = GetEnterpriseCheckboxId(enterprise);
                                if (!string.IsNullOrEmpty(checkboxId))
                                {
                                    generateForm.UpdateEnterpriseCheckboxState(checkboxId, true);
                                }
                                else
                                {
                                    // 如果找不到映射的ID，尝试直接使用名称作为ID
                                    generateForm.UpdateEnterpriseCheckboxState(enterprise, true);
                                }
                            }));
                        }
                    }
                }
            }

            // 如果是用户手动修改，设置标志
            if (isUserModified)
            {
                _enterpriseSelectionModifiedByUser = true;
            }

            // 完成后手动触发同步
            foreach (System.Windows.Forms.Form form in System.Windows.Forms.Application.OpenForms)
            {
                if (form is GenerateForm generateForm && !form.IsDisposed)
                {
                    form.Invoke(new Action(() => {
                        // 调用新方法进行同步
                        generateForm.SyncEnterpriseCheckboxes();
                    }));
                }
            }
        }
    }

    // 新增辅助方法，根据企业名称查找对应的checkbox ID
    private static string GetEnterpriseCheckboxId(string enterpriseName)
    {
        var enterpriseLabels = new Dictionary<string, string>
        {
            { "南方电网", "checkbox1" },
            { "广东电网公司", "checkbox2" },
            { "广西电网公司", "checkbox3" },
            { "云南电网公司", "checkbox4" },
            { "贵州电网公司", "checkbox5" },
            { "海南电网公司", "checkbox6" },
            { "深圳供电局", "checkbox7" },
            { "南网超高压公司", "checkbox8" },
            { "南网储能公司", "checkbox50" },
            { "南网产业投资集团", "checkbox51" },
            { "鼎元资产公司", "checkbox52" },
            { "南网能源公司", "checkbox53" },
            { "南网国际（香港）公司", "checkbox54" },
            { "南网澜湄国际公司", "checkbox55" },
            { "南网资本控股公司", "checkbox56" },
            { "南网财务公司", "checkbox57" },
            { "鼎和保险公司", "checkbox58" },
            { "南网党校", "checkbox59" },
            { "南网北京分公司", "checkbox60" },
            { "南网共享公司", "checkbox61" },
            { "南网生态运营公司", "checkbox62" },
            { "南网数字集团", "checkbox63" },
            { "南网供应链集团", "checkbox64" },
            { "南网能源院", "checkbox65" },
            { "南网科研院", "checkbox66" },
            { "广州电力交易中心", "checkbox67" },
            { "南网传媒公司", "checkbox68" },
            { "北京研究院", "checkbox69" }
        };
            
        return enterpriseLabels.ContainsKey(enterpriseName) ? enterpriseLabels[enterpriseName] : string.Empty;
    }
}

