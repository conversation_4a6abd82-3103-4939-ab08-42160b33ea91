<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择生成章节</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #ffffff;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .dashboard {
            display: flex;
            width: 100%;
            height: 100vh;
            margin: 0;
            background-color: #ffffff;
            border-radius: 0;
            box-shadow: none;
            overflow: hidden;
        }

        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow: visible;
            max-height: none;
            width: 100%;
        }

        .card {
			background-color: #fff;
			border-radius: 0;
			box-shadow: none;
			padding: 12px;
			margin-bottom: 0;
			position: relative;
			width: 100%;
			margin: 0;
			min-height: calc(100vh - 40px);
			box-sizing: border-box;
		}

        .card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #1a237e;
            font-size: 16px;
        }

        .button-group {
            display: flex;
            gap: 8px;
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .button {
            padding: 6px 12px;
            background: linear-gradient(45deg, #4a90e2, #63b8ff);
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .button:hover {
            background: linear-gradient(45deg, #3a80d2, #53a8ff);
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .dashboard-item {
			background-color: #ffffff;
			border-radius: 0;
			box-shadow: none;
			border: 1px solid #e0e0e0;
			padding: 12px;
			transition: border-color 0.3s ease;
			width: 300px;  /* 设置固定宽度 */
		}

        .dashboard-item:hover {
            border-color: #4a90e2;
        }

        .dashboard-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #4a90e2;
            font-size: 16px;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
            width: fit-content;
        }

        .checkbox-item {
            position: relative;
            min-height: 32px;
            display: inline-block;
            width: auto;
        }

        .checkbox-item input[type="checkbox"] {
            display: none;
        }

        .checkbox-item label {
            display: flex;
            align-items: center;
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            background: #fff;
            transition: all 0.3s ease;
            position: relative;
            padding: 0 8px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 300px;
            max-width: fit-content;
        }

        .checkbox-item input[type="checkbox"]:checked + label {
            border-color: #4a90e2;
            color: #4a90e2;
        }

        .checkbox-item input[type="checkbox"]:checked + label::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 16px 16px;
            border-color: transparent transparent #4a90e2 transparent;
        }
		

    </style>
</head>

<body>
    <div class="dashboard">
        <main class="main-content">
            <section id="section2" class="card">
                <h3>二、选择生成章节</h3>
                <div class="button-group">
                    <button class="button select-all-menu">全选</button>
                    <button class="button invert-selection-menu">反选</button>
                </div>
                <div class="dashboard-container">
                    <div class="dashboard-item">
                        <h3>1. 概述</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox9" >
                                <label for="checkbox9">1.1项目背景</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox10" >
                                <label for="checkbox10">1.2项目依据</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox11" >
                                <label for="checkbox11">1.3项目目标</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox12" >
                                <label for="checkbox12">1.4项目范围</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>2.项目现状及必要性分析</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox13" >
                                <label for="checkbox13">2.1现状分析</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox14" >
                                <label for="checkbox14">2.2需求分析</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox15" >
                                <label for="checkbox15">2.3必要性结论</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>3.项目方案</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox16" >
                                <label for="checkbox16">3.1业务架构</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox17" >
                                <label for="checkbox17">3.2应用架构</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox18" >
                                <label for="checkbox18">3.3数据架构</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox19" >
                                <label for="checkbox19">3.4技术架构</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox20" >
                                <label for="checkbox20">3.5系统部署方式及软硬件资源需求</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox21" >
                                <label for="checkbox21">3.6安全技术方案</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox22" >
                                <label for="checkbox22">3.7项目实施需求</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>4.项目投资估算</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox23" >
                                <label for="checkbox23">4.1投资依据说明</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox24" >
                                <label for="checkbox24">4.2总投资</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox25" >
                                <label for="checkbox25">4.3资金计划建议</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>5.项目效益分析</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox26" >
                                <label for="checkbox26">5.1管理效益分析</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox27" >
                                <label for="checkbox27">5.2经济效益分析</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox28" >
                                <label for="checkbox28">5.3社会效益分析</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>6.项目风险分析</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox29" >
                                <label for="checkbox29">6.1项目风险分析</label>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-item">
                        <h3>7.项目可研结论</h3>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="checkbox30">
                                <label for="checkbox30">7.1项目可研结论</label>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

        </main>
    </div>

    <script>
        // 添加关闭表单的函数
        function closeForm() {
            try {
                // 使用最简单的消息格式
                window.chrome.webview.postMessage('{"type":"closeForm"}');
            } catch (error) {
                console.error("发送消息时出错:", error);
            }
        }

        function handleCheckboxChange(checkbox) {
            const checkboxItem = checkbox.closest('.checkbox-item');
            sendCheckboxState(checkbox.id, checkbox.checked);
        }

        function sendCheckboxState(id, isChecked) {
            const label = document.querySelector(`label[for="${id}"]`).textContent;
            window.chrome.webview.postMessage({ id: id, checked: isChecked, label: label });
        }

        function selectAll() {
            document.querySelectorAll('.dashboard-container input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = true;
                handleCheckboxChange(checkbox);
            });
        }

        function invertSelection() {
            document.querySelectorAll('.dashboard-container input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = !checkbox.checked;
                handleCheckboxChange(checkbox);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 首先查询C#端提供的状态，这将通过WebView_NavigationCompleted回调执行
            requestInitialCheckboxStates();
            
            // 设置复选框变化监听器
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    handleCheckboxChange(this);
                });
            });

            document.querySelectorAll('.checkbox-item label').forEach(label => {
                label.addEventListener('click', function(e) {
                    e.preventDefault();
                    const checkbox = document.getElementById(this.getAttribute('for'));
                    checkbox.checked = !checkbox.checked;
                    handleCheckboxChange(checkbox);
                });
            });

            document.querySelectorAll('.checkbox-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    if (e.target === this) {
                        const checkbox = this.querySelector('input[type="checkbox"]');
                        checkbox.checked = !checkbox.checked;
                        handleCheckboxChange(checkbox);
                    }
                });
            });

            document.querySelector('.select-all-menu').addEventListener('click', selectAll);
            document.querySelector('.invert-selection-menu').addEventListener('click', invertSelection);
        });

        // 请求初始状态
        function requestInitialCheckboxStates() {
            try {
                // 这会触发WebView_NavigationCompleted回调
                window.chrome.webview.postMessage({ type: "getInitialState" });
            } catch (error) {
                // 静默处理错误
            }
        }

        // 这个函数会被C#端调用来设置复选框状态
        function restoreCheckboxStates(states) {
            for (let id in states) {
                const checkbox = document.getElementById(id);
                if (checkbox) {
                    checkbox.checked = states[id];
                }
            }
        }
    </script>
</body>

</html>