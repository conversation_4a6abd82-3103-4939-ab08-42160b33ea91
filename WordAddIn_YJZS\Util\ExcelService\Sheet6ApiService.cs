﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Net.Http.Headers;
using System.Linq;
using WordAddIn_YJZS;

namespace WordAddIn_YJZS
{
    public class Sheet6ApiService
    {
        private readonly HttpClient client;

        public Sheet6ApiService()
        {
            client = HttpClientManager.Client;
        }

        // 表6数据模型
        public class Table6Request
        {
            [JsonProperty("username")]
            public string Username { get; set; }

            [JsonProperty("project_name")]
            public string ProjectName { get; set; }

            [JsonProperty("start_row")]
            public int StartRow { get; set; }

            [JsonProperty("start_col")]
            public int StartCol { get; set; }
        }

        public class Table6Response
        {
            [JsonProperty("content_list")]
            public List<List<object>> ContentList { get; set; }

            //[JsonProperty("merged_list")]
            //public List<List<int>> MergedList { get; set; }
        }

        public class MarkInfo
        {
            [JsonProperty("row")]
            public int Row { get; set; }

            [JsonProperty("col")]
            public int Col { get; set; }

            [JsonProperty("content")]
            public string Content { get; set; }
        }

        // 生成表6
        public async Task<Table6Response> GenerateTable6Async(string username, string projectName, int startRow = 1, int startCol = 1)
        {
            try
            {
                var request = new Table6Request
                {
                    Username = username,
                    ProjectName = projectName,
                    StartRow = startRow,
                    StartCol = startCol
                };

                // 添加请求信息调试
                string requestInfo = $"Request Details:\nUsername: {username}\nProject: {projectName}\nStartRow: {startRow}\nStartCol: {startCol}";
                //MessageBox.Show(requestInfo, "Request Debug Info");

                var response = await PostJsonAsync<Table6Response>(
                    AllApi.Sheet6Url,
                    request,
                    "data"
                );

                // 添加响应信息调试
                string responseInfo = "Response Details:\n";
                if (response != null)
                {
                    responseInfo += $"ContentList Count: {response.ContentList?.Count ?? 0}\n";

                    if (response.ContentList != null && response.ContentList.Count > 0)
                    {
                        responseInfo += $"First Row Columns: {response.ContentList[0]?.Count ?? 0}\n";
                        responseInfo += "First Row Content:\n";
                        foreach (var item in response.ContentList[0])
                        {
                            responseInfo += $"{item?.ToString() ?? "null"}, ";
                        }
                    }
                }
                else
                {
                    responseInfo += "Response is null";
                }
                //MessageBox.Show(responseInfo, "Response Debug Info");

                return response;
            }
            catch (Exception)
            {
                //MessageBox.Show($"生成表6失败: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        public List<List<string>> ProcessContentList(List<List<object>> contentList)
        {
            if (contentList == null) return new List<List<string>>();

            var result = new List<List<string>>();
            foreach (var row in contentList)
            {
                if (row == null) continue;

                var processedRow = new List<string>();
                for (int i = 0; i < row.Count; i++)
                {
                    var item = row[i];
                    if (item == null)
                    {
                        processedRow.Add("");
                        continue;
                    }

                    string value = item.ToString().Trim();

                    // 对于数值类型的特殊处理
                    if (double.TryParse(value, out double numValue))
                    {
                        // 保持原始格式
                        processedRow.Add(value);
                    }
                    else
                    {
                        // 文本内容
                        processedRow.Add(value);
                    }
                }
                result.Add(processedRow);
            }

            // 调试输出
            for (int i = 0; i < result.Count; i++)
            {
                System.Diagnostics.Debug.WriteLine($"Row {i + 1}:");
                for (int j = 0; j < result[i].Count; j++)
                {
                    System.Diagnostics.Debug.WriteLine($"  Column {j + 1}: {result[i][j]}");
                }
            }

            return result;
        }

        //public List<List<int>> ProcessMergedList(List<List<int>> mergedList)
        //{
        //    if (mergedList == null) return new List<List<int>>();

        //    // 直接返回合并单元格信息，不需要额外处理
        //    return mergedList;
        //}

        // 验证响应数据
        public bool ValidateResponse(Table6Response response)
        {
            if (response == null)
                return false;

            if (response.ContentList == null || response.ContentList.Count == 0)
                return false;

            return true;
        }

        private void ShowMessage(string message)
        {
            MessageBox.Show(message);
        }

        // POST请求处理
        private async Task<T> PostJsonAsync<T>(string url, object data, string dataField)
        {
            using (var jsonContent = new StringContent(JsonConvert.SerializeObject(data)))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    return await HandleResponseAsync<T>(response, dataField);
                }
                catch (TaskCanceledException)
                {
                    throw new Exception("请求超时");
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception("网络错误：" + ex.Message);
                }
            }
        }

        // 处理API响应
        private static async Task<T> HandleResponseAsync<T>(HttpResponseMessage response, string dataField)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            //MessageBox.Show($"Raw Response:\n{responseString}", "Raw Response Debug");

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    // 首先解析为 JObject，这样我们可以更灵活地处理数据
                    JObject jsonResponse = JObject.Parse(responseString);

                    // 检查状态码
                    int statusCode = jsonResponse["status_code"].Value<int>();
                    string message = jsonResponse["message"].Value<string>();

                    if (statusCode == 200)
                    {
                        // 获取数据部分
                        JToken dataToken = jsonResponse["data"];
                        if (dataToken == null)
                        {
                            throw new Exception("响应数据为空");
                        }

                        // 直接将数据部分转换为目标类型
                        T result = dataToken.ToObject<T>();

                        // 调试输出
                        if (typeof(T) == typeof(Table6Response))
                        {
                            var tableResponse = result as Table6Response;
                            string debugInfo = "解析后的数据:\n";
                            if (tableResponse?.ContentList != null)
                            {
                                debugInfo += $"ContentList Count: {tableResponse.ContentList.Count}\n";
                                if (tableResponse.ContentList.Count > 0)
                                {
                                    debugInfo += $"First Row Count: {tableResponse.ContentList[0].Count}\n";
                                    debugInfo += "First Row Content: ";
                                    foreach (var item in tableResponse.ContentList[0].Take(5))
                                    {
                                        debugInfo += $"{item?.ToString() ?? "null"}, ";
                                    }
                                }
                            }
                            //MessageBox.Show(debugInfo, "Data Parsing Debug");
                        }

                        return result;
                    }
                    else
                    {
                        throw new Exception($"API错误: {message}");
                    }
                }
                catch (JsonException ex)
                {
                    //MessageBox.Show($"JSON解析错误: {ex.Message}\n\nResponse String: {responseString}", "JSON Error Debug");
                    throw new Exception($"JSON解析错误: {ex.Message}");
                }
            }
            else
            {
                //MessageBox.Show($"HTTP错误:\nStatus: {response.StatusCode}\nContent: {responseString}", "HTTP Error Debug");
                throw new Exception($"HTTP错误 {response.StatusCode}: {responseString}");
            }
        }

        // API响应模型
        private class ApiResponse<T>
        {
            [JsonProperty("status_code")]
            public int status_code { get; set; }

            [JsonProperty("message")]
            public string message { get; set; }

            [JsonProperty("data")]
            public T data { get; set; }
        }

        // 验证错误响应模型
        private class ValidationErrorResponse
        {
            [JsonProperty("detail")]
            public List<ValidationError> Detail { get; set; }
        }

        private class ValidationError
        {
            [JsonProperty("loc")]
            public List<string> Loc { get; set; }

            [JsonProperty("msg")]
            public string Msg { get; set; }

            [JsonProperty("type")]
            public string Type { get; set; }
        }
    }
}
