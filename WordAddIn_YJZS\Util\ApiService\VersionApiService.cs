﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System;
using WordAddIn_YJZS;
using System.Linq;
using System.Diagnostics;
using System.Net;

// 版本检查结果类
public class VersionCheckResult
{
    public string CurrentVersion { get; set; }
    public string LatestVersion { get; set; }
    public bool UpdateAvailable { get; set; }
    public string Changelog { get; set; }
}

public class VersionApiService
{
    private readonly HttpClient client;
    public ApiResponse<JObject> Result { get; private set; }

    public VersionApiService()
    {
        // 获取预先配置的HttpClient实例
        client = HttpClientManager.Client;
        
        // 确保HttpClient配置了超时和适当的SSL处理
        if (client.Timeout < TimeSpan.FromSeconds(30))
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        }
        
        // 配置SSL
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
    }

    // 检查更新，返回完整版本信息
    public async Task<VersionCheckResult> CheckVersionAsync(string current_version)
    {
        if (string.IsNullOrEmpty(current_version))
        {
            throw new ArgumentNullException(nameof(current_version), "版本不能为空！");
        }

        Debug.WriteLine($"检查版本更新: 当前版本 {current_version}");
        Debug.WriteLine($"使用URL: {AllApi.CheckVersionUrl}");

        var requestBody = new { current_version };
        
        try
        {
            using (var jsonContent = new StringContent(JsonConvert.SerializeObject(requestBody)))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                
                Debug.WriteLine("发送检查版本请求...");
                var response = await client.PostAsync(AllApi.CheckVersionUrl, jsonContent);
                var responseString = await response.Content.ReadAsStringAsync();
                
                Debug.WriteLine($"收到响应: {responseString}");
                
                if (string.IsNullOrEmpty(responseString))
                {
                    throw new Exception("服务器返回空响应");
                }
                
                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        // 尝试直接解析版本检查响应数据
                        var versionData = JsonConvert.DeserializeObject<JObject>(responseString);

                        if (versionData != null)
                        {
                            // 根据API实际返回的字段名称映射数据
                            bool updateAvailable = versionData.Value<bool?>("update_available") ?? false;
                            string currentVersionFromApi = versionData.Value<string>("current_version") ?? current_version;
                            string latestVersion = versionData.Value<string>("latest_version") ?? current_version;
                            string changelog = versionData.Value<string>("changelog") ?? "";

                            Debug.WriteLine($"版本检查成功: 当前版本={currentVersionFromApi}, 最新版本={latestVersion}, 需要更新={updateAvailable}");

                            return new VersionCheckResult
                            {
                                UpdateAvailable = updateAvailable,
                                CurrentVersion = currentVersionFromApi,
                                LatestVersion = latestVersion,
                                Changelog = changelog
                            };
                        }
                    }
                    catch (JsonException ex)
                    {
                        Debug.WriteLine($"解析版本检查响应失败: {ex.Message}");
                        Debug.WriteLine($"响应内容: {responseString}");
                    }

                    // 如果直接解析失败，尝试解析为标准API响应格式
                    try
                    {
                        Result = JsonConvert.DeserializeObject<ApiResponse<JObject>>(responseString);

                        if (Result != null && Result.data != null)
                        {
                            // 根据API实际返回的字段名称映射数据
                            bool updateAvailable = Result.data.Value<bool?>("update_available") ?? false;
                            string currentVersionFromApi = Result.data.Value<string>("current_version") ?? current_version;
                            string latestVersion = Result.data.Value<string>("latest_version") ?? current_version;
                            string changelog = Result.data.Value<string>("changelog") ?? "";

                            Debug.WriteLine($"版本检查成功(标准格式): 当前版本={currentVersionFromApi}, 最新版本={latestVersion}, 需要更新={updateAvailable}");

                            return new VersionCheckResult
                            {
                                UpdateAvailable = updateAvailable,
                                CurrentVersion = currentVersionFromApi,
                                LatestVersion = latestVersion,
                                Changelog = changelog
                            };
                        }
                    }
                    catch (JsonException ex)
                    {
                        Debug.WriteLine($"解析标准API响应格式失败: {ex.Message}");
                    }

                    // 如果所有解析都失败，抛出异常
                    throw new Exception($"无法解析版本检查响应: {responseString}");
                }
                else if ((int)response.StatusCode == 422)
                {
                    try
                    {
                        var validationErrors = JsonConvert.DeserializeObject<ValidationErrorResponse>(responseString);
                        string errorMsg = string.Join(", ", validationErrors?.Detail?.Select(e => e.Msg) ?? new[] { "未知验证错误" });
                        Debug.WriteLine($"验证错误: {errorMsg}");
                        throw new Exception($"验证错误: {errorMsg}");
                    }
                    catch (Exception parseEx)
                    {
                        Debug.WriteLine($"解析验证错误失败: {parseEx.Message}");
                        throw new Exception($"验证错误: {responseString}");
                    }
                }
                else
                {
                    Debug.WriteLine($"请求失败，状态码 {response.StatusCode}: {responseString}");
                    throw new Exception($"请求失败，状态码 {response.StatusCode}: {responseString}");
                }
            }
        }
        catch (TaskCanceledException ex)
        {
            Debug.WriteLine($"请求超时: {ex.Message}");
            throw new Exception("检查版本时请求超时，请检查网络连接后重试");
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine($"网络请求异常: {ex.Message}");
            throw new Exception("网络连接异常，请检查网络设置后重试");
        }
        catch (Exception ex) when (!(ex is TaskCanceledException || ex is HttpRequestException))
        {
            Debug.WriteLine($"检查更新时出错: {ex.Message}");
            throw new Exception("检查更新时出错: " + ex.Message);
        }
    }
}
