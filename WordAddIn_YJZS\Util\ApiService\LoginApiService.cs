﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace WordAddIn_YJZS
{
    public class LoginApiService
    {
        private readonly HttpClient client;

        public LoginApiService()
        {
            client = HttpClientManager.Client;
        }

        private void ShowMessage(string message, string caption = "提示",
        MessageBoxButtons buttons = MessageBoxButtons.OK,
        MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        // 注册方法
        public async Task<string> RegisterAsync(string username, string password, string email, string phoneNumber, string department, string fullName)
        {
            var registrationData = new
            {
                username,
                password,
                email,
                phone_number = phoneNumber,
                department,
                full_name = fullName
            };

            return await PostJsonAsync<string>(AllApi.RegisterUrl, registrationData, "message");
        }

        // 登录方法
        public async Task<(string username, string fullName, string access_token, string errorMessage)> LoginAsync(string username, string password)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    return (null, null, null, "用户名和密码不能为空");
                }

                // 登录失败，返回错误消息
                string errorMessage = "账号或密码错误，请重新输入。若忘记密码，请点击“找回密码”或联系管理员重置。";

                var loginData = new { username, password };
                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(loginData),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(AllApi.LoginUrl, jsonContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var jsonResponse = JsonConvert.DeserializeAnonymousType(responseContent, new
                    {
                        status_code = 0,
                        data = new
                        {
                            username = "",
                            full_name = "",
                            access_token = ""
                        }
                    });

                    if (jsonResponse?.status_code == 200)
                    {
                        return (
                            jsonResponse.data.username ?? username,
                            jsonResponse.data.full_name,
                            jsonResponse.data.access_token,
                            null
                        );
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return (null, null, null, errorMessage);
                }

                return (null, null, null, errorMessage);
            }
            catch (JsonException ex)
            {
                throw new Exception($"解析服务器响应时出错：{ex.Message}");
            }
            catch (HttpRequestException)
            {
                string httpErrorMessage = "登录失败：当前网络不稳定或服务器繁忙，请检查网络后重试。若问题持续，请联系管理员处理。";
                return ( null, null, null, httpErrorMessage);
            }
            catch (Exception ex)
            {
                throw new Exception($"登录失败：{ex.Message}");
            }
        }

        // 验证令牌是否有效
        public async Task<(bool isValid, string username, string fullName)> VerifyTokenAsync(string token)
        {
            try
            {
                var requestData = new
                {
                    token
                };

                var content = new StringContent(
                    JsonConvert.SerializeObject(requestData),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(AllApi.LoginStateUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<dynamic>(responseContent);

                // 检查状态码，500 表示令牌过期或无效
                if (result.status_code == 200)
                {
                    // 令牌有效
                    string username = result.data.username;
                    string fullName = result.data.full_name;
                    return (true, username, fullName);
                }
                else
                {
                    // 令牌无效或过期
                    return (false, null, null);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"验证令牌时出错: {ex.Message}");
                return (false, null, null);
            }
        }

        // 自动验证保存的令牌
        public async Task<(bool isLoggedIn, string username, string fullName)> AutoLoginAsync()
        {
            try
            {
                // 获取保存的令牌
                var tokenData = TokenManager.GetToken();
                if (tokenData == null)
                {
                    return (false, null, null);
                }

                // 验证令牌
                var (isValid, username, fullName) = await VerifyTokenAsync(tokenData.AccessToken);
                if (isValid)
                {
                    // 令牌有效，设置HTTP客户端的默认请求头
                    client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", tokenData.AccessToken);
                    return (true, username, fullName);
                }
                else
                {
                    // 令牌无效，清除保存的令牌
                    TokenManager.ClearToken();
                    return (false, null, null);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动登录时出错: {ex.Message}");
                return (false, null, null);
            }
        }

        // 重置密码方法
        public async Task<string> ResetPasswordAsync(string username, string oldPassword, string newPassword)
        {
            var resetPasswordData = new
            {
                username,
                old_password = oldPassword,
                new_password = newPassword
            };

            return await PostJsonAsync<string>(AllApi.ResetPsdUrl, resetPasswordData, "message");
        }

        // POST请求
        private async Task<T> PostJsonAsync<T>(string url, object data, string dataField)
        {
            using (var jsonContent = new StringContent(JsonConvert.SerializeObject(data)))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    return await HandleResponseAsync<T>(response, dataField);
                }
                catch (TaskCanceledException)
                {
                    throw new Exception("请求超时");
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception("网络错误：" + ex.Message);
                }
            }
        }

        // 处理响应
        private static async Task<T> HandleResponseAsync<T>(HttpResponseMessage response, string dataField)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var result = JsonConvert.DeserializeObject<ApiResponse<JObject>>(responseString);
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)result.message;
                }
                else if (result.data != null && result.data.TryGetValue(dataField, out JToken value))
                {
                    return value.ToObject<T>();
                }
                throw new Exception($"响应中没有找到 {dataField} 字段");
            }
            else if ((int)response.StatusCode == 409)
            {
                throw new Exception("用户名或邮箱已存在");
            }
            else if ((int)response.StatusCode == 401)
            {
                throw new Exception("用户名或密码错误");
            }
            else if ((int)response.StatusCode == 404)
            {
                throw new Exception("用户名不存在");
            }
            else if ((int)response.StatusCode == 422)
            {
                var validationErrors = JsonConvert.DeserializeObject<ValidationErrorResponse>(responseString);
                throw new Exception($"验证错误: {string.Join(", ", validationErrors.Detail.Select(e => e.Msg))}");
            }
            else
            {
                throw new Exception($"请求失败，状态码 {response.StatusCode}: {responseString}");
            }
        }
    }
}