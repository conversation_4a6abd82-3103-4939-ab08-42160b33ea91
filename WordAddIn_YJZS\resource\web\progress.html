<!--<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>进度条</title>
        <style>
            .progress {
                position: relative;
                width: 80px; /* Increased size */
                height: 80px; /* Increased size */
                left: calc(50% - 40px); /* Centering adjustment */
                animation: rotate 1.5s steps(8) infinite;
            }

            .svg_loading {
                position: absolute;
                width: 80px; /* Increased size */
                top: 50%;
                transform: translateY(-50%);
            }

            @keyframes rotate {
                from {
                    transform: rotate(0deg);
                }
                to {
                    transform: rotate(360deg);
                }
            }
        </style>
    </head>
    <body>
        <div class="progress">
            <img class="svg_loading" src="../image/loading.svg" alt="">
        </div>
    </body>
</html>-->

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条</title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: white;
        }

        .container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            /* 向上偏移一点 */
            margin-top: -50px;
        }

        .progress-wrapper {
            position: relative;
            width: 150px; /* 增加容器尺寸 */
            height: 150px; /* 增加容器尺寸 */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-circle {
            transform: rotate(-90deg);
            width: 150px; /* 增加圆圈尺寸 */
            height: 150px; /* 增加圆圈尺寸 */
        }

            .progress-circle circle {
                fill: none;
                stroke-width: 6; /* 稍微减小线条宽度 */
            }

            .progress-circle .bg {
                stroke: #f3f3f3;
            }

            .progress-circle .progress {
                stroke: #4CAF50;
                stroke-linecap: round;
                transition: stroke-dashoffset 0.3s ease;
            }

        .progress-text {
            position: absolute;
            font-size: 28px; /* 增大字号 */
            color: #333;
            font-family: Arial, sans-serif;
            /* 移除默认定位，使用 flex 居中 */
        }

        .status-text {
            margin-top: 20px; /* 增加与进度圈的间距 */
            font-size: 28px; /* 增大字号 */
            color: #f90b0b;
            font-family: "Microsoft YaHei", sans-serif;
            text-align: center;
            max-width: 300px; /* 增加最大宽度 */
            word-wrap: break-word;
            line-height: 1.5; /* 增加行高 */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="progress-wrapper">
            <svg class="progress-circle" viewBox="0 0 120 120">
                <circle class="bg" cx="60" cy="60" r="54"></circle>
                <circle class="progress" cx="60" cy="60" r="54"></circle>
            </svg>
            <div class="progress-text">0%</div>
        </div>
        <div class="status-text">准备中...</div>
    </div>

    <script>
        const circle = document.querySelector('.progress-circle .progress');
        const progressText = document.querySelector('.progress-text');
        const statusText = document.querySelector('.status-text');
        const circumference = 2 * Math.PI * 54;

        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = circumference;

        window.chrome.webview.addEventListener('message', event => {
            const { progress, status } = event.data;

            const offset = circumference - (progress / 100 * circumference);
            circle.style.strokeDashoffset = offset;

            progressText.textContent = `${Math.round(progress)}%`;

            if (status) {
                statusText.textContent = status;
            }
        });
    </script>
</body>
</html>
