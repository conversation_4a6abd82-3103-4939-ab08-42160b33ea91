using System;

namespace WordAddIn_YJZS
{
    /// <summary>
    /// LLM健康检查配置类
    /// </summary>
    public static class LlmHealthCheckConfig
    {
        /// <summary>
        /// 是否在API调用失败时使用模拟数据
        /// 生产环境建议设置为 false
        /// 开发/测试环境可以设置为 true
        /// </summary>
        public static bool UseMockDataOnFailure { get; set; } = false;

        /// <summary>
        /// 是否启用LLM健康检查功能
        /// 如果设置为 false，将跳过整个检查过程
        /// </summary>
        public static bool EnableHealthCheck { get; set; } = true;

        /// <summary>
        /// 检查超时时间（秒）
        /// </summary>
        public static int TimeoutSeconds { get; set; } = 20;

        /// <summary>
        /// 状态显示时间（毫秒）
        /// </summary>
        public static int StatusDisplayDurationMs { get; set; } = 2000;

        /// <summary>
        /// 根据环境自动配置
        /// </summary>
        public static void ConfigureForEnvironment(EnvironmentType environment)
        {
            switch (environment)
            {
                case EnvironmentType.Development:
                    UseMockDataOnFailure = true;
                    EnableHealthCheck = true;
                    break;
                
                case EnvironmentType.Testing:
                    UseMockDataOnFailure = true;
                    EnableHealthCheck = true;
                    break;
                
                case EnvironmentType.Production:
                    UseMockDataOnFailure = false;
                    EnableHealthCheck = true;
                    break;
                
                case EnvironmentType.Disabled:
                    EnableHealthCheck = false;
                    break;
            }
        }
    }

    /// <summary>
    /// 环境类型枚举
    /// </summary>
    public enum EnvironmentType
    {
        Development,    // 开发环境
        Testing,        // 测试环境
        Production,     // 生产环境
        Disabled        // 禁用功能
    }
}