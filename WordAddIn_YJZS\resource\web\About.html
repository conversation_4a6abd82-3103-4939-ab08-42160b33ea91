<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于软件</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #ffffff;
        }

        .about-container {
            background: #ffffff;
            backdrop-filter: none;
            padding: 40px;
            border-radius: 0;
            box-shadow: none;
            width: 100%;
            height: 100vh;
            max-width: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .about-title {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: #fff;
            font-size: 24px;
            font-weight: 700;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .about-subtitle {
            color: #555;
            font-size: 16px;
            margin-bottom: 30px;
            text-align: center;
        }

        .about-info {
            margin-bottom: 20px;
        }

        .about-info p {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }

        .about-info strong {
            font-weight: 500;
            color: #4a90e2;
            margin-right: 10px;
            min-width: 100px;
        }

        .button-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }

        .theme-button {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .theme-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
        }

        .theme-button:active {
            transform: translateY(0);
        }

        a {
            color: #4a90e2;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #63b3ed;
        }

        .copyright {
            color: #777;
            margin-top: 30px;
            font-size: 12px;
            text-align: center;
        }

        /* 模态对话框样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            min-width: 300px;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            text-align: center;
        }

        .modal-content {
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-button {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-button:hover {
            background: linear-gradient(45deg, #3a80d2, #53a8ff);
        }

        /* 加载动画样式 */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4a90e2;
            border-radius: 50%;
            margin: 0 auto 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #checkingUpdate {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #updateResult {
            padding: 10px;
        }

        .update-available {
            color: #2ecc71;
            font-weight: 500;
            text-align: center;
        }

        .no-update {
            color: #7f8c8d;
            text-align: center;
        }

        .error-message {
            color: #e74c3c;
            text-align: center;
        }
        
        /* 版本更新日志 */
        .changelog {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            font-size: 13px;
            color: #666;
            max-height: 150px;
            overflow-y: auto;
            text-align: left;
        }

        /* 更新状态样式 */
        .update-available {
            color: #28a745;
            font-weight: bold;
        }

        .no-update {
            color: #6c757d;
            font-weight: bold;
        }

        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 模态对话框HTML -->
    <div class="modal-overlay" id="modalOverlay"></div>
    <div class="modal" id="updateModal">
        <div class="modal-content" id="modalContent">
            <!-- 内容将由JavaScript动态填充 -->
        </div>
        <button class="modal-button" onclick="closeModal()">确定</button>
    </div>

    <div class="about-container">
        <p class="about-subtitle">了解我们的产品和团队</p>
        <div class="about-info">
            <p><strong>当前版本:</strong> <span id="currentVersion">0.9.4</span></p>
            <p><strong>最新版本:</strong> <span id="latestVersion">检查中...</span></p>
            <p><strong>官方网站:</strong> <a href="http://gpt.gedi.com.cn/" target="_blank">gpt.gedi.com.cn</a></p>
            <p><strong>开发人员:</strong><a href="http://gpt.gedi.com.cn/name.html" target="_blank">gpt.gedi.com.cn/name.html</a></p>
        </div>
        <div class="button-container">
            <button class="theme-button" onclick="checkForUpdates()">检查更新</button>
            <!--<button class="theme-button" onclick="window.open('http://gpt.gedi.com.cn:7072/', '_blank')">访问官网</button>-->
        </div>
        <p class="copyright">版权所有 © GEDI. 版权所有。</p>
    </div>

    <script>
        // 页面加载后的处理
        window.onBridgeReady = function() {
            console.log("页面桥接已准备好，C#端将自动检查版本更新...");
            // 不再自动打开下载页面，由C#端的自动检查逻辑处理
        };

        // 手动点击检查更新按钮的函数
        function checkForUpdates() {
            console.log("checkForUpdates: 开始手动检查更新。");
            const modalContent = document.getElementById('modalContent');
            
            // 每次都重新生成“检查中”的HTML内容
            modalContent.innerHTML = `
                <div id="checkingUpdate" style="display: flex; flex-direction: column; align-items: center;">
                    <div class="loading-spinner"></div>
                    <p>正在检查更新...</p>
                </div>
            `;

            // 显示模态框
            document.getElementById('modalOverlay').style.display = 'block';
            document.getElementById('updateModal').style.display = 'block';

            // 调用C#端的版本检查
            if (window.checkVersionFromCSharp) {
                window.checkVersionFromCSharp();
            } else if (window.chrome && window.chrome.webview) {
                window.chrome.webview.postMessage({
                    action: 'checkVersion',
                    currentVersion: document.getElementById('currentVersion').textContent
                });
            } else {
                // 降级处理
                modalContent.innerHTML = `<p class='error-message'>无法连接到更新服务。</p>`;
            }
        }


        
        function showError(message) {
            console.error("显示错误信息:", message);
            // 简化的错误处理，直接打开下载页面
            window.open('http://115.156.114.151:12010/downloads', '_blank');
        }

        function closeModal() {
            console.log('closeModal: 关闭模态框。');
            document.getElementById('modalOverlay').style.display = 'none';
            document.getElementById('updateModal').style.display = 'none';
            // 清空内容，确保下次点击时是干净的状态
            document.getElementById('modalContent').innerHTML = '';
        }

        // 添加控制台日志方法
        console = console || {};
        console.log = console.log || function(msg) { 
            // 可以在这里添加自定义日志处理
        };
        
        // 页面加载完成时的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM 已加载完成");
            // 设置版本显示
            document.getElementById('latestVersion').textContent = '点击检查更新';
        });

        // 添加点击模态框背景关闭功能
        document.addEventListener('click', function(event) {
            if (event.target.id === 'modalOverlay') {
                closeModal();
            }
        });
    </script>
</body>
</html>