using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System;
using WordAddIn_YJZS;

namespace WordAddIn_YJZS
{
    public class CheckReportApiService
    {
        private readonly HttpClient client;

        public CheckReportApiService()
        {
            client = HttpClientManager.Client;
        }

        // 请求模型
        public class ProjectInfoRequest
        {
            [JsonProperty("username")]
            public string Username { get; set; }

            [JsonProperty("project_name")]
            public string ProjectName { get; set; }
        }

        // 响应数据项模型
        public class CheckReportItem
        {
            [JsonProperty("missing_flag")]
            public bool MissingFlag { get; set; }

            [JsonProperty("content")]
            public string Content { get; set; }
        }

        // API响应模型
        public class CheckReportResponse
        {
            [JsonProperty("status_code")]
            public int StatusCode { get; set; }

            [JsonProperty("message")]
            public string Message { get; set; }

            [JsonProperty("data")]
            public List<CheckReportItem> Data { get; set; }
        }

        private void ShowMessage(string message, string caption = "提示",
            MessageBoxButtons buttons = MessageBoxButtons.OK,
            MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        /// <summary>
        /// 生成检查报告
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="projectName">项目名称</param>
        /// <returns>检查报告数据列表</returns>
        public async Task<List<CheckReportItem>> GenerateCheckReportAsync(string username, string projectName)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowMessage("用户名不能为空");
                    return new List<CheckReportItem>();
                }
                if (string.IsNullOrWhiteSpace(projectName))
                {
                    ShowMessage("项目名称不能为空");
                    return new List<CheckReportItem>();
                }

                var requestBody = new ProjectInfoRequest
                {
                    Username = username,
                    ProjectName = projectName
                };

                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );

                try
                {
                    var response = await client.PostAsync(AllApi.CheckReportUrl, jsonContent);
                    var responseString = await response.Content.ReadAsStringAsync();

                    System.Diagnostics.Debug.WriteLine($"检查报告API响应: {responseString}");

                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            var apiResponse = JsonConvert.DeserializeObject<CheckReportResponse>(responseString);
                            if (apiResponse?.Data != null && apiResponse.StatusCode == 200)
                            {
                                System.Diagnostics.Debug.WriteLine($"检查报告生成成功，共{apiResponse.Data.Count}项数据");
                                return apiResponse.Data;
                            }
                            else
                            {
                                ShowErrorMessage($"检查报告生成失败: {apiResponse?.Message ?? "未知错误"}");
                                return new List<CheckReportItem>();
                            }
                        }
                        catch (JsonException ex)
                        {
                            ShowErrorMessage($"解析检查报告响应数据时出错: {ex.Message}");
                            return new List<CheckReportItem>();
                        }
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                    {
                        ShowErrorMessage($"服务器内部错误: {responseString}");
                        return new List<CheckReportItem>();
                    }

                    ShowErrorMessage($"检查报告请求失败: {response.StatusCode} - {responseString}");
                    return new List<CheckReportItem>();
                }
                catch (HttpRequestException ex)
                {
                    ShowErrorMessage($"网络请求错误: {ex.Message}");
                    return new List<CheckReportItem>();
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"生成检查报告时发生未知错误: {ex.Message}");
                return new List<CheckReportItem>();
            }
        }

        /// <summary>
        /// 验证检查报告响应是否有效
        /// </summary>
        /// <param name="reportItems">检查报告项列表</param>
        /// <returns>是否有效</returns>
        public bool ValidateResponse(List<CheckReportItem> reportItems)
        {
            return reportItems != null && reportItems.Count > 0;
        }
    }
}