<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .register-container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            transition: all 0.3s ease;
        }

        .register-title {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: #fff;
            font-size: 24px;
            font-weight: 700;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }

        .form-group {
            margin-bottom: 20px;
        }

            .form-group input {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                transition: all 0.3s ease;
            }

                .form-group input:focus {
                    border-color: #4a90e2;
                    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
                    outline: none;
                }

        .error {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }

        .theme-button {
            background: linear-gradient(45deg, #4a90e2, #63b3ed);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
            width: 100%;
            margin-top: 20px;
        }

            .theme-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
            }

            .theme-button:active {
                transform: translateY(0);
            }
    </style>
</head>
<body>
    <div class="register-container">
        <h2 class="register-title">注册</h2>
        <form id="registrationForm">
            <div class="form-group">
                <input type="text" id="username" name="username" placeholder="用户名" required>
                <div class="error" id="usernameError"></div>
            </div>
            <div class="form-group">
                <input type="password" id="password" name="password" placeholder="密码" required>
                <div class="error" id="passwordError"></div>
            </div>
            <div class="form-group">
                <input type="password" id="confirm_password" name="confirm_password" placeholder="确认密码" required>
                <div class="error" id="confirmPasswordError"></div>
            </div>
            <div class="form-group">
                <input type="email" id="email" name="email" placeholder="电子邮件地址" required>
                <div class="error" id="emailError"></div>
            </div>
            <div class="form-group">
                <input type="text" id="phone" name="phone" placeholder="手机号码" required>
                <div class="error" id="phoneError"></div>
            </div>
            <div class="form-group">
                <input type="text" id="department" name="department" placeholder="所属部门" required>
                <div class="error" id="departmentError"></div>
            </div>
            <div class="form-group">
                <input type="text" id="fullname" name="fullname" placeholder="姓名" required>
                <div class="error" id="fullnameError"></div>
            </div>
            <button type="submit" class="theme-button">注册</button>
        </form>
    </div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', function (event) {
            event.preventDefault();

            let valid = true;

            // Username validation
            const username = document.getElementById('username').value;
            const usernameError = document.getElementById('usernameError');
            if (!/^[a-zA-Z0-9]{1,20}$/.test(username)) {
                usernameError.textContent = '用户名只能是中文，不能使用英文、数字和字母的组合，且长度不超过20。';
                valid = false;
            } else {
                usernameError.textContent = '';
            }

            // Phone validation
            const phone = document.getElementById('phone').value;
            const phoneError = document.getElementById('phoneError');
            if (!/^[0-9]{1,11}$/.test(phone)) {
                phoneError.textContent = '电话号码只能是数字，且长度不超过11。';
                valid = false;
            } else {
                phoneError.textContent = '';
            }

            // Department validation
            const department = document.getElementById('department').value;
            const departmentError = document.getElementById('departmentError');
            if (!/^[\u4e00-\u9fa5a-zA-Z]{1,20}$/.test(department)) {
                departmentError.textContent = '所属部门只能是文字和英文，且长度不超过20。';
                valid = false;
            } else {
                departmentError.textContent = '';
            }

            // Fullname validation
            const fullname = document.getElementById('fullname').value;
            const fullnameError = document.getElementById('fullnameError');
            if (!/^[\u4e00-\u9fa5a-zA-Z]{1,20}$/.test(fullname)) {
                fullnameError.textContent = '姓名只能是文字和英文，且长度不超过20。';
                valid = false;
            } else {
                fullnameError.textContent = '';
            }

            // Email validation
            const email = document.getElementById('email').value;
            const emailError = document.getElementById('emailError');
            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailPattern.test(email)) {
                emailError.textContent = '请输入有效的电子邮件地址。';
                valid = false;
            } else {
                emailError.textContent = '';
            }

            // Password validation
            const password = document.getElementById('password').value;
            const passwordError = document.getElementById('passwordError');
            if (!/^[a-zA-Z0-9]{1,20}$/.test(password)) {
                passwordError.textContent = '密码只能是数字和字母的组合，且长度不超过20。';
                valid = false;
            } else {
                passwordError.textContent = '';
            }

            // Confirm Password validation
            const confirmPassword = document.getElementById('confirm_password').value;
            const confirmPasswordError = document.getElementById('confirmPasswordError');
            if (password !== confirmPassword) {
                confirmPasswordError.textContent = '密码和确认密码必须一致。';
                valid = false;
            } else {
                confirmPasswordError.textContent = '';
            }

            if (valid) {
                // Collect form data
                const formData = {
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    confirm_password: document.getElementById('confirm_password').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value,
                    department: document.getElementById('department').value,
                    fullname: document.getElementById('fullname').value
                };

                // Send form data to the host application via WebView2
                window.chrome.webview.postMessage(formData);
            }
        });
    </script>
</body>
</html>