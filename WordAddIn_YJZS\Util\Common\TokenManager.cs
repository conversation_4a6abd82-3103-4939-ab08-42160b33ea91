﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public class TokenManager
    {
        private static readonly string TokenFileName = "user_token.dat";
        private static readonly string FolderName = "WordAddIn_YJZS";

        // 使用静态属性而不是字段，确保每次访问时都重新计算路径
        public static string TokenFilePath
        {
            get
            {
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string folderPath = Path.Combine(appDataPath, FolderName);

                // 确保目录存在
                EnsureDirectoryExists(folderPath);

                return Path.Combine(folderPath, TokenFileName);
            }
        }

        // 确保目录存在的辅助方法
        private static void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"创建目录失败: {ex.Message}");
                    // 如果无法创建目录，记录错误但不抛出异常
                }
            }
        }

        // 用于加密的密钥和IV
        private static readonly byte[] EncryptionKey = Encoding.UTF8.GetBytes("YJZSWordAddInKey");
        private static readonly byte[] EncryptionIV = Encoding.UTF8.GetBytes("YJZSWordAddInIV1");

        public class TokenData
        {
            public string Username { get; set; }
            public string FullName { get; set; }
            public string AccessToken { get; set; }
            public DateTime ExpiresAt { get; set; }
        }

        // 保存令牌到文件，设置15天有效期
        public static void SaveToken(string username, string fullName, string accessToken)
        {
            try
            {
                // 创建令牌数据，设置15天有效期
                var tokenData = new TokenData
                {
                    Username = username,
                    FullName = fullName,
                    AccessToken = accessToken
                };

                // 序列化令牌数据
                string jsonData = JsonConvert.SerializeObject(tokenData);
                byte[] dataBytes = Encoding.UTF8.GetBytes(jsonData);

                // 加密数据
                byte[] encryptedData;
                using (var aes = Aes.Create())
                {
                    aes.Key = EncryptionKey;
                    aes.IV = EncryptionIV;

                    using (var encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                    {
                        encryptedData = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);
                    }
                }

                // 确保目录存在
                string directory = Path.GetDirectoryName(TokenFilePath);
                EnsureDirectoryExists(directory);

                // 写入文件
                File.WriteAllBytes(TokenFilePath, encryptedData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存令牌时出错: {ex.Message}");
                throw;
            }
        }

        // 从文件读取令牌
        public static TokenData GetToken()
        {
            try
            {
                if (!File.Exists(TokenFilePath))
                {
                    return null;
                }

                // 读取加密数据
                byte[] encryptedData = File.ReadAllBytes(TokenFilePath);

                // 解密数据
                string json = DecryptData(encryptedData);

                // 反序列化为对象
                var tokenData = JsonConvert.DeserializeObject<TokenData>(json);

                return tokenData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取令牌时出错: {ex.Message}");
                // 如果出错，尝试删除可能损坏的令牌文件
                if (File.Exists(TokenFilePath))
                {
                    File.Delete(TokenFilePath);
                }
                return null;
            }
        }

        // 清除令牌
        public static void ClearToken()
        {
            try
            {
                if (File.Exists(TokenFilePath))
                {
                    File.Delete(TokenFilePath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除令牌时出错: {ex.Message}");
            }
        }

        // 加密数据
        private static byte[] EncryptData(string data)
        {
            using (Aes aes = Aes.Create())
            {
                aes.Key = EncryptionKey;
                aes.IV = EncryptionIV;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter sw = new StreamWriter(cs))
                        {
                            sw.Write(data);
                        }
                        return ms.ToArray();
                    }
                }
            }
        }

        // 解密数据
        private static string DecryptData(byte[] encryptedData)
        {
            using (Aes aes = Aes.Create())
            {
                aes.Key = EncryptionKey;
                aes.IV = EncryptionIV;

                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                using (MemoryStream ms = new MemoryStream(encryptedData))
                {
                    using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader sr = new StreamReader(cs))
                        {
                            return sr.ReadToEnd();
                        }
                    }
                }
            }
        }
    }
}