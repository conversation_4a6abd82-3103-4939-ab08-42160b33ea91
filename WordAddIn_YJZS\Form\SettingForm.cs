﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class SettingForm : Form
    {
        private readonly string SETTINGS_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");

        public SettingForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "Setting.html");
                webView21.Source = new Uri(htmlFilePath);

                // 注册消息处理程序
                webView21.CoreWebView2.WebMessageReceived += HandleWebMessage;
                webView21.NavigationCompleted += WebView_NavigationCompleted;

                //MessageBox.Show("用户协议WebView2初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void HandleWebMessage(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                string jsonMessage = e.WebMessageAsJson;
                var message = JsonConvert.DeserializeObject<dynamic>(jsonMessage);
                string action = message.action.ToString();

                switch (action)
                {
                    case "selectFolder":
                        using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
                        {
                            folderDialog.Description = "选择文件保存位置";
                            folderDialog.ShowNewFolderButton = true;

                            if (folderDialog.ShowDialog() == DialogResult.OK)
                            {
                                string selectedPath = folderDialog.SelectedPath;
                                var response = new
                                {
                                    action = "folderSelected",
                                    path = selectedPath
                                };

                                string jsonResponse = JsonConvert.SerializeObject(response);
                                webView21.CoreWebView2.PostWebMessageAsJson(jsonResponse);
                            }
                        }
                        break;

                    case "saveSettings":
                        try
                        {
                            string filePath = message.filePath.ToString();
                            string modelSelection = message.modelSelection?.ToString() ?? "auto";

                            // 确保目录存在
                            string directoryPath = Path.GetDirectoryName(SETTINGS_FILE_PATH);
                            if (!Directory.Exists(directoryPath) && directoryPath != null)
                            {
                                Directory.CreateDirectory(directoryPath);
                            }

                            // 确保选择的路径存在
                            if (!Directory.Exists(filePath))
                            {
                                Directory.CreateDirectory(filePath);
                            }

                            // 创建设置对象
                            var settings = new
                            {
                                FilePath = filePath,
                                ModelSelection = modelSelection
                            };

                            // 将设置序列化为JSON
                            string settingsJson = JsonConvert.SerializeObject(settings, Formatting.Indented);

                            // 保存设置到文件
                            File.WriteAllText(SETTINGS_FILE_PATH, settingsJson);

                            // 复制模板文件到新路径
                            CopyTemplateFiles(filePath);

                            // 通知 Ribbon_YJZS 更新路径设置
                            try
                            {
                                Ribbon_YJZS.UpdateSavePath(filePath);
                                Console.WriteLine($"已通知 Ribbon_YJZS 更新路径: {filePath}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"通知 Ribbon_YJZS 更新路径时出错: {ex.Message}");
                            }

                            // 输出调试信息
                            Console.WriteLine($"设置已保存到: {SETTINGS_FILE_PATH}");
                            Console.WriteLine($"设置内容: {settingsJson}");

                            // 发送保存成功的消息
                            var saveResponse = new
                            {
                                action = "settingsSaved",
                                success = true
                            };

                            string saveJsonResponse = JsonConvert.SerializeObject(saveResponse);
                            webView21.CoreWebView2.PostWebMessageAsJson(saveJsonResponse);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"保存设置时出错: {ex.Message}");
                            var errorResponse = new
                            {
                                action = "settingsSaved",
                                success = false,
                                error = ex.Message
                            };

                            string errorJsonResponse = JsonConvert.SerializeObject(errorResponse);
                            webView21.CoreWebView2.PostWebMessageAsJson(errorJsonResponse);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理Web消息时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                LoadAndApplySettings();
            }
            else
            {
                MessageBox.Show("设置页面加载失败");
            }
        }

        private void LoadAndApplySettings()
        {
            try
            {
                // 始终获取当前实际使用的路径
                string currentPath = Ribbon_YJZS.GetDefaultSavePath();

                // 准备设置数据
                var settings = new
                {
                    FilePath = currentPath,
                    ModelSelection = "auto"
                };

                var response = new
                {
                    action = "loadSettings",
                    settings = settings
                };

                string jsonResponse = JsonConvert.SerializeObject(response);
                webView21.CoreWebView2.PostWebMessageAsJson(jsonResponse);

                System.Diagnostics.Debug.WriteLine($"已向WebView发送当前路径设置: {currentPath}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时出错: {ex.Message}");

                // 如果出错，至少显示一个备用路径
                try
                {
                    string fallbackPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Word-Solvy");

                    var fallbackSettings = new
                    {
                        FilePath = fallbackPath,
                        ModelSelection = "auto"
                    };

                    var fallbackResponse = new
                    {
                        action = "loadSettings",
                        settings = fallbackSettings
                    };

                    string fallbackJsonResponse = JsonConvert.SerializeObject(fallbackResponse);
                    webView21.CoreWebView2.PostWebMessageAsJson(fallbackJsonResponse);

                    System.Diagnostics.Debug.WriteLine($"使用备用路径: {fallbackPath}");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"设置备用路径时也出错: {fallbackEx.Message}");
                }
            }
        }

        private void SaveSettings(string filePath)
        {
            try
            {
                // 确保选择的路径存在
                if (!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                var settings = new
                {
                    FilePath = filePath,
                    ModelSelection = "auto" // 添加默认值
                };

                string settingsJson = JsonConvert.SerializeObject(settings, Formatting.Indented);
                File.WriteAllText(SETTINGS_FILE_PATH, settingsJson);

                // 复制模板文件到Style目录
                CopyTemplateFiles(filePath);

                // 通知 Ribbon_YJZS 更新路径设置
                try
                {
                    Ribbon_YJZS.UpdateSavePath(filePath);
                    Console.WriteLine($"已通知 Ribbon_YJZS 更新路径: {filePath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"通知 Ribbon_YJZS 更新路径时出错: {ex.Message}");
                }

                // 输出调试信息
                Console.WriteLine($"设置已保存到: {SETTINGS_FILE_PATH}");
                Console.WriteLine($"设置内容: {settingsJson}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存设置时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CopyTemplateFiles(string basePath)
        {
            try
            {
                string stylePath = Path.Combine(basePath, "Style");
                if (!Directory.Exists(stylePath))
                {
                    Directory.CreateDirectory(stylePath);
                }

                // 从安装目录复制所有Style文件
                string sourceStylePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style");

                if (Directory.Exists(sourceStylePath))
                {
                    // 复制所有文件
                    foreach (string file in Directory.GetFiles(sourceStylePath))
                    {
                        string fileName = Path.GetFileName(file);
                        string targetFile = Path.Combine(stylePath, fileName);
                        File.Copy(file, targetFile, true);
                    }

                    // 递归复制所有子目录
                    foreach (string dir in Directory.GetDirectories(sourceStylePath))
                    {
                        string dirName = Path.GetFileName(dir);
                        string targetDir = Path.Combine(stylePath, dirName);

                        if (!Directory.Exists(targetDir))
                        {
                            Directory.CreateDirectory(targetDir);
                        }

                        // 复制子目录中的文件
                        foreach (string file in Directory.GetFiles(dir))
                        {
                            string fileName = Path.GetFileName(file);
                            string targetFile = Path.Combine(targetDir, fileName);
                            File.Copy(file, targetFile, true);
                        }
                    }
                }
                else
                {
                    MessageBox.Show($"未找到Style目录: {sourceStylePath}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制模板文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 添加窗体关闭事件处理
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // 确保设置已保存
            if (File.Exists(SETTINGS_FILE_PATH))
            {
                try
                {
                    // 确保文件不是只读的
                    File.SetAttributes(SETTINGS_FILE_PATH, FileAttributes.Normal);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"设置文件属性时出错: {ex.Message}");
                }
            }
        }
    }
}