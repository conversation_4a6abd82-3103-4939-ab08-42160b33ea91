﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace WordAddIn_YJZS
{
    public class ApiResponse<T>
    {
        [JsonProperty("status_code")]
        public int status_code { get; set; }

        [JsonProperty("message")]
        public string message { get; set; }

        [JsonProperty("data")]
        public T data { get; set; }
    }

    public class ContentItem
    {
        private object _content;

        [JsonProperty("style")]
        public string style { get; set; } = "0";

        [JsonProperty("content")]
        public object content
        {
            get => _content;
            set
            {
                _content = value;
                if (value != null)
                {
                    // 如果是 JArray，转换为 List<List<string>>
                    if (value is JArray jArray)
                    {
                        _content = jArray.Select(row =>
                            ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                        ).ToList();
                    }
                }
            }
        }

        public string GetContentAsString()
        {
            return content?.ToString() ?? string.Empty;
        }

        // 添加新的下载图片方法
        public async Task<string> DownloadImageAsync()
        {
            if (style != "-1" || content == null)
                return null;

            try
            {
                // 使用统一的路径获取方法
                string savePath = Ribbon_YJZS.GetDefaultSavePath();

                // 创建 Images 子文件夹
                string imagesPath = Path.Combine(savePath, "Images");
                if (!Directory.Exists(imagesPath))
                {
                    Directory.CreateDirectory(imagesPath);

                    // 设置Images文件夹为隐藏
                    try
                    {
                        DirectoryInfo dirInfo = new DirectoryInfo(imagesPath);
                        dirInfo.Attributes |= FileAttributes.Hidden;
                        Console.WriteLine($"Images文件夹已设置为隐藏: {imagesPath}");
                    }
                    catch (Exception hideEx)
                    {
                        Console.WriteLine($"设置Images文件夹隐藏属性时出错: {hideEx.Message}");
                    }
                }

                string imageUrl = content.ToString().Trim('"');
                Console.WriteLine($"准备下载图片，URL: {imageUrl}");

                using (var client = new HttpClient())
                {
                    // 设置30分钟超时，避免无限等待
                    client.Timeout = TimeSpan.FromMinutes(30);

                    var imageBytes = await client.GetByteArrayAsync(imageUrl);

                    // 使用URL中的原始文件名
                    string fileName = Path.GetFileName(new Uri(imageUrl).LocalPath);
                    string imagePath = Path.Combine(imagesPath, fileName);

                    Console.WriteLine($"保存图片到: {imagePath}");
                    File.WriteAllBytes(imagePath, imageBytes);

                    // 验证文件是否成功保存
                    if (File.Exists(imagePath))
                    {
                        Console.WriteLine("图片保存成功");
                        return imagePath;
                    }
                    else
                    {
                        Console.WriteLine("图片保存失败");
                        return null;
                    }
                }
            }
            catch (TaskCanceledException ex)
            {
                Console.WriteLine($"下载图片时超时: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"下载图片时出错: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                return null;
            }     
        }

        public List<List<string>> GetTableContent()
        {
            if (style != "-3" || content == null)
                return null;

            try
            {
                if (content is List<List<string>> tableData)
                {
                    return tableData;
                }
                else if (content is string strContent)
                {
                    return JsonConvert.DeserializeObject<List<List<string>>>(strContent);
                }
                else if (content is JArray jArray)
                {
                    return jArray.Select(row =>
                        ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                    ).ToList();
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理表格内容时出错: {ex.Message}");
                return null;
            }
        }

        public ContentItem()
        {
        }

        public ContentItem(string content, string style = "0")
        {
            this.content = content;
            this.style = style;
        }
    }

    public class ValidationErrorResponse
    {
        public List<ValidationError> Detail { get; set; }
    }

    public class ValidationError
    {
        public List<object> Loc { get; set; }
        public string Msg { get; set; }
        public string Type { get; set; }
    }
}
