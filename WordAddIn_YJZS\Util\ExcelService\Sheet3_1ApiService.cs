﻿using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Net.Http.Headers;
using System.Linq;
using WordAddIn_YJZS;
using System.IO;

namespace WordAddIn_YJZS
{
    public class Sheet3_1ApiService
    {
        private readonly HttpClient client;

        public Sheet3_1ApiService()
        {
            client = HttpClientManager.Client;
        }

        // 表3-1数据模型
        public class Table3_1Request
        {
            [JsonProperty("username")]
            public string Username { get; set; }

            [JsonProperty("project_name")]
            public string ProjectName { get; set; }

            [JsonProperty("start_row")]
            public int StartRow { get; set; }

            [JsonProperty("start_col")]
            public int StartCol { get; set; }
        }

        public class Table3_1Response
        {
            [JsonProperty("content_list")]
            public List<List<object>> ContentList { get; set; }

            [JsonProperty("mark_list")]
            public List<MarkInfo> MarkList { get; set; }

            [JsonProperty("merged_list")]
            public List<List<int>> MergedList { get; set; }
        }

        public class MarkInfo
        {
            [JsonProperty("row")]
            public int Row { get; set; }

            [JsonProperty("col")]
            public int Col { get; set; }

            [JsonProperty("content")]
            public string Content { get; set; }
        }

        // 生成表3-1
        public async Task<Table3_1Response> GenerateTable3_1Async(string username, string projectName, int startRow = 1, int startCol = 1)
        {
            try
            {
                var request = new Table3_1Request
                {
                    Username = username,
                    ProjectName = projectName,
                    StartRow = startRow,
                    StartCol = startCol
                };

                // 记录请求信息到日志
                System.Diagnostics.Debug.WriteLine($"表3-1请求: 用户={username}, 项目={projectName}, 起始行={startRow}, 起始列={startCol}");

                var response = await PostJsonAsync<Table3_1Response>(
                    AllApi.Sheet3_1Url,
                    request,
                    "生成表3-1失败"
                );

                return response;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成表3-1时出错: {ex.Message}");
                throw new Exception($"生成表3-1失败: {ex.Message}", ex);
            }
        }

        // 验证响应数据
        public bool ValidateResponse(Table3_1Response response)
        {
            return response != null && response.ContentList != null;
        }

        // 处理内容列表
        public List<List<string>> ProcessContentList(List<List<object>> contentList)
        {
            if (contentList == null)
                return new List<List<string>>();

            List<List<string>> result = new List<List<string>>();

            foreach (var row in contentList)
            {
                List<string> processedRow = new List<string>();
                foreach (var cell in row)
                {
                    if (cell == null)
                    {
                        processedRow.Add(string.Empty);
                    }
                    else
                    {
                        processedRow.Add(cell.ToString());
                    }
                }
                result.Add(processedRow);
            }

            return result;
        }

        // 处理批注列表
        public Dictionary<(int, int), string> ProcessMarkList(List<MarkInfo> markList)
        {
            Dictionary<(int, int), string> result = new Dictionary<(int, int), string>();

            if (markList != null)
            {
                foreach (var mark in markList)
                {
                    result.Add((mark.Row, mark.Col), mark.Content);
                }
            }

            return result;
        }

        // 处理合并单元格列表
        public List<List<int>> ProcessMergedList(List<List<int>> mergedList)
        {
            return mergedList ?? new List<List<int>>();
        }        

        private void ShowMessage(string message)
        {
            MessageBox.Show(message);
        }

        // POST请求处理
        private async Task<T> PostJsonAsync<T>(string url, object data, string dataField)
        {
            using (var jsonContent = new StringContent(JsonConvert.SerializeObject(data)))
            {
                jsonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    return await HandleResponseAsync<T>(response, dataField);
                }
                catch (TaskCanceledException)
                {
                    throw new Exception("请求超时");
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception("网络错误：" + ex.Message);
                }
            }
        }

        // 处理API响应
        private static async Task<T> HandleResponseAsync<T>(HttpResponseMessage response, string dataField)
        {
            var responseString = await response.Content.ReadAsStringAsync();
            System.Diagnostics.Debug.WriteLine($"[DEBUG] Response received. Length: {responseString.Length} characters.");
            //MessageBox.Show($"Raw Response:\n{responseString}", "Raw Response Debug");

            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                    // 首先解析为 JObject，这样我们可以更灵活地处理数据
                    JObject jsonResponse = JObject.Parse(responseString);
                    stopwatch.Stop();
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] JObject.Parse completed in {stopwatch.ElapsedMilliseconds} ms.");

                    // 检查状态码
                    int statusCode = jsonResponse["status_code"].Value<int>();
                    string message = jsonResponse["message"].Value<string>();

                    if (statusCode == 200)
                    {
                        // 获取数据部分
                        JToken dataToken = jsonResponse["data"];
                        if (dataToken == null)
                        {
                            throw new Exception("响应数据为空");
                        }

                        // 直接将数据部分转换为目标类型
                        var stopwatchConvert = System.Diagnostics.Stopwatch.StartNew();
                        T result = dataToken.ToObject<T>();
                        stopwatchConvert.Stop();
                        System.Diagnostics.Debug.WriteLine($"[DEBUG] dataToken.ToObject<T> completed in {stopwatchConvert.ElapsedMilliseconds} ms.");

                        // 调试输出
                        if (typeof(T) == typeof(Table3_1Response))
                        {
                            var tableResponse = result as Table3_1Response;
                            string debugInfo = "解析后的数据:\n";
                            if (tableResponse?.ContentList != null)
                            {
                                debugInfo += $"ContentList Count: {tableResponse.ContentList.Count}\n";
                                if (tableResponse.ContentList.Count > 0)
                                {
                                    debugInfo += $"First Row Count: {tableResponse.ContentList[0].Count}\n";
                                    debugInfo += "First Row Content: ";
                                    foreach (var item in tableResponse.ContentList[0].Take(5))
                                    {
                                        debugInfo += $"{item?.ToString() ?? "null"}, ";
                                    }
                                }
                            }
                            //MessageBox.Show(debugInfo, "Data Parsing Debug");
                        }

                        return result;
                    }
                    else
                    {
                        throw new Exception($"API错误: {message}");
                    }
                }
                catch (JsonException ex)
                {
                    //MessageBox.Show($"JSON解析错误: {ex.Message}\n\nResponse String: {responseString}", "JSON Error Debug");
                    throw new Exception($"JSON解析错误: {ex.Message}");
                }
            }
            else
            {
                //MessageBox.Show($"HTTP错误:\nStatus: {response.StatusCode}\nContent: {responseString}", "HTTP Error Debug");
                throw new Exception($"HTTP错误 {response.StatusCode}: {responseString}");
            }
        }

        // API响应模型
        private class ApiResponse<T>
        {
            [JsonProperty("status_code")]
            public int status_code { get; set; }

            [JsonProperty("message")]
            public string message { get; set; }

            [JsonProperty("data")]
            public T data { get; set; }
        }

        // 验证错误响应模型
        private class ValidationErrorResponse
        {
            [JsonProperty("detail")]
            public List<ValidationError> Detail { get; set; }
        }

        private class ValidationError
        {
            [JsonProperty("loc")]
            public List<string> Loc { get; set; }

            [JsonProperty("msg")]
            public string Msg { get; set; }

            [JsonProperty("type")]
            public string Type { get; set; }
        }
    }
}