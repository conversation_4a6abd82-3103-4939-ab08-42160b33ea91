﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class RegisterForm : Form
    {
        private readonly LoginApiService _loginApiService = new LoginApiService();

        public RegisterForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "register.html");
                webView21.Source = new Uri(htmlFilePath);

                webView21.NavigationCompleted += WebView_NavigationCompleted;
                // 添加消息接收事件
                webView21.CoreWebView2.WebMessageReceived += WebView_WebMessageReceived;

                //MessageBox.Show("用户协议WebView2初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                //MessageBox.Show("用户协议页面加载完成");
            }
            else
            {
                MessageBox.Show("用户协议页面加载失败");
            }
        }

        private async void WebView_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                string message = e.WebMessageAsJson;
                var formData = JsonConvert.DeserializeObject<RegisterFormData>(message);

                try
                {
                    var registrationResult = await _loginApiService.RegisterAsync(
                        formData.Username,
                        formData.Password,
                        formData.Email,
                        formData.Phone,
                        formData.Department,
                        formData.Fullname
                    );

                    // 注册成功，通知 HTML 页面
                    await webView21.CoreWebView2.ExecuteScriptAsync($"alert('注册成功！');");
                    this.Close();
                }
                catch (Exception ex)
                {
                    // 注册失败，反馈错误信息给 HTML 页面
                    await webView21.CoreWebView2.ExecuteScriptAsync($"alert('注册失败: {ex.Message}');");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理Web消息时出错: {ex.Message}");
            }
        }
    }
}
