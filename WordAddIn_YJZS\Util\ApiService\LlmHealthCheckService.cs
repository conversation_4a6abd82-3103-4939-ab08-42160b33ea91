using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public class LlmHealthCheckService
    {
        private readonly HttpClient _httpClient;
        private readonly bool _useMockDataOnFailure;

        public LlmHealthCheckService(bool useMockDataOnFailure = false)
        {
            _httpClient = HttpClientManager.CreateCancellableClient(TimeSpan.FromSeconds(20));
            _useMockDataOnFailure = useMockDataOnFailure;
        }

        /// <summary>
        /// 检查LLM服务健康状态
        /// </summary>
        /// <returns>LLM健康检查结果</returns>
        public async Task<LlmHealthCheckResponse> CheckLlmHealthAsync()
        {
            try
            {
                var url = $"{AllApi.BaseUrl}/common/llm_chat/health_check";
                
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                
                var jsonContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<LlmHealthCheckResponse>(jsonContent);
                
                return result;
            }
            catch (HttpRequestException ex)
            {
                if (_useMockDataOnFailure)
                {
                    System.Diagnostics.Debug.WriteLine($"LLM API调用失败，使用模拟数据: {ex.Message}");
                    return CreateMockResponse(hasErrors: true);
                }
                throw new Exception($"网络连接失败，无法检测LLM服务状态：{ex.Message}");
            }
            catch (TaskCanceledException)
            {
                if (_useMockDataOnFailure)
                {
                    System.Diagnostics.Debug.WriteLine("LLM API调用超时，使用模拟数据");
                    return CreateMockResponse(hasErrors: true);
                }
                throw new Exception("LLM服务检测超时，请检查网络连接或稍后重试");
            }
            catch (JsonException ex)
            {
                if (_useMockDataOnFailure)
                {
                    System.Diagnostics.Debug.WriteLine($"LLM API响应解析失败，使用模拟数据: {ex.Message}");
                    return CreateMockResponse(hasErrors: true);
                }
                throw new Exception($"解析LLM服务响应数据失败：{ex.Message}");
            }
            catch (Exception ex)
            {
                if (_useMockDataOnFailure)
                {
                    System.Diagnostics.Debug.WriteLine($"LLM API调用异常，使用模拟数据: {ex.Message}");
                    return CreateMockResponse(hasErrors: true);
                }
                throw new Exception($"LLM服务检测失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建模拟的健康检查响应（用于测试）
        /// </summary>
        /// <returns>模拟的健康检查结果</returns>
        public LlmHealthCheckResponse CreateMockResponse(bool hasErrors = false)
        {
            return new LlmHealthCheckResponse
            {
                Code = 200,
                Message = "LLM API 可用性检测完成",
                Data = new LlmHealthData
                {
                    TotalApis = 3,
                    AvailableApis = hasErrors ? 2 : 3,
                    UnavailableApis = hasErrors ? 1 : 0,
                    Summary = hasErrors ? "共 3 个 API，2 个可用，1 个不可用" : "共 3 个 API，全部可用",
                    Results = new List<ApiResult>
                    {
                        new ApiResult
                        {
                            Name = "OpenAI GPT-4",
                            BaseUrl = "https://api.openai.com/v1",
                            Model = "gpt-4",
                            Weight = 1.0,
                            Available = true,
                            ResponseTimeMs = 1250,
                            ErrorMessage = null
                        },
                        new ApiResult
                        {
                            Name = "Claude API",
                            BaseUrl = "https://api.anthropic.com/v1",
                            Model = "claude-3",
                            Weight = 0.8,
                            Available = true,
                            ResponseTimeMs = 980,
                            ErrorMessage = null
                        },
                        new ApiResult
                        {
                            Name = "Local LLM",
                            BaseUrl = "http://localhost:8080/v1",
                            Model = "llama2",
                            Weight = 0.5,
                            Available = !hasErrors,
                            ResponseTimeMs = hasErrors ? (int?)null : 1500,
                            ErrorMessage = hasErrors ? "连接失败：无法连接到服务器" : null
                        }
                    }
                }
            };
        }

        /// <summary>
        /// 获取LLM服务状态的简要描述
        /// </summary>
        /// <param name="response">健康检查响应</param>
        /// <returns>状态描述</returns>
        public string GetStatusSummary(LlmHealthCheckResponse response)
        {
            if (response?.Data == null)
                return "无法获取LLM服务状态！";

            var data = response.Data;
            
            if (data.AvailableApis == 0)
                return "⚠️ 所有LLM服务均不可用，请尽快联系管理员修复！";
            
            if (data.UnavailableApis == 0)
                return "✅ 所有LLM服务运行正常！";
            
            return $"⚠️ 检测到{data.TotalApis - data.AvailableApis}个LLM服务不可用，请联系管理员修复！";
        }

        /// <summary>
        /// 检查是否有可用的LLM服务
        /// </summary>
        /// <param name="response">健康检查响应</param>
        /// <returns>是否有可用服务</returns>
        public bool HasAvailableServices(LlmHealthCheckResponse response)
        {
            return response?.Data?.AvailableApis > 0;
        }
    }
}