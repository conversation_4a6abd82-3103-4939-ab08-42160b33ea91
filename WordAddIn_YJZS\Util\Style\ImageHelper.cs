﻿using System;
using Word = Microsoft.Office.Interop.Word;

namespace WordAddIn_YJZS
{
    public class ImageHelper
    {
        // 定义图片位置的枚举
        public enum ImagePosition
        {
            InLine,             // 嵌入式
            TopCenter,          // 上方居中
            TopLeft,            // 上方居左
            TopRight,           // 上方居右
            BottomCenter,       // 下方居中
            BottomLeft,         // 下方居左
            BottomRight,        // 下方居右
            Center,             // 居中
            Left,               // 居左
            Right               // 居右
        }

        // 定义文字环绕方式的枚举
        public enum TextWrapping
        {
            InLine,             // 嵌入式
            Square,             // 四周型
            Tight,              // 紧密型
            Through,            // 穿越型
            TopAndBottom,       // 上下型
            Behind,             // 衬于文字下方
            InFront             // 浮于文字上方
        }

        /// <summary>
        /// 插入图片的增强方法
        /// </summary>
        /// <param name="doc">Word文档对象</param>
        /// <param name="imagePath">图片路径</param>
        /// <param name="widthInCm">宽度(厘米)，可选</param>
        /// <param name="heightInCm">高度(厘米)，可选</param>
        /// <param name="position">图片位置</param>
        /// <param name="wrapping">文字环绕方式</param>
        /// <param name="description">图片说明文字（可选）</param>
        public void InsertImageEnhanced(
            Word.Document doc,
            string imagePath,
            float? widthInCm = null,
            float? heightInCm = null,
            ImagePosition position = ImagePosition.InLine,
            TextWrapping wrapping = TextWrapping.InLine,
            string description = null)
        {
            try
            {
                if (!System.IO.File.Exists(imagePath))
                {
                    throw new System.IO.FileNotFoundException("找不到指定的图片文件", imagePath);
                }

                // 获取当前位置
                Word.Range rng = doc.Paragraphs.Last.Range;
                rng.Collapse(Word.WdCollapseDirection.wdCollapseEnd);

                // 创建新段落并设置居中对齐
                Word.Paragraph imagePara = doc.Paragraphs.Add(rng);
                imagePara.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;

                // 先以内嵌方式插入图片
                Word.InlineShape inlineShape = imagePara.Range.InlineShapes.AddPicture(
                    imagePath,
                    false,  // 不链接到文件
                    true    // 保存到文档
                );

                // 设置图片大小（如果指定了的话）
                if (widthInCm.HasValue)
                    inlineShape.Width = widthInCm.Value * 28.35f;
                if (heightInCm.HasValue)
                    inlineShape.Height = heightInCm.Value * 28.35f;

                // 如果需要其他环绕方式，则转换为浮动图片
                if (wrapping != TextWrapping.InLine)
                {
                    Word.Shape shape = inlineShape.ConvertToShape();

                    // 设置环绕方式
                    switch (wrapping)
                    {
                        case TextWrapping.Square:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapSquare;
                            break;
                        case TextWrapping.Tight:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapTight;
                            break;
                        case TextWrapping.Through:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapThrough;
                            break;
                        case TextWrapping.TopAndBottom:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapTopBottom;
                            break;
                        case TextWrapping.Behind:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapBehind;
                            break;
                        case TextWrapping.InFront:
                            shape.WrapFormat.Type = Word.WdWrapType.wdWrapFront;
                            break;
                    }

                    // 设置位置
                    if (position == ImagePosition.Center)
                    {
                        shape.RelativeHorizontalPosition = Word.WdRelativeHorizontalPosition.wdRelativeHorizontalPositionPage;
                        shape.Left = (float)(doc.PageSetup.PageWidth - shape.Width) / 2;
                    }
                }

                // 插入图片说明
                if (!string.IsNullOrEmpty(description))
                {
                    Word.Paragraph descPara = doc.Paragraphs.Add();
                    //换行符
                    descPara.Range.InsertParagraphAfter();
                    descPara.Range.Text = description;
                    descPara.Range.set_Style("附图标题");
                    descPara.Alignment = Word.WdParagraphAlignment.wdAlignParagraphCenter;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"插入图片时发生错误：{ex.Message}", ex);
            }
        }      
    }
}
