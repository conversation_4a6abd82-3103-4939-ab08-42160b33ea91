﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;

namespace WordAddIn_YJZS
{
    public partial class AgreementForm : Form
    {
        private WebView2 webView;

        public AgreementForm()
        {
            InitializeComponent();
            InitializeWebView2();
        }

        private async void InitializeWebView2()
        {
            try
            {
                webView = new WebView2();
                webView.Dock = DockStyle.Fill;
                this.Controls.Add(webView);

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "agreement.html");
                webView.Source = new Uri(htmlFilePath);

                webView.NavigationCompleted += WebView_NavigationCompleted;

                //MessageBox.Show("用户协议WebView2初始化完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                //MessageBox.Show("用户协议页面加载完成");
            }
            else
            {
                MessageBox.Show("用户协议页面加载失败");
            }
        }
    }
}