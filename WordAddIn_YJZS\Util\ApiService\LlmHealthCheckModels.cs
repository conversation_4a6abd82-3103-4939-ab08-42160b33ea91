using System.Collections.Generic;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public class LlmHealthCheckResponse
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public LlmHealthData Data { get; set; }
    }

    public class LlmHealthData
    {
        [JsonProperty("total_apis")]
        public int TotalApis { get; set; }

        [JsonProperty("available_apis")]
        public int AvailableApis { get; set; }

        [JsonProperty("unavailable_apis")]
        public int UnavailableApis { get; set; }

        [JsonProperty("summary")]
        public string Summary { get; set; }

        [JsonProperty("results")]
        public List<ApiResult> Results { get; set; }
    }

    public class ApiResult
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("base_url")]
        public string BaseUrl { get; set; }

        [JsonProperty("model")]
        public string Model { get; set; }

        [JsonProperty("weight")]
        public double Weight { get; set; }

        [JsonProperty("available")]
        public bool Available { get; set; }

        [JsonProperty("response_time_ms")]
        public int? ResponseTimeMs { get; set; }

        [JsonProperty("error_message")]
        public string ErrorMessage { get; set; }
    }
}