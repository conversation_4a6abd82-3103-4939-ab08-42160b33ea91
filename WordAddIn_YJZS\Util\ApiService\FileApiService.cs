﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace WordAddIn_YJZS
{
    public class FileApiService
    {
        private readonly HttpClient client;
        private string username;
        private string projectName;

        public FileApiService(string username, string projectName)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(projectName))
            {
                throw new ArgumentNullException("用户名或项目名称不能为空！");
            }

            this.username = username;
            this.projectName = projectName;
            this.client = HttpClientManager.Client;
        }

        // 上传文件到服务器
        public async Task<Dictionary<string, object>> UploadFilesAsync(string[] filePaths, long maxFileSizeInBytes, IProgress<int> progress = null)
        {
            if (filePaths == null || filePaths.Length == 0)
            {
                throw new ArgumentException("没有选择文件");
            }

            var url = AllApi.UploadUrl;

            using (var formData = new MultipartFormDataContent())
            {
                // 添加 username 和 project_name 到 formData
                formData.Add(new StringContent(username), "username");
                formData.Add(new StringContent(projectName), "project_name");

                long totalBytes = 0;
                long uploadedBytes = 0;

                foreach (var filePath in filePaths)
                {
                    var fileInfo = new FileInfo(filePath);
                    totalBytes += fileInfo.Length;

                    if (fileInfo.Length > maxFileSizeInBytes)
                    {
                        throw new ArgumentException($"文件 {fileInfo.Name} 超过最大允许大小 {maxFileSizeInBytes / (1024 * 1024)}MB");
                    }
                }

                int fileIndex = 0;
                foreach (var filePath in filePaths)
                {
                    var fileInfo = new FileInfo(filePath);
                    var fileContent = new StreamContent(new FileStream(filePath, FileMode.Open, FileAccess.Read));
                    fileContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    formData.Add(fileContent, "files", fileInfo.Name);

                    fileIndex++;
                    uploadedBytes += fileInfo.Length;
                    progress?.Report((int)(uploadedBytes * 100 / totalBytes));
                }

                var response = await client.PostAsync(url, formData);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var responseObject = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseContent);

                // 使用MessageBox显示responseObject的内容
                //string jsonFormatted = JsonConvert.SerializeObject(responseObject, Formatting.Indented);
                //MessageBox.Show(jsonFormatted, "响应对象内容",MessageBoxButtons.OK, MessageBoxIcon.Information);

                // 返回完整的响应对象，包含文件列表、项目信息、单位列表和章节列表等
                return responseObject;
            }
        }

        // 添加一个新方法来解析上传响应中的文件列表
        public List<string> ExtractUploadedFileNames(Dictionary<string, object> response)
        {
            if (response == null || !response.ContainsKey("data"))
                return new List<string>();

            try
            {
                // 将 data 转换为 JObject
                var data = response["data"] as Newtonsoft.Json.Linq.JObject;
                if (data == null || !data.ContainsKey("files"))
                    return new List<string>();

                // 获取文件列表
                var files = data["files"].ToObject<List<string>>();
                return files ?? new List<string>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析上传文件名时出错: {ex.Message}");
                return new List<string>();
            }
        }

        // 添加一个新方法来解析上传响应中的项目信息
        public Dictionary<string, string> ExtractProjectInfo(Dictionary<string, object> response)
        {
            if (response == null || !response.ContainsKey("data"))
                return new Dictionary<string, string>();

            try
            {
                // 将 data 转换为 JObject
                var data = response["data"] as Newtonsoft.Json.Linq.JObject;
                if (data == null || !data.ContainsKey("project"))
                    return new Dictionary<string, string>();

                // 获取项目信息
                var project = data["project"].ToObject<Dictionary<string, string>>();
                return project ?? new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析项目信息时出错: {ex.Message}");
                return new Dictionary<string, string>();
            }
        }

        // 添加一个新方法来解析上传响应中的单位列表
        public List<string> ExtractUnitList(Dictionary<string, object> response)
        {
            List<string> unitList = new List<string>();
            try
            {
                if (response != null && response.ContainsKey("data") && response["data"] is JObject data)
                {
                    if (data.ContainsKey("unit_list") && data["unit_list"] is JArray unitArray)
                    {
                        foreach (var unit in unitArray)
                        {
                            string unitName = unit.ToString();
                            if (!string.IsNullOrEmpty(unitName))
                            {
                                unitList.Add(unitName);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提取企业列表时出错: {ex.Message}");
            }

            return unitList;
        }

        // 添加一个新方法来解析上传响应中的项目配置
        public Dictionary<string, object> ExtractProjectConfig(Dictionary<string, object> response)
        {
            if (response == null || !response.ContainsKey("data"))
                return new Dictionary<string, object>();

            try
            {
                // 将 data 转换为 JObject
                var data = response["data"] as Newtonsoft.Json.Linq.JObject;
                if (data == null || !data.ContainsKey("project_config"))
                    return new Dictionary<string, object>();

                // 获取项目配置
                var projectConfig = data["project_config"].ToObject<Dictionary<string, object>>();
                return projectConfig ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析项目配置时出错: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        // 添加一个新方法来解析上传响应中的章节列表
        public List<string> ExtractChapterList(Dictionary<string, object> response)
        {
            if (response == null || !response.ContainsKey("data"))
                return new List<string>();

            try
            {
                // 将 data 转换为 JObject
                var data = response["data"] as Newtonsoft.Json.Linq.JObject;
                if (data == null || !data.ContainsKey("chapter_list"))
                    return new List<string>();

                // 获取章节列表
                var chapterList = data["chapter_list"].ToObject<List<string>>();
                return chapterList ?? new List<string>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析章节列表时出错: {ex.Message}");
                return new List<string>();
            }
        }

        // 获取已上传文件列表
        public async Task<List<string>> GetUploadedFilesAsync()
        {
            try
            {
                var url = AllApi.GetFilesUrl;

                var requestBody = new
                {
                    username = this.username,
                    project_name = this.projectName
                };

                var jsonContent = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");

                var response = await client.PostAsync(url, jsonContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"HTTP Error: {response.StatusCode} - {response.ReasonPhrase}\nResponse Content: {responseContent}");
                }

                var apiResponse = JsonConvert.DeserializeObject<ApiResponse<Dictionary<string, List<string>>>>(responseContent);
                if (apiResponse.status_code != 200)
                {
                    throw new HttpRequestException($"API Error: {apiResponse.status_code} - {apiResponse.message}");
                }

                return apiResponse.data["files"];
            }
            catch (Exception ex)
            {
                throw new Exception("获取已上传文件列表时发生意外错误: " + ex.Message, ex);
            }
        }

        // 删除文件
        public async Task<string> DeleteFileAsync(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                throw new ArgumentNullException(nameof(fileName), "文件名不能为空！");
            }

            try
            {
                var url = AllApi.DeleteFileUrl;

                var requestBody = new
                {
                    username = this.username,
                    project_name = this.projectName,
                    filename = fileName
                };

                var jsonContent = new StringContent(JsonConvert.SerializeObject(requestBody), Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Delete, url)
                {
                    Content = jsonContent
                };

                var response = await client.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        throw new FileNotFoundException($"文件 '{fileName}' 不存在。");
                    }
                    throw new HttpRequestException($"HTTP Error: {response.StatusCode} - {response.ReasonPhrase}\nResponse Content: {responseContent}");
                }

                var apiResponse = JsonConvert.DeserializeObject<ApiResponse<Dictionary<string, string>>>(responseContent);
                if (apiResponse.status_code != 200)
                {
                    throw new HttpRequestException($"API Error: {apiResponse.status_code} - {apiResponse.message}");
                }

                return apiResponse.message;
            }
            catch (Exception ex)
            {
                throw new Exception($"删除文件 '{fileName}' 时发生意外错误: {ex.Message}", ex);
            }
        }

        // 删除项目
        public async Task<bool> DeleteProjectAsync()
        {
            try
            {
                var url = AllApi.DelProjectNameUrl;

                // 创建请求内容
                var content = new StringContent(
                    JsonConvert.SerializeObject(new
                    {
                        username = this.username,
                        project_name = this.projectName
                    }),
                    Encoding.UTF8,
                    "application/json"
                );

                // 创建 DELETE 请求
                var request = new HttpRequestMessage
                {
                    Method = HttpMethod.Delete,
                    RequestUri = new Uri(url),
                    Content = content
                };

                // 发送请求
                var response = await client.SendAsync(request);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseContent);

                // 检查响应状态
                if (result.ContainsKey("code") && result["code"].ToString() == "200")
                {
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"删除项目时发生错误：{ex.Message}");
            }
        }
    }
}