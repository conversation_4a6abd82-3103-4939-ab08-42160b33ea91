<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有解助手登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            box-sizing: border-box;
			overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            box-sizing: border-box;
            position: relative;
            height: 100vh;
			padding: 0;
			margin: 0;
        }

        .logo-container {
            text-align: center;
            padding: 0;
			margin: 0; /* 移除外边距 */
            width: 100%; /* 确保容器宽度100% */
            overflow: hidden; /* 防止内容溢出 */
        }

        .logo-container img {
            width: 100%;
            height: auto;
			display: block; /* 移除图片下方的空隙 */
            margin: 0; /* 移除图片的外边距 */
        }

        .right {
            flex: 1;
            padding: 0px 10px 0px 10px;
            text-align: center;
            margin: 0;
            width: 100%;
            box-sizing: border-box; /* 确保padding不会增加宽度 */
        }

            .right h2 {
                margin-bottom: 20px;
                font-size: 24px;
                color: #333;
            }

            .right h3 {
                margin-bottom: 2px;
                font-size: 16px;
                color: #1366BA;
            }

        .tab {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

            .tab button {
                flex: 1;
                padding: 10px;
                cursor: pointer;
                background-color: transparent;
                border: none;
                outline: none;
                position: relative;
                color: #999; /* 修改未激活标签的颜色为灰色 */
            }
		
                .tab button.active {
                    color: #333; /* 激活的标签保持黑色 */
                }


                .tab button.active::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 100%;
                    height: 2px;
                    background-color: #0078d7;
                }

        .form-container {
            display: none;
            margin-bottom: 60px; /* 为agreement留出空间 */
        }

            .form-container.active {
                display: block;
            }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }

        .verification-container {
            display: flex;
            margin-bottom: 10px;
        }

            .verification-container input[type="text"] {
                flex: 1;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px 0 0 5px;
                box-sizing: border-box;
            }

            .verification-container button {
                padding: 10px;
                background-color: #0078d7;
                border: none;
                border-radius: 0 5px 5px 0;
                color: #fff;
                font-size: 16px;
                cursor: pointer;
                box-sizing: border-box;
                height: 37px;
            }

                .verification-container button:hover {
                    background-color: #005a9e;
                }

        button[type="submit"] {
            width: 100%;
            padding: 10px;
            background-color: #0078d7;
            border: none;
            border-radius: 5px;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
        }

            button[type="submit"]:hover {
                background-color: #005a9e;
            }

        .qrcode {
            margin-top: 20px;
        }

            .qrcode img {
                width: 150px;
                height: 150px;
            }

        .agreement {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            margin-top: 20px;
            color: #999;
            width: 80%;
            text-align: center;
        }

        .forgot-password {
            margin-top: 10px;
        }

            .forgot-password a {
                color: #0078d7;
                text-decoration: none;
            }

                .forgot-password a:hover {
                    text-decoration: underline;
                }


        @media (max-width: 500px) {
            .container {
                width: 95%;
                min-height: 550px;
            }

            .left, .right {
                width: 100%;
            }
        }
		
		.modal-overlay {
			display: none;
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			z-index: 999;
		}

		.modal {
			display: none;
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background: white;
			min-width: 300px;
			padding: 25px;
			border-radius: 12px;
			box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
			z-index: 1000;
			text-align: center;
		}

		.modal-content {
			margin-bottom: 20px;
			text-align: center;
		}

		.modal-button {
			background: linear-gradient(45deg, #4a90e2, #63b3ed);
			color: white;
			border: none;
			border-radius: 6px;
			padding: 8px 20px;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.modal-button:hover {
			background: linear-gradient(45deg, #3a80d2, #53a8ff);
		}

		.llm-status {
			margin-top: 10px;
			padding: 0;
			font-size: 14px;
			text-align: center;
			display: none;
			background: none;
			border: none;
		}

		.llm-status.checking {
			color: #1890ff;
			display: block;
		}

		.llm-status.success {
			color: #1890ff;
			display: block;
		}

		.llm-status.warning {
			color: #1890ff;
			display: block;
		}

		.llm-status.error {
			color: #1890ff;
			display: block;
		}
		
		.beta-tag {
			background-color: #ff4d4f;  /* 红色背景 */
			color: white;               /* 白色文字 */
			padding: 2px 6px;          /* 内边距 */
			border-radius: 4px;        /* 圆角 */
			font-size: 14px;           /* 字体大小 */
			margin-left: 5px;          /* 左边距 */
			display: inline-block;     /* 行内块级元素 */
    	}
		
		h3 {
			text-align: center;
			margin: 0;
			padding: 0;
		}

		h3:first-of-type {
			font-size: 14px;
			color: #0078d7;
			font-weight: 500;
			letter-spacing: 1px;
			margin-bottom: 8px;
			display: inline-block;
			position: relative;
		}
    </style>
</head>
<body>
    <div class="container">
		<div class="logo-container">
            <img src="../image/login-logo.png" alt="有解助手 Logo">
        </div>
        <div class="right">
            <h2>有解助手 <span class="beta-tag">Beta</span></h2>
            <h3>一键生成可研报告&nbsp;&nbsp;&nbsp;&nbsp;“有解”助手问世</h3>

            <div class="tab">
				<button class="tablinks active" id="accountLoginTab">账号密码登录</button>
                <button class="tablinks" id="phoneLoginTab">手机号登录</button>                
                <button class="tablinks" id="qrCodeLoginTab">微信扫码登录</button>
            </div>
            <div id="PhoneLogin" class="form-container">
<!--
                <input type="text" id="phone" placeholder="手机号">
                <div class="verification-container">
                    <input type="text" id="verification" placeholder="验证码">
                    <button type="button" onclick="getVerificationCode()">获取验证码</button>
                </div>
                <button type="submit" onclick="loginWithPhone()">登录</button>
-->
            </div>
            <div id="AccountLogin" class="form-container active">
                <input type="text" id="username" placeholder="账号">
                <input type="password" id="password" placeholder="密码">
                <button type="submit" id="loginButton" onclick="loginWithAccount()">登录</button>
                <div id="llmStatus" class="llm-status"></div>
                <!--<div class="forgot-password">
                    <a href="forgot_password.html" target="_blank">忘记密码？</a>
					<a href="register.html" target="_blank">注册</a>
                </div>-->
            </div>
            <div id="QRCodeLogin" class="form-container">
<!--
                <div class="qrcode">
                    <p>微信扫码登录</p>
                    <img src="qrcode.png" alt="QR Code">
                </div>
-->
            </div>
            <div class="agreement">
                <input type="checkbox" id="agreement" checked> 已阅读并同意 <a href="agreement.html" target="_blank">用户许可协议</a>
            </div>
        </div>
    </div>
	<div class="modal-overlay" id="modalOverlay"></div>
	<div class="modal" id="messageModal">
		<div class="modal-content" id="modalContent">
			<p id="modalMessage"></p>
		</div>
		<button class="modal-button" onclick="closeModal()">确定</button>
	</div>
    <script>
		function showModal(message) {
			document.getElementById('modalMessage').textContent = message;
			document.getElementById('modalOverlay').style.display = 'block';
			document.getElementById('messageModal').style.display = 'block';
		}

		function closeModal() {
			document.getElementById('modalOverlay').style.display = 'none';
			document.getElementById('messageModal').style.display = 'none';
		}
		
        document.getElementById('phoneLoginTab').addEventListener('click', function (event) {
            openForm(event, 'PhoneLogin');
        });

        document.getElementById('accountLoginTab').addEventListener('click', function (event) {
            openForm(event, 'AccountLogin');
        });

        document.getElementById('qrCodeLoginTab').addEventListener('click', function (event) {
            openForm(event, 'QRCodeLogin');
        });
        function openForm(evt, formName) {
            var i, formContainer, tablinks;
            formContainer = document.getElementsByClassName("form-container");
            for (i = 0; i < formContainer.length; i++) {
                formContainer[i].style.display = "none";
                formContainer[i].classList.remove("active");
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("active");
            }
            document.getElementById(formName).style.display = "block";
            document.getElementById(formName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }

        function getVerificationCode() {
            const phone = document.getElementById('phone').value;
            const phoneRegex = /^[0-9]{11}$/;
            if (phone) {
                if (!phoneRegex.test(phone)) {
                    showModal('请输入正确的手机号码');
                } else {
                    showModal('验证码已发送到您的手机：' + phone);
                }
            } else {
                showModal('请输入手机号');
            }
        }

        function loginWithPhone() {
            const phone = document.getElementById('phone').value;
            const verificationCode = document.getElementById('verification').value;
            if (document.getElementById('agreement').checked) {
                window.chrome.webview.postMessage(JSON.stringify({ type: 'login', loginType: 'phone', phone: phone, verificationCode: verificationCode }));
            } else {
                showModal('请同意用户许可协议');
            }
        }

        function loginWithAccount() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showModal('用户名和密码不能为空');
                return;
            }

            // 禁用登录按钮，防止重复点击
            const loginButton = document.getElementById('loginButton');
            loginButton.disabled = true;
            loginButton.textContent = '登录中...';

            if (!document.getElementById('agreement').checked) {
                showModal('请同意用户许可协议');
                return;
            }

            // 构造简单的登录消息对象
            const loginData = {
                username: username,
                password: password
            };

            // 直接发送对象，让 WebView2 处理序列化
            window.chrome.webview.postMessage(loginData);

            // 添加消息接收器，处理登录结果
            window.chrome.webview.addEventListener('message', function (event) {
                const response = event.data;
                const llmStatus = document.getElementById('llmStatus');

                if (response.status === 'error') {
                    // 登录失败，显示错误消息并重新启用按钮
                    showModal(response.message);
                    loginButton.disabled = false;
                    loginButton.textContent = '登录';
                    llmStatus.style.display = 'none';
                } else if (response.status === 'success') {
                    // 登录成功，显示成功消息
                    llmStatus.className = 'llm-status checking';
                    llmStatus.textContent = response.message;
                } else if (response.status === 'checking') {
                    // LLM检测中
                    llmStatus.className = 'llm-status checking';
                    llmStatus.textContent = response.message;
                } else if (response.status === 'llm_ok') {
                    // LLM服务正常
                    llmStatus.className = 'llm-status success';
                    llmStatus.textContent = response.message;
                } else if (response.status === 'llm_warning') {
                    // LLM服务部分可用
                    llmStatus.className = 'llm-status warning';
                    llmStatus.textContent = response.message;
                } else if (response.status === 'llm_error') {
                    // LLM服务检测失败
                    llmStatus.className = 'llm-status error';
                    llmStatus.textContent = response.message;
                }
                // 登录成功的情况下，按钮保持禁用状态，因为窗口会关闭
            });
        }
    </script>
</body>
</html>
