using System;
using System.Threading.Tasks;

namespace WordAddIn_YJZS.Test
{
    /// <summary>
    /// LLM健康检查功能的简单测试类
    /// 注意：这是一个演示性的测试类，实际项目中建议使用专业的测试框架
    /// </summary>
    public class LlmHealthCheckTest
    {
        /// <summary>
        /// 测试LLM健康检查服务的基本功能
        /// </summary>
        public static async Task TestLlmHealthCheckService()
        {
            try
            {
                Console.WriteLine("开始测试LLM健康检查服务...");
                
                var service = new LlmHealthCheckService();
                
                // 测试模拟数据生成
                Console.WriteLine("\n1. 测试模拟数据生成（正常情况）:");
                var mockResponse = service.CreateMockResponse(hasErrors: false);
                Console.WriteLine($"   总API数: {mockResponse.Data.TotalApis}");
                Console.WriteLine($"   可用API数: {mockResponse.Data.AvailableApis}");
                Console.WriteLine($"   状态摘要: {service.GetStatusSummary(mockResponse)}");
                
                Console.WriteLine("\n2. 测试模拟数据生成（异常情况）:");
                var mockResponseWithErrors = service.CreateMockResponse(hasErrors: true);
                Console.WriteLine($"   总API数: {mockResponseWithErrors.Data.TotalApis}");
                Console.WriteLine($"   可用API数: {mockResponseWithErrors.Data.AvailableApis}");
                Console.WriteLine($"   不可用API数: {mockResponseWithErrors.Data.UnavailableApis}");
                Console.WriteLine($"   状态摘要: {service.GetStatusSummary(mockResponseWithErrors)}");
                
                Console.WriteLine("\n3. 测试真实API调用:");
                try
                {
                    var realResponse = await service.CheckLlmHealthAsync();
                    Console.WriteLine($"   API调用成功！");
                    Console.WriteLine($"   响应码: {realResponse.Code}");
                    Console.WriteLine($"   消息: {realResponse.Message}");
                    Console.WriteLine($"   状态摘要: {service.GetStatusSummary(realResponse)}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   API调用失败（这是正常的，如果后端服务未启动）: {ex.Message}");
                }
                
                Console.WriteLine("\n4. 测试状态判断功能:");
                Console.WriteLine($"   正常情况有可用服务: {service.HasAvailableServices(mockResponse)}");
                Console.WriteLine($"   异常情况有可用服务: {service.HasAvailableServices(mockResponseWithErrors)}");
                
                Console.WriteLine("\nLLM健康检查服务测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试状态显示窗口的创建
        /// </summary>
        public static void TestLlmStatusForm()
        {
            try
            {
                Console.WriteLine("\n开始测试LLM状态显示窗口...");
                
                var service = new LlmHealthCheckService();
                var mockResponse = service.CreateMockResponse(hasErrors: true);
                
                // 注意：在实际应用中，这需要在UI线程中执行
                // var statusForm = new LlmStatusForm(mockResponse);
                // statusForm.Show(); // 或 ShowDialog()
                
                Console.WriteLine("LLM状态显示窗口测试准备完成（需要在UI环境中实际运行）");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"状态窗口测试中发生错误: {ex.Message}");
            }
        }
    }
}