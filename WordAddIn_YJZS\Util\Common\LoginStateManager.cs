﻿using System;

namespace WordAddIn_YJZS
{
    public class LoginStateManager
    {
        private static LoginStateManager _instance;
        private static readonly object _lock = new object();

        private string _username;
        private string _projectName;
        private string _construction_unit; // 建设单位

        private LoginStateManager() { }

        public static LoginStateManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LoginStateManager();
                        }
                    }
                }
                return _instance;
            }
        }

        public void SetLoginState(string username, string projectName)
        {
            _username = username;
            _projectName = projectName;
        }

        public void SetUnitState(string construction_unit)
        {
            _construction_unit = construction_unit;
        }

        public bool IsLoggedIn => !string.IsNullOrEmpty(_username);

        public string Username => _username;
        public string ProjectName => _projectName;

        public string ConstructionUnit => _construction_unit;

        public void Clear()
        {
            _username = null;
            _projectName = null;
        }
    }
}