﻿using Newtonsoft.Json;
using System;

public class CalculateFormData
{
    // 确保所有属性都有 getter 和 setter
    [<PERSON><PERSON><PERSON>roper<PERSON>("Select1")]
    public string Select1 { get; set; }

    [JsonProperty("Select3")]
    public string Select3 { get; set; }

    [JsonProperty("Select7")]
    public string Select7 { get; set; }

    [<PERSON>sonProperty("Select9")]
    public string Select9 { get; set; }

    [JsonProperty("Input10")]
    public double Input10 { get; set; }

    [JsonProperty("Select11")]
    public string Select11 { get; set; }

    [JsonProperty("Select12")]
    public string Select12 { get; set; }

    // ... 其他属性 ...

    public CalculateFormData()
    {
        // 设置默认值
        Select1 = "";
        Select3 = "";
        Select7 = "";
        Select9 = "";
        Input10 = 0;
        Select11 = "";
        Select12 = "";
    }
}