﻿using Microsoft.Web.WebView2.WinForms;
using Microsoft.Web.WebView2.Core;
using System;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;

namespace WordAddIn_YJZS
{
    public partial class ProjectForm : Form
    {
        public string ProjectName { get; private set; }
        public string SelectedProjectType { get; private set; }
        public string ProjectCycle { get; private set; }  // 添加立项周期属性
        public bool IsConfirmed { get; private set; }
        private string pendingProjectName;  // 添加字段保存待设置的项目名称

        public ProjectForm()
        {
            InitializeComponent();
            InitializeWebView2();
            IsConfirmed = false;
        }

        private async void InitializeWebView2()
        {
            try
            {
                if (webView21.CoreWebView2 != null)
                {
                    // 如果已经初始化完成，直接设置项目名称
                    if (!string.IsNullOrEmpty(pendingProjectName))
                    {
                        SetProjectName(pendingProjectName);
                    }
                    return;
                }

                string userDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "WebView2UserData");
                var environment = await CoreWebView2Environment.CreateAsync(null, userDataFolder);

                await webView21.EnsureCoreWebView2Async(environment);

                string htmlFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "resource/web", "chooseProject.html");
                webView21.Source = new Uri(htmlFilePath);

                // 修改导航完成事件处理
                webView21.NavigationCompleted += (s, e) =>
                {
                    if (e.IsSuccess && !string.IsNullOrEmpty(pendingProjectName))
                    {
                        SetProjectName(pendingProjectName);
                    }
                };

                // 添加消息处理
                webView21.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2时出错: {ex.Message}");
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<dynamic>(e.WebMessageAsJson);
                SelectedProjectType = message.select4Value.ToString();
                ProjectName = message.input1Value.ToString();
                ProjectCycle = message.cycleValue.ToString();  // 获取立项周期
                IsConfirmed = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理消息时出错: {ex.Message}");
            }
        }

        public void SetProjectName(string projectName)
        {
            pendingProjectName = projectName;  // 保存项目名称

            if (webView21?.CoreWebView2 != null)
            {
                // 确保JavaScript字符串中的特殊字符被正确转义
                string escapedProjectName = projectName.Replace("'", "\\'");
                webView21.CoreWebView2.ExecuteScriptAsync($"setProjectName('{escapedProjectName}')");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (!e.IsSuccess)
            {
                MessageBox.Show("页面加载失败");
            }
        }
    }
}