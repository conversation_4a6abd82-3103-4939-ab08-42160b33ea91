﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System;
using WordAddIn_YJZS;
using Newtonsoft.Json.Linq;
using System.Linq;

namespace WordAddIn_YJZS
{
    public class RiskApiService
    {
        private readonly HttpClient client;

        public RiskApiService()
        {
            client = HttpClientManager.Client;
        }

        private void ShowMessage(string message, string caption = "提示",
        MessageBoxButtons buttons = MessageBoxButtons.OK,
        MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            CircleProgree.ShowMessageBox(message, caption, buttons, icon);
        }

        private void ShowErrorMessage(string message)
        {
            CircleProgree.ShowMessageBox(
                message,
                "错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );
        }

        // 通用的异步POST请求方法
        private async Task<List<ContentItem>> PostAndHandleAsync(string url, string username, string project_name, string operationType)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(username))
                {
                    ShowMessage("用户名不能为空");
                    return new List<ContentItem>();
                }
                if (string.IsNullOrWhiteSpace(project_name))
                {
                    ShowMessage("项目名称不能为空");
                    return new List<ContentItem>();
                }

                var requestBody = new { username, project_name };
                var jsonContent = new StringContent(
                    JsonConvert.SerializeObject(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );

                try
                {
                    var response = await client.PostAsync(url, jsonContent);
                    var responseString = await response.Content.ReadAsStringAsync();
                    //ShowMessage($"服务器响应: {responseString}");

                    if (response.IsSuccessStatusCode)
                    {
                        try
                        {
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ContentItem>>>(responseString);
                            if (apiResponse?.data != null)
                            {
                                // 处理图片下载
                                foreach (var item in apiResponse.data)
                                {
                                    // 在处理图片的部分
                                    if (item.style == "-1")
                                    {
                                        Console.WriteLine($"发现图片类型内容: {item.content}"); // 添加日志
                                        string imagePath = await item.DownloadImageAsync();
                                        if (!string.IsNullOrEmpty(imagePath))
                                        {
                                            Console.WriteLine($"图片已下载到: {imagePath}"); // 添加日志
                                            item.content = imagePath;
                                        }
                                        else
                                        {
                                            Console.WriteLine("图片下载失败"); // 添加日志
                                        }
                                    }
                                    // 处理表格数据
                                    else if (item.style == "-3" && item.content != null)
                                    {
                                        try
                                        {
                                            // 如果 content 已经是 JArray
                                            if (item.content is JArray jArray)
                                            {
                                                var tableData = jArray.Select(row =>
                                                    ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                                                ).ToList();
                                                item.content = tableData;
                                            }
                                            // 如果 content 是字符串
                                            else if (item.content is string strContent)
                                            {
                                                try
                                                {
                                                    // 先尝试直接解析为二维字符串数组
                                                    var tableData = JsonConvert.DeserializeObject<List<List<string>>>(strContent);
                                                    if (tableData != null)
                                                    {
                                                        item.content = tableData;
                                                    }
                                                }
                                                catch
                                                {
                                                    // 如果解析失败，尝试解析为 JArray
                                                    var array = JsonConvert.DeserializeObject<JArray>(strContent);
                                                    if (array != null)
                                                    {
                                                        var tableData = array.Select(row =>
                                                            ((JArray)row).Select(cell => cell?.ToString() ?? "").ToList()
                                                        ).ToList();
                                                        item.content = tableData;
                                                    }
                                                }
                                            }
                                            // 如果 content 是其他类型，尝试转换为字符串后处理
                                            else
                                            {
                                                string jsonStr = JsonConvert.SerializeObject(item.content);
                                                try
                                                {
                                                    var tableData = JsonConvert.DeserializeObject<List<List<string>>>(jsonStr);
                                                    if (tableData != null)
                                                    {
                                                        item.content = tableData;
                                                    }
                                                }
                                                catch
                                                {
                                                    Console.WriteLine($"无法将内容转换为表格数据: {jsonStr}");
                                                    item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"处理表格数据时出错: {ex.Message}");
                                            item.content = new List<List<string>> { new List<string> { "处理表格数据时出错" } };
                                        }
                                    }
                                }
                                return apiResponse.data;
                            }
                        }
                        catch (JsonException)
                        {
                            // 如果解析失败，返回原始响应作为内容
                            return new List<ContentItem> { new ContentItem { style = "0", content = responseString } };
                        }
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                    {
                        ShowErrorMessage($"服务器内部错误: {responseString}");
                        return new List<ContentItem>
                        {
                            new ContentItem
                            {
                                style = "0",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            },
                            new ContentItem
                            {
                                style = "-5",
                                content = $"获取{operationType}时发生错误，请确保上传文件存在该内容。"
                            }
                        };
                    }

                    ShowErrorMessage($"请求失败: {response.StatusCode} - {responseString}");
                    return new List<ContentItem>();
                }
                catch (JsonException ex)
                {
                    ShowErrorMessage($"解析响应数据时出错: {ex.Message}");
                    return new List<ContentItem>();
                }
                catch (HttpRequestException ex)
                {
                    ShowErrorMessage($"网络请求错误: {ex.Message}");
                    return new List<ContentItem>();
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"获取{operationType}时发生未知错误: {ex.Message}");
                return new List<ContentItem>();
            }
        }

        //项目风险分析
        public async Task<List<ContentItem>> RiskAnalyseAsync(string username, string project_name)
        {
            return await PostAndHandleAsync(AllApi.RiskAnalyseUrl, username, project_name, "项目风险分析");
        }
    }
}


