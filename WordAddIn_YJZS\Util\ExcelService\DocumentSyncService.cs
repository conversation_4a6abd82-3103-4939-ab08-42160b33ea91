﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text;
using System.Threading;
using System.Reflection;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;
using System.Linq;

namespace WordAddIn_YJZS
{
    public class DocumentSyncService
    {
        // Windows API 声明用于最小化Excel窗口
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        private const int SW_MINIMIZE = 6;

        private readonly LoginStateManager _loginManager;
        private static DocumentSyncService _instance;
        private static readonly object _lock = new object();
        // 添加取消检查相关字段
        private CircleProgree _progressForm;
        private CancellationTokenSource _cancellationTokenSource;

        // 单例模式实现
        public static DocumentSyncService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DocumentSyncService(LoginStateManager.Instance);
                        }
                    }
                }
                return _instance;
            }
        }

        // 最小化Excel窗口的方法
        private async Task MinimizeExcelWindows()
        {
            try
            {
                // 等待一下让Excel完全打开，支持取消
                await DelayWithCancellation(1000);

                // 查找并最小化所有Excel窗口
                EnumWindows(MinimizeExcelWindowCallback, IntPtr.Zero);

                System.Diagnostics.Debug.WriteLine("已尝试最小化Excel窗口");
            }
            catch (Exception)
            {
                // System.Diagnostics.Debug.WriteLine($"最小化Excel窗口时出错: {ex.Message}");
            }
        }

        private bool MinimizeExcelWindowCallback(IntPtr hWnd, IntPtr lParam)
        {
            if (!IsWindowVisible(hWnd)) return true;

            StringBuilder className = new StringBuilder(256);
            GetClassName(hWnd, className, className.Capacity);
            string windowClass = className.ToString();

            StringBuilder windowText = new StringBuilder(256);
            GetWindowText(hWnd, windowText, windowText.Capacity);
            string windowTitle = windowText.ToString();

            // 检查是否是Excel窗口
            if (windowClass.Contains("XLMAIN") || windowClass.Contains("Excel") ||
                windowTitle.Contains("Microsoft Excel") || windowTitle.Contains(".xls") || windowTitle.Contains(".xlsx"))
            {
                ShowWindow(hWnd, SW_MINIMIZE);
                System.Diagnostics.Debug.WriteLine($"最小化Excel窗口: {windowTitle} (类名: {windowClass})");
            }

            return true; // 继续枚举所有窗口
        }

        public DocumentSyncService(LoginStateManager loginManager)
        {
            _loginManager = loginManager;
        }

        // 设置进度表单的方法
        public void SetProgressForm(CircleProgree progressForm)
        {
            _progressForm = progressForm;

            // 创建新的取消令牌源
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        // 检查是否请求取消的方法
        private bool IsCancellationRequested()
        {
            bool progressFormCancelled = _progressForm != null && _progressForm.IsCancellationRequested();
            bool tokenCancelled = _cancellationTokenSource != null && _cancellationTokenSource.Token.IsCancellationRequested;

            // 如果进度表单请求取消，也取消令牌源
            if (progressFormCancelled && _cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
            }

            return progressFormCancelled || tokenCancelled;
        }

        // 支持取消的延迟方法
        private async Task DelayWithCancellation(int milliseconds)
        {
            var checkInterval = Math.Min(200, milliseconds); // 每200ms或更短时间检查一次取消状态
            var remainingTime = milliseconds;

            while (remainingTime > 0)
            {
                if (IsCancellationRequested())
                {
                    return; // 如果请求取消，立即返回
                }

                var delayTime = Math.Min(checkInterval, remainingTime);
                await Task.Delay(delayTime);
                remainingTime -= delayTime;
            }
        }

        // 轮询JSON文件的方法
        private async Task<bool> PollJsonFileAsync(string jsonUrl, string jsonFilePath, TimeSpan timeout, TimeSpan pollInterval)
        {
            var startTime = DateTime.Now;
            var endTime = startTime.Add(timeout);

            System.Diagnostics.Debug.WriteLine($"开始轮询JSON文件: {jsonUrl}，超时时间: {timeout.TotalMinutes}分钟");

            while (DateTime.Now < endTime)
            {
                // 检查是否请求取消
                if (IsCancellationRequested())
                {
                    System.Diagnostics.Debug.WriteLine("轮询被用户取消");
                    return false;
                }

                try
                {
                    using (var client = new HttpClient())
                    {
                        // 设置较短的超时时间
                        client.Timeout = TimeSpan.FromSeconds(10);

                        var cancellationToken = _cancellationTokenSource?.Token ?? CancellationToken.None;
                        var response = await client.GetAsync(jsonUrl, cancellationToken);

                        if (response.IsSuccessStatusCode)
                        {
                            var jsonBytes = await response.Content.ReadAsByteArrayAsync();
                            File.WriteAllBytes(jsonFilePath, jsonBytes);

                            if (File.Exists(jsonFilePath))
                            {
                                System.Diagnostics.Debug.WriteLine($"JSON文件轮询成功，路径: {jsonFilePath}");
                                return true;
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"JSON文件轮询失败，HTTP状态码: {response.StatusCode}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"轮询JSON文件时发生异常: {ex.Message}");
                }

                // 等待下一次轮询
                await DelayWithCancellation((int)pollInterval.TotalMilliseconds);

                // 再次检查是否请求取消
                if (IsCancellationRequested())
                {
                    System.Diagnostics.Debug.WriteLine("轮询在等待间隔中被用户取消");
                    return false;
                }
            }

            System.Diagnostics.Debug.WriteLine($"JSON文件轮询超时，已等待 {timeout.TotalMinutes} 分钟");
            return false;
        }

        // 获取用户设置的保存路径
        private string GetUserSavePath()
        {
            try
            {
                // 读取设置文件
                string settingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings.json");
                if (File.Exists(settingsFilePath))
                {
                    string json = File.ReadAllText(settingsFilePath);
                    var settings = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                    if (settings != null && settings.ContainsKey("savePath") && settings["savePath"] != null)
                    {
                        string customPath = settings["savePath"].ToString();
                        if (!string.IsNullOrEmpty(customPath) && Directory.Exists(customPath))
                        {
                            return customPath;
                        }
                    }
                }
            }
            catch (Exception)
            {
                // System.Diagnostics.Debug.WriteLine($"读取自定义保存路径时出错: {ex.Message}");
                // 出错时不显示消息框，静默回退到默认路径
            }

            // 如果没有找到有效的自定义路径，则使用默认路径
            return Ribbon_YJZS.GetDefaultSavePath();
        }

        public async Task<string> GenerateExcelFromWordAsync(string projectName, bool requiresExcel, bool generateEmptyIfNull = true)
        {
            return await GenerateExcelFromWordAsync(projectName, requiresExcel, generateEmptyIfNull, null);
        }

        public async Task<string> GenerateExcelFromWordAsync(string projectName, bool requiresExcel, bool generateEmptyIfNull, string sharedTimestamp)
        {
            if (!_loginManager.IsLoggedIn)
            {
                throw new Exception("请先登录系统");
            }

            if (!requiresExcel)
            {
                return null;
            }

            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                // 获取必要的数据
                var coverService = new CoverApiService();

                string investmentUnit = LoginStateManager.Instance.ConstructionUnit;

                // 检查是否请求取消
                if (IsCancellationRequested())
                {
                    // 如果请求取消，关闭Excel并返回
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    return null;
                }

                // 按照"、"切分投资单位字符串
                string[] investmentUnits = investmentUnit.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);

                // 如果需要，可以将切分后的数组转换为列表
                List<string> investmentUnitList = investmentUnits.ToList();

                // 获取保存路径 - 使用Ribbon_YJZS中的静态方法
                string savePath = Ribbon_YJZS.GetDefaultSavePath();

                // 获取用户设置的保存路径，如果没有则使用默认路径
                //string savePath = GetUserSavePath();

                // 确保目录存在
                if (!Directory.Exists(savePath))
                {
                    Directory.CreateDirectory(savePath);
                }

                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                if (!File.Exists(templatePath))
                {
                    // 尝试在保存路径下的Style文件夹中查找模板
                    templatePath = Path.Combine(savePath, "Style", "excel_template20250411.xls");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception($"找不到Excel模板文件: {templatePath}");
                    }
                }

                string MultitemplatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "xx项目可行性研究投资估算书-总表（含开发、实施工作量）.xls");
                if (!File.Exists(MultitemplatePath))
                {
                    // 尝试在保存路径下的Style文件夹中查找模板
                    MultitemplatePath = Path.Combine(savePath, "Style", "xx项目可行性研究投资估算书-总表（含开发、实施工作量）.xls");
                    if (!File.Exists(MultitemplatePath))
                    {
                        throw new Exception($"找不到Excel模板文件: {MultitemplatePath}");
                    }
                }

                // 创建 Excel 实例
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.DisplayAlerts = false;

                if (investmentUnitList.Count > 1)
                {
                    // 打开模板文件
                    workbook = excelApp.Workbooks.Open(
                        Filename: MultitemplatePath,
                        ReadOnly: false,
                        UpdateLinks: 0);
                }
                else
                {
                    // 打开模板文件
                    workbook = excelApp.Workbooks.Open(
                        Filename: templatePath,
                        ReadOnly: false,
                        UpdateLinks: 0);
                }

                // 添加标志变量，用于跟踪是否有任何API返回了有效数据
                bool hasValidData = false;

                // 检查是否请求取消
                if (IsCancellationRequested())
                {
                    // 如果请求取消，关闭Excel并返回
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    return null;
                }

                //会签页
                var authorInfo = await coverService.ProcessAuthorAsync(_loginManager.Username);
                if (authorInfo != null && authorInfo.Count > 0)
                {
                    hasValidData = true;
                }
                string editorName = authorInfo.ContainsKey("编  写") ? authorInfo["编  写"] : _loginManager.Username;

                ////封面
                //string processedProjectName = await coverService.ProcessCoverProjectNameAsync(_loginManager.Username, projectName);
                //// 去掉项目名称中的"建设项目可行性研究报告"
                //processedProjectName = processedProjectName + projectName;


                // 定义需要的工作表列表
                Dictionary<string, bool> requiredSheets = new Dictionary<string, bool>
                {
                    { "封面", false },
                    { "会签页", false },
                    { "编制说明", false },
                    { "表一（项目估算汇总表）", false },
                    { "表二（项目分项汇总估算表）", false },
                    { "表三（项目分项估算表）", false },
                    { "表三附1（开发、集成工作量测算）", false },
                    { "表三附2（实施工作量）", false },
                    { "表四（其他费用估算表）", false },
                    { "表五（分阶段投资估算表）", false },
                    { "表六（软件设备汇总表）", false },
                    { "表七（设备服务租赁汇总表）", false },
                    { "表八（需求变更费用对比表）", false },
                    { "计算参数表", false }
                };

                // 检查工作表是否存在，并标记
                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    if (requiredSheets.ContainsKey(sheet.Name))
                    {
                        requiredSheets[sheet.Name] = true;
                    }
                }

                // 创建缺失的工作表
                foreach (var sheetInfo in requiredSheets)
                {
                    if (!sheetInfo.Value)
                    {
                        try
                        {
                            // 添加新工作表
                            Excel.Worksheet newSheet = workbook.Sheets.Add(After: workbook.Sheets[workbook.Sheets.Count]);
                            newSheet.Name = sheetInfo.Key;

                            // 记录创建了新工作表
                            //MessageBox.Show($"创建了缺失的工作表: {sheetInfo.Key}");
                        }
                        catch (Exception)
                        {
                            // MessageBox.Show($"创建工作表 '{sheetInfo.Key}' 时出错: {ex.Message}");
                        }
                    }
                }

                // 定义工作表处理顺序
                string[] sheetProcessOrder = new string[]
                {
                    "会签页",
                    "计算参数表",
                    "表三附1（开发、集成工作量测算）",
                    "表三附2（实施工作量）",
                    "表六（软件设备汇总表）",
                    "表七（设备服务租赁汇总表）",
                    "表八（需求变更费用对比表）",
                    "表三（项目分项估算表）",
                    "表一（项目估算汇总表）",
                    "编制说明",
                    "表二（项目分项汇总估算表）",
                    "表四（其他费用估算表）",
                    "表五（分阶段投资估算表）",
                    "封面"
                };

                // 创建工作表字典，用于快速查找
                Dictionary<string, Excel.Worksheet> worksheets = new Dictionary<string, Excel.Worksheet>();
                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    if (requiredSheets.ContainsKey(sheet.Name))
                    {
                        worksheets[sheet.Name] = sheet;
                    }
                }



                // 按顺序处理各个工作表
                foreach (string sheetName in sheetProcessOrder)
                {
                    // 检查是否请求取消
                    if (IsCancellationRequested())
                    {
                        // 如果请求取消，关闭Excel并返回
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                        }
                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                        }
                        return null;
                    }

                    try
                    {
                        // 查找对应名称的工作表
                        if (!worksheets.TryGetValue(sheetName, out Excel.Worksheet sheet))
                        {
                            continue; // 如果找不到工作表，跳过处理
                        }

                        if (sheetName == "封面")
                        {
                            // 获取封面数据
                            var excelCoverService = new ExcelCoverApiService();
                            var coverResponse = await excelCoverService.GetCoverEstimateCostAsync(_loginManager.Username, projectName);
                            if (coverResponse != null)
                            {
                                hasValidData = true;
                            }

                            FillCoverSheet(sheet, projectName, investmentUnit, coverResponse);
                        }
                        else if (sheetName == "会签页")
                        {
                            FillSignatureSheet(sheet, editorName);
                            hasValidData = true;
                        }
                        else if (sheetName == "编制说明")
                        {
                            // 获取编制说明数据
                            var excelExplainService = new ExcelExplainApiService();
                            var explainResponse = await excelExplainService.GetProjectInvestAsync(_loginManager.Username, projectName);
                            if (explainResponse != null)
                            {
                                hasValidData = true;
                            }
                            FillInstructionSheet(sheet, projectName, investmentUnit, explainResponse);
                        }
                        else if (sheetName == "计算参数表")
                        {
                            if (investmentUnitList.Count > 1)
                            {
                                // 多投资单位情况，使用ParaSheetServices
                                var paraService = new ParaSheetServices();
                                var paraResponse = await paraService.GenerateParaSheetAsync(_loginManager.Username, projectName, 1, 1);
                                if (paraResponse != null && paraService.ValidateResponse(paraResponse))
                                {
                                    hasValidData = true;
                                    var processedContent = paraService.ProcessContentList(paraResponse.ContentList);
                                    FillParaSheetMulti(sheet, processedContent);
                                }
                            }
                            else
                            {
                                // 单投资单位情况，使用ParaSheetService
                                var paraService = new ParaSheetService();
                                var paraResponse = await paraService.GenerateParaSheetAsync(_loginManager.Username, projectName, 1, 1);
                                if (paraResponse != null && paraService.ValidateResponse(paraResponse))
                                {
                                    hasValidData = true;
                                    var processedContent = paraService.ProcessContentList(paraResponse.ContentList);
                                    FillParaSheet(sheet, processedContent);
                                }
                            }
                        }
                        else if (sheetName == "表三附1（开发、集成工作量测算）")
                        {
                            try
                            {
                                // 准备JSON文件路径信息
                                string jsonSavePath = Ribbon_YJZS.GetDefaultSavePath();
                                string jsonFileName = "table3_1.json";
                                string jsonFilePath = Path.Combine(jsonSavePath, jsonFileName);
                                string jsonUrl = $"{AllApi.BaseUrl}/static/{_loginManager.Username}/{projectName}/media/table3_1.json";

                                // 1. 启动API调用任务（不等待完成）
                                var sheet3_1Service = new Sheet3_1ApiService();
                                System.Diagnostics.Debug.WriteLine("表三附1: 启动API调用任务...");
                                var apiTask = Task.Run(async () =>
                                {
                                    try
                                    {
                                        System.Diagnostics.Debug.WriteLine("表三附1: 正在调用API...");
                                        var apiResponse = await sheet3_1Service.GenerateTable3_1Async(_loginManager.Username, projectName, 14, 1);
                                        System.Diagnostics.Debug.WriteLine("表三附1: API调用完成");
                                        return true;
                                    }
                                    catch (Exception apiEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"表三附1: API调用异常: {apiEx.Message}");
                                        return false;
                                    }
                                });

                                // 2. 立即开始轮询JSON文件（与API调用并发执行）
                                System.Diagnostics.Debug.WriteLine("表三附1: 立即开始轮询JSON文件，最多等待120分钟...");
                                bool jsonDownloaded = await PollJsonFileAsync(jsonUrl, jsonFilePath, TimeSpan.FromMinutes(120), TimeSpan.FromSeconds(10));

                                // 3. 处理轮询结果
                                if (IsCancellationRequested())
                                {
                                    System.Diagnostics.Debug.WriteLine("表三附1: 用户取消了操作，跳过轮询，继续执行后续API");
                                }
                                else if (jsonDownloaded)
                                {
                                    System.Diagnostics.Debug.WriteLine($"表三附1: 轮询成功，使用JSON文件填充数据: {jsonFilePath}");
                                    FillSheet3_1FromJson(sheet, jsonFilePath);
                                    hasValidData = true;
                                    System.Diagnostics.Debug.WriteLine("表三附1: 数据填充成功，继续执行后续API");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("表三附1: 轮询超时或失败，跳过此表格，继续执行后续API");
                                    // MessageBox.Show($"无法在指定时间内生成“表三附1”的数据，请稍后重试或联系技术支持。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    // 不设置hasValidData = true，但也不返回null，继续处理后续API
                                }

                                // 4. 等待API任务完成（可选，用于日志记录）
                                try
                                {
                                    bool apiSuccess = await apiTask;
                                    System.Diagnostics.Debug.WriteLine($"表三附1: API任务完成，结果: {(apiSuccess ? "成功" : "失败")}");
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"表三附1: 等待API任务时发生异常: {ex.Message}");
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"处理表三附1时发生严重错误: {ex.Message}");
                                // MessageBox.Show($"处理“表三附1”时发生严重错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                // 发生严重错误时也继续处理后续API，不中断整个流程
                                System.Diagnostics.Debug.WriteLine("表三附1: 发生严重错误，跳过此表格，继续执行后续API");
                            }
                        }
                        else if (sheetName == "表三附2（实施工作量）")
                        {
                            // 获取表三附2数据
                            var sheet3_2Service = new Sheet3_2ApiService();
                            var table3_2Response = await sheet3_2Service.GenerateTable3_2Async(_loginManager.Username, projectName, 2, 1);
                            if (table3_2Response != null && sheet3_2Service.ValidateResponse(table3_2Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet3_2Service.ProcessContentList(table3_2Response.ContentList);

                                if (investmentUnitList.Count > 1)
                                {
                                    FillSheet3_2Muliti(sheet, processedContent);
                                }
                                else
                                {
                                    FillSheet3_2(sheet, processedContent);
                                }
                            }
                        }
                        else if (sheetName == "表六（软件设备汇总表）")
                        {
                            // 获取表六数据
                            var sheet6Service = new Sheet6ApiService();
                            var table6Response = await sheet6Service.GenerateTable6Async(_loginManager.Username, projectName, 4, 1);
                            if (table6Response != null && sheet6Service.ValidateResponse(table6Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet6Service.ProcessContentList(table6Response.ContentList);
                                FillSheet6(sheet, processedContent);
                            }
                        }
                        else if (sheetName == "表七（设备服务租赁汇总表）")
                        {
                            // 获取表七数据
                            var sheet7Service = new Sheet7ApiService();
                            var table7Response = await sheet7Service.GenerateTable7Async(_loginManager.Username, projectName, 4, 1);
                            if (table7Response != null && sheet7Service.ValidateResponse(table7Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet7Service.ProcessContentList(table7Response.ContentList);
                                FillSheet7(sheet, processedContent);
                            }
                        }
                        else if (sheetName == "表八（需求变更费用对比表）")
                        {
                            // 获取表八数据
                            var sheet8Service = new Sheet8ApiService();
                            var table8Response = await sheet8Service.GenerateTable8Async(_loginManager.Username, projectName, 5, 1);
                            if (table8Response != null && sheet8Service.ValidateResponse(table8Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet8Service.ProcessContentList(table8Response.ContentList);
                                FillSheet8(sheet, processedContent);
                            }
                        }
                        else if (sheetName == "表三（项目分项估算表）")
                        {


                            // 获取表三数据
                            var sheet3Service = new Sheet3ApiService();
                            var table3Response = await sheet3Service.GenerateTable3Async(_loginManager.Username, projectName, 5, 1);
                            if (table3Response != null && sheet3Service.ValidateResponse(table3Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet3Service.ProcessContentList(table3Response.ContentList);
                                var markDict = sheet3Service.ProcessMarkList(table3Response.MarkList);
                                var mergedList = sheet3Service.ProcessMergedList(table3Response.MergedList);

                                if (investmentUnitList.Count > 1)
                                {
                                    FillSheet3(sheet, processedContent, markDict, mergedList);

                                    //在这后面添加OtherTable的填充逻辑
                                    try
                                    {
                                        // 创建OtherTableApiService实例
                                        var otherTableService = new OtherTableApiService();

                                        // 根据投资单位列表数量确定需要处理的单位
                                        List<string> unitNames = new List<string>();

                                        if (investmentUnitList.Count == 8)
                                        {
                                            // 8个单位: 公司总部，南网超高压公司，广东电网公司，广西电网公司，云南电网公司，贵州电网公司，海南电网公司，深圳供电局
                                            unitNames = new List<string>
                                            {
                                                "公司总部",
                                                "南网超高压公司",
                                                "广东电网公司",
                                                "广西电网公司",
                                                "云南电网公司",
                                                "贵州电网公司",
                                                "海南电网公司",
                                                "深圳供电局"
                                            };
                                        }
                                        else if (investmentUnitList.Count == 7)
                                        {
                                            // 7个单位: 公司总部，广东电网公司，广西电网公司，云南电网公司，贵州电网公司，海南电网公司，深圳供电局
                                            unitNames = new List<string>
                                            {
                                                "公司总部",
                                                "广东电网公司",
                                                "广西电网公司",
                                                "云南电网公司",
                                                "贵州电网公司",
                                                "海南电网公司",
                                                "深圳供电局"
                                            };
                                        }
                                        else if (investmentUnitList.Count == 6)
                                        {
                                            // 6个单位: 公司总部，广东电网公司，广西电网公司，云南电网公司，贵州电网公司，海南电网公司
                                            unitNames = new List<string>
                                            {
                                                "公司总部",
                                                "广东电网公司",
                                                "广西电网公司",
                                                "云南电网公司",
                                                "贵州电网公司",
                                                "海南电网公司"
                                            };
                                        }
                                        else
                                        {
                                            // 不处理其他情况
                                            return null;
                                        }

                                        // 获取保存路径 - 使用Ribbon_YJZS中的静态方法
                                        string unitSavePath = Ribbon_YJZS.GetDefaultSavePath();

                                        // 确保目录存在
                                        if (!Directory.Exists(unitSavePath))
                                        {
                                            Directory.CreateDirectory(unitSavePath);
                                        }

                                        // 获取表三数据 - 只获取一次API数据，用于所有投资单位
                                        var tableResponse = await otherTableService.GenerateOtherTableAsync(
                                            _loginManager.Username,
                                            projectName,
                                            5, // 开始行为A5
                                            1  // 开始列为A列
                                        );

                                        if (tableResponse == null || !otherTableService.ValidateResponse(tableResponse))
                                        {
                                            System.Diagnostics.Debug.WriteLine("无法获取表三数据，跳过处理多投资单位");
                                            return null;
                                        }

                                        // 处理API返回的数据
                                        var unitProcessedContent = otherTableService.ProcessContentList(tableResponse.ContentList);
                                        var othermarkDict = otherTableService.ProcessMarkList(tableResponse.MarkList);
                                        var othermergedList = otherTableService.ProcessMergedList(tableResponse.MergedList);

                                        // 处理每个投资单位
                                        for (int i = 0; i < unitNames.Count; i++)
                                        {
                                            // 检查是否请求取消
                                            if (IsCancellationRequested())
                                            {
                                                return null;
                                            }

                                            // 确保前一个Excel实例完全释放
                                            GC.Collect();
                                            GC.WaitForPendingFinalizers();
                                            GC.Collect();

                                            // 短暂延迟以确保资源完全释放
                                            await Task.Delay(100);

                                            string unitName = unitNames[i];
                                            Excel.Application unitExcelApp = null;
                                            Excel.Workbook unitWorkbook = null;

                                            try
                                            {
                                                // 模板文件路径
                                                string unitTemplateName = $"{i + 1}、xx项目可行性研究投资估算书（{unitName}）.xls";
                                                string unitTemplatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", unitTemplateName);

                                                if (!File.Exists(unitTemplatePath))
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"模板文件不存在: {unitTemplatePath}");
                                                    continue; // 跳过处理该单位
                                                }

                                                // 创建新的Excel应用程序实例
                                                unitExcelApp = new Excel.Application();
                                                unitExcelApp.Visible = false;
                                                unitExcelApp.DisplayAlerts = false;

                                                // 打开模板文件
                                                unitWorkbook = unitExcelApp.Workbooks.Open(
                                                    Filename: unitTemplatePath,
                                                    ReadOnly: false,
                                                    UpdateLinks: 0);

                                                // 查找表三工作表
                                                Excel.Worksheet sheet3 = null;
                                                foreach (Excel.Worksheet ws in unitWorkbook.Sheets)
                                                {
                                                    if (ws.Name == "表三(项目分项估算表)")
                                                    {
                                                        sheet3 = ws;
                                                        break;
                                                    }
                                                }

                                                if (sheet3 == null)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"模板 {unitTemplateName} 中找不到表三工作表");
                                                    continue; // 跳过处理该单位
                                                }

                                                // 直接填充表三数据，而不是再次调用GenerateMultipleInvestmentSheets
                                                FillMultiInvestmentSheet3(sheet3, unitProcessedContent, othermarkDict, othermergedList);

                                                // 查找表二工作表并修改E5单元格公式
                                                Excel.Worksheet sheet2 = null;
                                                foreach (Excel.Worksheet ws in unitWorkbook.Sheets)
                                                {
                                                    if (ws.Name == "表二(项目分项汇总估算表)")
                                                    {
                                                        sheet2 = ws;
                                                        break;
                                                    }
                                                }

                                                if (sheet2 != null)
                                                {
                                                    try
                                                    {
                                                        // 设置表二的E5单元格公式为='表三(项目分项估算表)'!I11
                                                        sheet2.Range["E5"].Formula = "='表三(项目分项估算表)'!I11";
                                                        System.Diagnostics.Debug.WriteLine($"已修改 {unitName} 的表二E5单元格公式");
                                                    }
                                                    catch (Exception)
                                                    {
                                                        // System.Diagnostics.Debug.WriteLine($"修改 {unitName} 的表二E5单元格时出错: {ex.Message}");
                                                    }
                                                }

                                                // 查找封面工作表并填充项目名称
                                                Excel.Worksheet coverSheet = null;
                                                foreach (Excel.Worksheet ws in unitWorkbook.Sheets)
                                                {
                                                    if (ws.Name == "封面")
                                                    {
                                                        coverSheet = ws;
                                                        break;
                                                    }
                                                }

                                                if (coverSheet != null)
                                                {
                                                    try
                                                    {
                                                        // 在C7单元格填充项目名称
                                                        coverSheet.Range["C7"].Value = projectName;
                                                        System.Diagnostics.Debug.WriteLine($"已填充 {unitName} 的封面C7单元格项目名称");
                                                    }
                                                    catch (Exception)
                                                    {
                                                        // System.Diagnostics.Debug.WriteLine($"填充 {unitName} 的封面C7单元格时出错: {ex.Message}");
                                                    }
                                                }

                                                // 创建项目专用文件夹（使用共享时间戳）
                                                string unitProjectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                                                    ? Ribbon_YJZS.CreateProjectFolder(unitSavePath, projectName)
                                                    : Ribbon_YJZS.CreateProjectFolder(unitSavePath, projectName, sharedTimestamp);

                                                // 保存单位专用文件
                                                string unitFileName = $"{i + 1}、{projectName}建设项目可行性研究投资估算书（{unitName}）.xls";
                                                string unitFilePath = Path.Combine(unitProjectFolderPath, unitFileName);

                                                unitWorkbook.SaveAs(
                                                    Filename: unitFilePath,
                                                    FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                                                    CreateBackup: false);

                                                System.Diagnostics.Debug.WriteLine($"成功保存投资单位表格: {unitFilePath}");
                                            }
                                            catch (Exception)
                                            {
                                                // System.Diagnostics.Debug.WriteLine($"处理投资单位 {unitName} 时出错: {ex.Message}");
                                            }
                                            finally
                                            {
                                                // 立即释放资源
                                                if (unitWorkbook != null)
                                                {
                                                    try
                                                    {
                                                        unitWorkbook.Close(false);
                                                        Marshal.ReleaseComObject(unitWorkbook);
                                                    }
                                                    catch { }
                                                    unitWorkbook = null;
                                                }

                                                if (unitExcelApp != null)
                                                {
                                                    try
                                                    {
                                                        unitExcelApp.Quit();
                                                        Marshal.ReleaseComObject(unitExcelApp);
                                                    }
                                                    catch { }
                                                    unitExcelApp = null;
                                                }

                                                // 强制垃圾回收
                                                GC.Collect();
                                                GC.WaitForPendingFinalizers();
                                                GC.Collect();
                                            }
                                        }
                                    }
                                    catch (Exception)
                                    {
                                        // System.Diagnostics.Debug.WriteLine($"处理多投资单位表格时出错: {ex.Message}");
                                    }
                                }
                                else
                                {
                                    FillSheet3Single(sheet, processedContent, markDict, mergedList);
                                }
                            }
                        }
                        else if (sheetName == "表一（项目估算汇总表）")
                        {


                            // 获取表一数据
                            var sheet1Service = new Sheet1ApiService();
                            var table1Response = await sheet1Service.GenerateTable1Async(_loginManager.Username, projectName, 5, 1);
                            if (table1Response != null && sheet1Service.ValidateResponse(table1Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet1Service.ProcessContentList(table1Response.ContentList);
                                FillSheet1_Optimized(sheet, processedContent);
                            }
                        }
                        else if (sheetName == "表二（项目分项汇总估算表）")
                        {


                            // 获取表二数据
                            var sheet2Service = new Sheet2ApiService();
                            var table2Response = await sheet2Service.GenerateTable2Async(_loginManager.Username, projectName, 5, 1);
                            if (table2Response != null && sheet2Service.ValidateResponse(table2Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet2Service.ProcessContentList(table2Response.ContentList);
                                FillSheet2(sheet, processedContent);
                            }
                        }
                        else if (sheetName == "表四（其他费用估算表）")
                        {


                            // 获取表四数据
                            var sheet4Service = new Sheet4ApiService();
                            var table4Response = await sheet4Service.GenerateTable4Async(_loginManager.Username, projectName, 4, 1);
                            if (table4Response != null && sheet4Service.ValidateResponse(table4Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet4Service.ProcessContentList(table4Response.ContentList);

                                if (investmentUnitList.Count > 1)
                                {
                                    FillSheet4Multi(sheet, processedContent);
                                }
                                else
                                {
                                    FillSheet4(sheet, processedContent);
                                }
                            }
                        }
                        else if (sheetName == "表五（分阶段投资估算表）")
                        {
                            // 获取表五数据
                            var sheet5Service = new Sheet5ApiService();
                            var table5Response = await sheet5Service.GenerateTable5Async(_loginManager.Username, projectName, 5, 1);
                            if (table5Response != null && sheet5Service.ValidateResponse(table5Response))
                            {
                                hasValidData = true;
                                var processedContent = sheet5Service.ProcessContentList(table5Response.ContentList);
                                var markDict = sheet5Service.ProcessMarkList(table5Response.MarkList);
                                var mergedList = sheet5Service.ProcessMergedList(table5Response.MergedList);
                                FillSheet5(sheet, processedContent, markDict, mergedList);
                            }
                        }
                    }
                    catch (Exception)
                    {
                        //MessageBox.Show($"处理工作表 '{sheetName}' 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }



                // 即使没有有效数据，如果generateEmptyIfNull为true，也保存Excel文件
                if (!hasValidData && generateEmptyIfNull)
                {
                    try
                    {
                        // 关闭之前的实例
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 重新创建Excel应用程序和工作簿
                        excelApp = new Excel.Application();
                        excelApp.Visible = false;
                        excelApp.DisplayAlerts = false;

                        string saveNullPath = Ribbon_YJZS.GetDefaultSavePath();
                        string templateNullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                        if (!File.Exists(templatePath))
                        {
                            // 尝试在保存路径下的Style文件夹中查找模板
                            templatePath = Path.Combine(saveNullPath, "Style", "excel_template20250411.xls");
                            if (!File.Exists(templateNullPath))
                            {
                                return null; // 如果模板不存在，则返回null
                            }
                        }

                        //workbook = excelApp.Workbooks.Open(
                        //    Filename: templateNullPath,
                        //    ReadOnly: false,
                        //    UpdateLinks: 0);

                        // 创建项目专用文件夹（使用共享时间戳）
                        string nullProjectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                            ? Ribbon_YJZS.CreateProjectFolder(saveNullPath, projectName)
                            : Ribbon_YJZS.CreateProjectFolder(saveNullPath, projectName, sharedTimestamp);

                        // 保存为新文件
                        string fileNullName = $"{projectName}建设项目可行性研究投资估算书-总表（含开发、实施工作量）.xls";
                        string excelNullPath = Path.Combine(nullProjectFolderPath, fileNullName);

                        workbook.SaveAs(
                            Filename: excelNullPath,
                            FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                            CreateBackup: false);

                        // 添加新代码：打开生成的空Excel文件
                        try
                        {
                            System.Diagnostics.Process.Start(excelNullPath);
                            // 最小化Excel窗口
                            _ = MinimizeExcelWindows();
                        }
                        catch (Exception)
                        {
                            // System.Diagnostics.Debug.WriteLine($"打开空Excel文件时出错: {openEx.Message}");
                        }

                        return excelNullPath;
                    }
                    catch
                    {
                        return null; // 如果再次失败，则返回null
                    }
                    finally
                    {
                        // 确保释放资源
                        if (workbook != null)
                        {
                            try { workbook.Close(false); Marshal.ReleaseComObject(workbook); } catch { }
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            try { excelApp.Quit(); Marshal.ReleaseComObject(excelApp); } catch { }
                            excelApp = null;
                        }
                    }
                }

                // 创建项目专用文件夹（使用共享时间戳）
                string projectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                    ? Ribbon_YJZS.CreateProjectFolder(savePath, projectName)
                    : Ribbon_YJZS.CreateProjectFolder(savePath, projectName, sharedTimestamp);

                // 一切正常保存新文件
                string fileName = $"{projectName}建设项目可行性研究投资估算书-总表（含开发、实施工作量）.xls";
                string excelPath = Path.Combine(projectFolderPath, fileName);

                workbook.SaveAs(
                    Filename: excelPath,
                    FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                    CreateBackup: false);

                // 如果是多投资单位，则更新分摊Excel的数据链接
                if (investmentUnitList.Count > 1)
                {
                    try
                    {
                        // 释放当前工作簿和Excel应用程序，以便可以在UpdateExcelLinksAsync中重新打开
                        if (workbook != null)
                        {
                            workbook.Close(true); // 保存更改
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 强制垃圾回收
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();

                        // 等待一小段时间，确保文件已完全释放，支持取消
                        await DelayWithCancellation(500);

                        // 调用更新链接的方法
                        await UpdateExcelLinksAsync(projectName, excelPath, investmentUnitList);
                    }
                    catch (Exception)
                    {
                        // System.Diagnostics.Debug.WriteLine($"更新分摊Excel链接时出错: {ex.Message}");
                        // 继续返回生成的Excel路径，即使更新链接失败
                    }
                }

                // 添加新代码：打开所有生成的Excel文件
                try
                {
                    // 打开总表
                    System.Diagnostics.Process.Start(excelPath);

                    // 如果是多投资单位，还需要打开各个分摊单位的Excel文件
                    if (investmentUnitList.Count > 1)
                    {
                        // 获取单位名称列表
                        List<string> unitNames = new List<string>();

                        if (investmentUnitList.Count == 8)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "南网超高压公司",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司",
                                "深圳供电局"
                            };
                        }
                        else if (investmentUnitList.Count == 7)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司",
                                "深圳供电局"
                            };
                        }
                        else if (investmentUnitList.Count == 6)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司"
                            };
                        }

                        // 打开每个分摊单位的Excel文件
                        for (int i = 0; i < unitNames.Count; i++)
                        {
                            string unitName = unitNames[i];
                            string unitFileName = $"{i + 1}、{projectName}建设项目可行性研究投资估算书（{unitName}）.xls";
                            string unitFilePath = Path.Combine(projectFolderPath, unitFileName);

                            if (File.Exists(unitFilePath))
                            {
                                System.Diagnostics.Process.Start(unitFilePath);
                                // 短暂延迟，避免同时打开太多文件导致系统卡顿
                                await Task.Delay(200);
                            }
                        }
                    }

                    // 最小化所有打开的Excel窗口
                    _ = MinimizeExcelWindows();
                }
                catch (Exception)
                {
                    // System.Diagnostics.Debug.WriteLine($"打开Excel文件时出错: {ex.Message}");
                    // 即使打开文件失败，也继续返回生成的Excel路径
                }

                return excelPath;
            }
            catch (Exception)
            {
                //MessageBox.Show($"生成Excel文件时出错：{ex.Message}\n\n堆栈跟踪：{ex.StackTrace}");
                // 如果发生异常但generateEmptyIfNull为true，尝试只保存模板
                if (generateEmptyIfNull)
                {
                    try
                    {
                        // 关闭之前的实例
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 重新创建Excel应用程序和工作簿
                        excelApp = new Excel.Application();
                        excelApp.Visible = false;
                        excelApp.DisplayAlerts = false;

                        string savePath = Ribbon_YJZS.GetDefaultSavePath();
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                        if (!File.Exists(templatePath))
                        {
                            // 尝试在保存路径下的Style文件夹中查找模板
                            templatePath = Path.Combine(savePath, "Style", "excel_template20250411.xls");
                            if (!File.Exists(templatePath))
                            {
                                return null; // 如果模板不存在，则返回null
                            }
                        }

                        workbook = excelApp.Workbooks.Open(
                            Filename: templatePath,
                            ReadOnly: false,
                            UpdateLinks: 0);

                        // 创建项目专用文件夹（使用共享时间戳）
                        string projectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                            ? Ribbon_YJZS.CreateProjectFolder(savePath, projectName)
                            : Ribbon_YJZS.CreateProjectFolder(savePath, projectName, sharedTimestamp);

                        // 保存为新文件
                        string fileName = $"{projectName}建设项目可行性研究投资估算书-总表（含开发、实施工作量）.xls";
                        string excelPath = Path.Combine(projectFolderPath, fileName);

                        workbook.SaveAs(
                            Filename: excelPath,
                            FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                            CreateBackup: false);

                        // 添加新代码：打开生成的模板Excel文件
                        try
                        {
                            System.Diagnostics.Process.Start(excelPath);
                            // 最小化Excel窗口
                            _ = MinimizeExcelWindows();
                        }
                        catch (Exception)
                        {
                            // System.Diagnostics.Debug.WriteLine($"打开模板Excel文件时出错: {openEx.Message}");
                        }

                        return excelPath;
                    }
                    catch
                    {
                        return null; // 如果再次失败，则返回null
                    }
                    finally
                    {
                        // 确保释放资源
                        if (workbook != null)
                        {
                            try { workbook.Close(false); Marshal.ReleaseComObject(workbook); } catch { }
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            try { excelApp.Quit(); Marshal.ReleaseComObject(excelApp); } catch { }
                            excelApp = null;
                        }
                    }
                }
                throw;
            }
            finally
            {
                if (workbook != null)
                {
                    try
                    {
                        workbook.Close(SaveChanges: false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    catch { }
                    workbook = null;
                }

                if (excelApp != null)
                {
                    try
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    catch { }
                    excelApp = null;
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        private void FillCoverSheet(Excel.Worksheet coverSheet, string projectName, string investmentUnit, ExcelCoverApiService.ExcelCoverResponse coverResponse = null)
        {
            try
            {
                coverSheet.Range["C7"].Value = projectName;

                // 按照"、"切分投资单位字符串
                string[] investmentUnits = investmentUnit.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);

                // 如果需要，可以将切分后的数组转换为列表
                List<string> investmentUnitList = investmentUnits.ToList();

                if (investmentUnitList.Count >1)
                {
                    investmentUnit = "公司总部";
                }
                coverSheet.Range["C9"].Value = investmentUnit;

                // 填充投资估算数据
                if (coverResponse != null && !string.IsNullOrEmpty(coverResponse.ContentList))
                {
                    // 在C13单元格填充投资估算公式
                    coverSheet.Range["C13"].Formula = coverResponse.ContentList;
                }
            }
            catch (Exception)
            {
                throw new Exception($"填充封面页时发生错误");
            }
        }

        private void FillSignatureSheet(Excel.Worksheet signatureSheet, string editorName)
        {
            try
            {
                signatureSheet.Range["B14"].Value = "编  写：" + editorName;
            }
            catch (Exception)
            {
                throw new Exception($"填充会签页时发生错误");
            }
        }

        private void FillInstructionSheet(Excel.Worksheet sheet, string projectName, string investmentUnit, ExcelExplainApiService.ExcelExplainResponse explainResponse = null)
        {
            try
            {
                // 填充项目概况
                string projectScope = $"    本项目是{projectName}建设项目，参与的单位有{investmentUnit}。";
                sheet.Range["B4"].Value = projectScope;

                // 填充建设规模
                string businessScope = "    业务范围：本项目涉及市场营销业务域，包括营销项目管理。";
                string applicationScope = $"    应用范围：{investmentUnit}";
                string developmentScope = "    开发范围：本项目对项目管理应用进行升级改造，包括前期管理、投资管理、采购管理、实施过程管理、收尾及后评价管理、综合管理功能的升级改造。";

                // 使用单元格合并的方式填充建设规模
                sheet.Range["B7"].Value = businessScope + "\n" + applicationScope + "\n" + developmentScope;

                if (explainResponse != null && !string.IsNullOrEmpty(explainResponse.ContentList))
                {
                    // 在B16单元格填充项目投资说明公式
                    sheet.Range["B16"].Formula = explainResponse.ContentList;
                }
            }
            catch (Exception)
            {
                throw new Exception($"填充编制说明时发生错误");
            }
        }

        private void BatchFillSheetData(Excel.Worksheet sheet, List<List<string>> content, int startRow, int columnsCount)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                return;
            }

            sheet.Application.ScreenUpdating = false;
            sheet.Application.EnableEvents = false;

            try
            {
                int totalRows = content.Count;
                object[,] dataArray = new object[totalRows, columnsCount];

                for (int i = 0; i < totalRows; i++)
                {
                    var currentRow = content[i];
                    for (int j = 0; j < Math.Min(currentRow.Count, columnsCount); j++)
                    {
                        if (!string.IsNullOrWhiteSpace(currentRow[j]))
                        {
                            string cellValue = currentRow[j];
                            if (cellValue.StartsWith("="))
                            {
                                dataArray[i, j] = cellValue;
                            }
                            else if (double.TryParse(cellValue, out double numValue))
                            {
                                dataArray[i, j] = numValue;
                            }
                            else
                            {
                                dataArray[i, j] = cellValue;
                            }
                        }
                    }
                }

                Excel.Range targetRange = sheet.Range[sheet.Cells[startRow, 1], sheet.Cells[startRow + totalRows - 1, columnsCount]];
                targetRange.Value2 = dataArray;

                targetRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                targetRange.Borders.Weight = Excel.XlBorderWeight.xlThin;
                targetRange.WrapText = true;
                targetRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;

                Marshal.ReleaseComObject(targetRange);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] BatchFillSheetData: Filling failed: {ex.Message}");
            }
            finally
            {
                sheet.Application.ScreenUpdating = true;
                sheet.Application.EnableEvents = true;
            }
        }
        private void FillSheet1_Optimized(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("FillSheet1: 无效的工作表或数据为空");
                return;
            }

            try
            {
                int startRow = 5;
                int columnsCount = 12; // A-L
                int rowsToInsert = content.Count;

                // 先清除现有数据区域的内容，避免插入新行时保留原有内容
                Excel.Range clearRange = sheet.Range[sheet.Cells[startRow, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                clearRange.ClearContents();
                Marshal.ReleaseComObject(clearRange);

                // 如果需要插入多行，在第5行后插入所需行数并复制格式
                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0; // 清除剪贴板

                    // 释放COM对象
                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                // 使用新方法批量填充数据
                BatchFillSheetData(sheet, content, startRow, columnsCount);

                // 特殊格式化：处理合计行
                if (content.Count > 0)
                {
                    var lastRowData = content[content.Count - 1];
                    if (lastRowData.Count > 0 && lastRowData[0].Contains("合计"))
                    {
                        int totalExcelRow = startRow + content.Count - 1;
                        Excel.Range totalRowRange = sheet.Range[sheet.Cells[totalExcelRow, 1], sheet.Cells[totalExcelRow, columnsCount]];
                        totalRowRange.Interior.Color = Excel.XlRgbColor.rgbLightGray;
                        totalRowRange.Font.Bold = true;
                        Marshal.ReleaseComObject(totalRowRange);
                    }
                }

                // 调整行高并刷新计算
                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"填充表一数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet2(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 5;
                int columnsCount = 16; // A-P
                int rowsToInsert = content.Count;

                // 先清除现有数据区域的内容，避免插入新行时保留原有内容
                Excel.Range clearRange = sheet.Range[sheet.Cells[startRow, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                clearRange.ClearContents();
                Marshal.ReleaseComObject(clearRange);

                // 如果需要插入多行，在第5行后插入所需行数并复制格式
                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0; // 清除剪贴板

                    // 释放COM对象
                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                // 使用新方法批量填充数据
                BatchFillSheetData(sheet, content, startRow, columnsCount);

                // 特殊格式化：处理合计行
                if (content.Count > 0)
                {
                    var lastRowData = content[content.Count - 1];
                    if (lastRowData.Count > 0 && lastRowData[0].Contains("合计"))
                    {
                        int totalExcelRow = startRow + content.Count - 1;
                        Excel.Range totalRowRange = sheet.Range[sheet.Cells[totalExcelRow, 1], sheet.Cells[totalExcelRow, columnsCount]];
                        totalRowRange.Interior.Color = Excel.XlRgbColor.rgbLightGray;
                        totalRowRange.Font.Bold = true;
                        Marshal.ReleaseComObject(totalRowRange);
                    }
                }

                // 调整行高并刷新计算
                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表二数据时发生错误: {ex.Message}");
            }
        }

        //单建设单位-表三
        private void FillSheet3Single(Excel.Worksheet sheet, List<List<string>> content, Dictionary<(int, int), string> marks = null, List<List<int>> mergedList = null)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 5;
                int columnsCount = 12; // A-L
                int rowsToInsert = content.Count;

                // 判断是否需要插入额外的行
                if (rowsToInsert > 15)
                {
                    int additionalRows = rowsToInsert - 15;
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[additionalRows, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                // 批量填充数据
                BatchFillSheetData(sheet, content, startRow, columnsCount);

                // 处理合并单元格
                if (mergedList != null)
                {
                    foreach (var mergeInfo in mergedList)
                    {
                        if (mergeInfo.Count == 4)
                        {
                            try
                            {
                                Excel.Range mergeRange = sheet.Range[sheet.Cells[mergeInfo[0], mergeInfo[1]], sheet.Cells[mergeInfo[2], mergeInfo[3]]];
                                mergeRange.Merge();
                                mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                                mergeRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                                Marshal.ReleaseComObject(mergeRange);
                            }
                            catch { /*忽略合并错误*/ }
                        }
                    }
                }

                // 处理批注
                if (marks != null)
                {
                    foreach (var mark in marks)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[mark.Key.Item1, mark.Key.Item2];
                            if (cell.Comment != null) cell.Comment.Delete();
                            cell.AddComment(mark.Value);
                            cell.Comment.Visible = false;
                            Marshal.ReleaseComObject(cell);
                        }
                        catch { /*忽略批注错误*/ }
                    }
                }

                // 调整行高并刷新
                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表三数据时发生错误: {ex.Message}");
            }
        }

        //多建设单位-表三
        private void FillSheet3(Excel.Worksheet sheet, List<List<string>> content, Dictionary<(int, int), string> marks = null, List<List<int>> mergedList = null)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 5;
                int columnsCount = 29; // A-AC
                int rowsToInsert = content.Count;

                // 判断是否需要插入额外的行
                if (rowsToInsert > 4)
                {
                    int additionalRows = rowsToInsert - 4;
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[additionalRows, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                // 批量填充数据
                BatchFillSheetData(sheet, content, startRow, columnsCount);

                // 处理合并单元格
                if (mergedList != null)
                {
                    foreach (var mergeInfo in mergedList)
                    {
                        if (mergeInfo.Count == 4)
                        {
                            try
                            {
                                Excel.Range mergeRange = sheet.Range[sheet.Cells[mergeInfo[0], mergeInfo[1]], sheet.Cells[mergeInfo[2], mergeInfo[3]]];
                                mergeRange.Merge();
                                mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                                mergeRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                                Marshal.ReleaseComObject(mergeRange);
                            }
                            catch { /* 忽略合并错误 */ }
                        }
                    }
                }

                // 处理批注
                if (marks != null)
                {
                    foreach (var mark in marks)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[mark.Key.Item1, mark.Key.Item2];
                            if (cell.Comment != null) cell.Comment.Delete();
                            cell.AddComment(mark.Value);
                            cell.Comment.Visible = false;
                            Marshal.ReleaseComObject(cell);
                        }
                        catch { /* 忽略批注错误 */ }
                    }
                }

                // 调整行高并刷新
                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表三数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet3_1(Excel.Worksheet sheet, List<List<string>> content, Dictionary<(int, int), string> marks = null, List<List<int>> mergedList = null, int startRow = 14, int columnsCount = 32)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("[DEBUG] FillSheet3_1: 工作表或内容为空，跳过填充。");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[DEBUG] FillSheet3_1: 开始填充 {content.Count} 行数据。");
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();

            sheet.Application.ScreenUpdating = false;
            sheet.Application.EnableEvents = false;

            try
            {
                int totalRows = content.Count;
                int templateRowIndex = startRow;

                if (totalRows > 1)
                {
                    Excel.Range templateRow = sheet.Rows[templateRowIndex];
                    templateRow.Copy();

                    Excel.Range insertRange = sheet.Rows[templateRowIndex + 1];
                    insertRange.Resize[totalRows - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range newRowsRange = sheet.Range[sheet.Cells[templateRowIndex + 1, 1], sheet.Cells[templateRowIndex + totalRows - 1, columnsCount]];
                    newRowsRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats, Excel.XlPasteSpecialOperation.xlPasteSpecialOperationNone, false, false);
                    sheet.Application.CutCopyMode = 0;
                    Marshal.ReleaseComObject(newRowsRange);
                    Marshal.ReleaseComObject(insertRange);
                    Marshal.ReleaseComObject(templateRow);
                }

                const int chunkSize = 500; // 每次写入500行
                for (int i = 0; i < totalRows; i += chunkSize)
                {
                    var chunkStopwatch = System.Diagnostics.Stopwatch.StartNew();
                    int currentChunkSize = Math.Min(chunkSize, totalRows - i);
                    object[,] dataArray = new object[currentChunkSize, columnsCount];

                    for (int rowIndexInChunk = 0; rowIndexInChunk < currentChunkSize; rowIndexInChunk++)
                    {
                        var currentRow = content[i + rowIndexInChunk];
                        for (int j = 0; j < Math.Min(currentRow.Count, columnsCount); j++)
                        {
                            if (!string.IsNullOrWhiteSpace(currentRow[j]))
                            {
                                string cellValue = currentRow[j];
                                if (cellValue.StartsWith("="))
                                {
                                    dataArray[rowIndexInChunk, j] = cellValue;
                                }
                                else if (double.TryParse(cellValue, out double numValue))
                                {
                                    dataArray[rowIndexInChunk, j] = numValue;
                                }
                                else
                                {
                                    dataArray[rowIndexInChunk, j] = cellValue;
                                }
                            }
                        }
                    }

                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + i, 1], sheet.Cells[startRow + i + currentChunkSize - 1, columnsCount]];
                    targetRange.Value2 = dataArray;
                    chunkStopwatch.Stop();
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] FillSheet3_1: 写入行 {startRow + i} 到 {startRow + i + currentChunkSize - 1}，耗时 {chunkStopwatch.ElapsedMilliseconds} ms。");

                    targetRange.WrapText = true;
                    targetRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                    targetRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                    targetRange.Borders.Weight = Excel.XlBorderWeight.xlThin;
                    Marshal.ReleaseComObject(targetRange);
                }

                if (mergedList != null)
                {
                    foreach (var mergeInfo in mergedList)
                    {
                        if (mergeInfo.Count == 4)
                        {
                            Excel.Range mergeRange = sheet.Range[sheet.Cells[mergeInfo[0], mergeInfo[1]], sheet.Cells[mergeInfo[2], mergeInfo[3]]];
                            mergeRange.Merge();
                            mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                            mergeRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                            Marshal.ReleaseComObject(mergeRange);
                        }
                    }
                }

                if (marks != null)
                {
                    foreach (var mark in marks)
                    {
                        Excel.Range cell = sheet.Cells[mark.Key.Item1, mark.Key.Item2];
                        if (cell.Comment != null) cell.Comment.Delete();
                        cell.AddComment(mark.Value);
                        cell.Comment.Visible = false;
                        Marshal.ReleaseComObject(cell);
                    }
                }

                AdjustRowHeights(sheet, startRow, startRow + totalRows - 1);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ERROR] FillSheet3_1: 填充时发生异常: {ex.Message}");
            }
            finally
            {
                sheet.Application.ScreenUpdating = true;
                sheet.Application.EnableEvents = true;
                totalStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"[DEBUG] FillSheet3_1: 完成所有数据填充，总耗时 {totalStopwatch.ElapsedMilliseconds} ms。");
            }
        }

        private void FillSheet3_2(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 2;
                int columnsCount = 8; // A-H
                int rowsToInsert = content.Count;

                if (rowsToInsert > 1)
                {
                    int additionalRows = rowsToInsert - 1;
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[additionalRows, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表三附2数据时发生错误: {ex.Message}");
            }
        }

        //多企业-表三附二
        private void FillSheet3_2Muliti(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 2;
                int startColumn = 7; // G列
                int columnsCount = 8; // G-N
                int rowsToFill = Math.Min(content.Count, 62); // 最多填充到63行

                // 准备数据数组
                object[,] dataArray = new object[rowsToFill, columnsCount];
                for (int i = 0; i < rowsToFill; i++)
                {
                    var currentRow = content[i];
                    for (int j = 0; j < Math.Min(currentRow.Count, columnsCount); j++)
                    {
                        if (!string.IsNullOrWhiteSpace(currentRow[j]))
                        {
                            string cellValue = currentRow[j];
                            if (cellValue.StartsWith("="))
                            {
                                dataArray[i, j] = cellValue;
                            }
                            else if (double.TryParse(cellValue, out double numValue))
                            {
                                dataArray[i, j] = numValue;
                            }
                            else
                            {
                                dataArray[i, j] = cellValue;
                            }
                        }
                    }
                }

                // 批量写入数据
                Excel.Range targetRange = sheet.Range[sheet.Cells[startRow, startColumn], sheet.Cells[startRow + rowsToFill - 1, startColumn + columnsCount - 1]];
                targetRange.Value2 = dataArray;

                // 设置格式
                targetRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                targetRange.Borders.Weight = Excel.XlBorderWeight.xlThin;
                targetRange.WrapText = true;
                targetRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;

                Marshal.ReleaseComObject(targetRange);

                AdjustRowHeights(sheet, startRow, startRow + rowsToFill - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表三附2（多企业）数据时发生错误: {ex.Message}");
            }
        }

        // 填充多投资单位的表三
        private void FillMultiInvestmentSheet3(Excel.Worksheet sheet, List<List<string>> content, Dictionary<(int, int), string> marks = null, List<List<int>> mergedList = null)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 5;
                int columnsCount = 9; // A-I
                int rowsToInsert = content.Count;

                if (rowsToInsert > 4)
                {
                    int additionalRows = rowsToInsert - 4;
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[additionalRows, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                if (mergedList != null)
                {
                    foreach (var mergeInfo in mergedList)
                    {
                        if (mergeInfo.Count == 4)
                        {
                            try
                            {
                                Excel.Range mergeRange = sheet.Range[sheet.Cells[mergeInfo[0], mergeInfo[1]], sheet.Cells[mergeInfo[2], mergeInfo[3]]];
                                mergeRange.Merge();
                                mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                                mergeRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                                Marshal.ReleaseComObject(mergeRange);
                            }
                            catch { /* 忽略合并错误 */ }
                        }
                    }
                }

                if (marks != null)
                {
                    foreach (var mark in marks)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[mark.Key.Item1, mark.Key.Item2];
                            if (cell.Comment != null) cell.Comment.Delete();
                            cell.AddComment(mark.Value);
                            cell.Comment.Visible = false;
                            Marshal.ReleaseComObject(cell);
                        }
                        catch { /* 忽略批注错误 */ }
                    }
                }

                // 设置第D列（第4列）为小数点后两位格式
                Excel.Range dColumnRange = sheet.Range[sheet.Cells[startRow, 4], sheet.Cells[startRow + rowsToInsert - 1, 4]];
                dColumnRange.NumberFormat = "#,##0.00";;
                Marshal.ReleaseComObject(dColumnRange);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充多投资单位表三数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet4(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 4;
                int columnsCount = 5; // A-E
                int rowsToInsert = content.Count;

                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表四数据时发生错误: {ex.Message}");
            }
        }

        // 填充表四（多投资单位情况）
        private void FillSheet4Multi(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                return;
            }

            try
            {
                int startRow = 4;
                int endRow = 27;
                int column = 5; // E列
                int rowsToFill = Math.Min(content.Count, endRow - startRow + 1);

                object[,] dataArray = new object[rowsToFill, 1];

                for (int i = 0; i < rowsToFill; i++)
                {
                    var rowData = content[i];
                    if (rowData != null && rowData.Count > 0)
                    {
                        // 假设E列的数据在API返回的最后一列
                        string value = rowData.Count > 4 ? rowData[4] : rowData[rowData.Count - 1];
                        if (!string.IsNullOrEmpty(value))
                        {
                            if (value.StartsWith("="))
                            {
                                dataArray[i, 0] = value;
                            }
                            else if (double.TryParse(value, out double numValue))
                            {
                                dataArray[i, 0] = numValue;
                            }
                            else
                            {
                                dataArray[i, 0] = value;
                            }
                        }
                    }
                }

                Excel.Range targetRange = sheet.Range[sheet.Cells[startRow, column], sheet.Cells[startRow + rowsToFill - 1, column]];
                targetRange.Value2 = dataArray;

                targetRange.NumberFormat = "#,##0.00";
                targetRange.Borders.LineStyle = Excel.XlLineStyle.xlContinuous;
                targetRange.Borders.Weight = Excel.XlBorderWeight.xlThin;
                targetRange.WrapText = true;

                Marshal.ReleaseComObject(targetRange);

                AdjustRowHeights(sheet, startRow, startRow + rowsToFill - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"填充表四（多投资单位）时出错: {ex.Message}");
            }
        }

        private void FillSheet5(Excel.Worksheet sheet, List<List<string>> content, Dictionary<(int, int), string> marks = null, List<List<int>> mergedList = null)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                return;
            }

            try
            {
                int startRow = 5;
                int columnsCount = 10; // A-J
                int rowsToInsert = content.Count;

                // 先清除现有数据区域的内容，避免插入新行时保留原有内容
                Excel.Range clearRange = sheet.Range[sheet.Cells[startRow, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                clearRange.ClearContents();
                Marshal.ReleaseComObject(clearRange);

                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                if (mergedList != null)
                {
                    foreach (var mergeInfo in mergedList)
                    {
                        if (mergeInfo.Count == 4)
                        {
                            try
                            {
                                Excel.Range mergeRange = sheet.Range[sheet.Cells[mergeInfo[0], mergeInfo[1]], sheet.Cells[mergeInfo[2], mergeInfo[3]]];
                                mergeRange.Merge();
                                mergeRange.HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                                mergeRange.VerticalAlignment = Excel.XlVAlign.xlVAlignCenter;
                                Marshal.ReleaseComObject(mergeRange);
                            }
                            catch { /* 忽略合并错误 */ }
                        }
                    }
                }

                if (marks != null)
                {
                    foreach (var mark in marks)
                    {
                        try
                        {
                            Excel.Range cell = sheet.Cells[mark.Key.Item1, mark.Key.Item2];
                            if (cell.Comment != null) cell.Comment.Delete();
                            cell.AddComment(mark.Value);
                            cell.Comment.Visible = false;
                            Marshal.ReleaseComObject(cell);
                        }
                        catch { /* 忽略批注错误 */ }
                    }
                }

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"填充表五数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet6(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 4;
                int columnsCount = 11; // A-K
                int rowsToInsert = content.Count;

                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表六数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet7(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 4;
                int columnsCount = 9; // A-I
                int rowsToInsert = content.Count;

                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表七数据时发生错误: {ex.Message}");
            }
        }

        private void FillSheet8(Excel.Worksheet sheet, List<List<string>> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            try
            {
                int startRow = 5;
                int columnsCount = 12; // A-L
                int rowsToInsert = content.Count;

                if (rowsToInsert > 1)
                {
                    Excel.Range insertRange = sheet.Rows[startRow + 1];
                    insertRange.Resize[rowsToInsert - 1, Type.Missing].Insert(Excel.XlInsertShiftDirection.xlShiftDown);

                    Excel.Range sourceRange = sheet.Rows[startRow];
                    Excel.Range targetRange = sheet.Range[sheet.Cells[startRow + 1, 1], sheet.Cells[startRow + rowsToInsert - 1, columnsCount]];
                    sourceRange.Copy();
                    targetRange.PasteSpecial(Excel.XlPasteType.xlPasteFormats);
                    sheet.Application.CutCopyMode = 0;

                    Marshal.ReleaseComObject(targetRange);
                    Marshal.ReleaseComObject(sourceRange);
                    Marshal.ReleaseComObject(insertRange);
                }

                BatchFillSheetData(sheet, content, startRow, columnsCount);

                AdjustRowHeights(sheet, startRow, startRow + rowsToInsert - 1);
                sheet.Calculate();
            }
            catch (Exception ex)
            {
                throw new Exception($"填充表八数据时发生错误: {ex.Message}");
            }
        }

        private void FillParaSheet(Excel.Worksheet sheet, List<(int Row, int Col, string Value)> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                throw new Exception("无效的工作表或数据为空");
            }

            sheet.Application.ScreenUpdating = false;
            sheet.Application.EnableEvents = false;

            try
            {
                // 先清除C31到C39和E31到E39单元格的内容
                Excel.Range rangeCColumn = sheet.Range[sheet.Cells[31, 3], sheet.Cells[39, 3]];
                Excel.Range rangeEColumn = sheet.Range[sheet.Cells[31, 5], sheet.Cells[39, 5]];
                rangeCColumn.ClearContents();
                rangeEColumn.ClearContents();
                Marshal.ReleaseComObject(rangeCColumn);
                Marshal.ReleaseComObject(rangeEColumn);


                // 填充数据
                foreach (var item in content)
                {
                    if (!string.IsNullOrWhiteSpace(item.Value))
                    {
                        sheet.Cells[item.Row, item.Col].Value = item.Value;
                    }
                }

                // 刷新计算
                sheet.Calculate();
            }
            catch (Exception)
            {
                throw new Exception($"填充计算参数表时发生错误");
            }
            finally
            {
                sheet.Application.ScreenUpdating = true;
                sheet.Application.EnableEvents = true;
            }
        }

        // 填充计算参数表（多投资单位情况）
        private void FillParaSheetMulti(Excel.Worksheet sheet, List<(int Row, int Col, string Value)> content)
        {
            if (sheet == null || content == null || content.Count == 0)
            {
                return;
            }

            sheet.Application.ScreenUpdating = false;
            sheet.Application.EnableEvents = false;

            try
            {
                // 遍历内容列表，填充数据
                foreach (var item in content)
                {
                    int row = item.Row;
                    int col = item.Col;
                    string value = item.Value;

                    // 直接填充到指定单元格
                    sheet.Cells[row, col] = value;
                }
            }
            catch (Exception)
            {
                //MessageBox.Show($"填充计算参数表（多投资单位情况）时出错: {ex.Message}");
            }
            finally
            {
                sheet.Application.ScreenUpdating = true;
                sheet.Application.EnableEvents = true;
            }
        }

        // 添加一个新方法，用于更新所有分摊Excel的数据链接
        private Task UpdateExcelLinksAsync(string projectName, string excelPath, List<string> investmentUnitList)
        {
            return Task.Run(() =>
            {
                if (investmentUnitList == null || investmentUnitList.Count <= 1 || string.IsNullOrEmpty(excelPath))
                {
                    return; // 如果不是多投资单位，则不需要更新链接
                }

            Excel.Application totalExcelApp = null;
            Excel.Workbook totalWorkbook = null;
            List<Excel.Application> unitExcelApps = new List<Excel.Application>();
            List<Excel.Workbook> unitWorkbooks = new List<Excel.Workbook>();

            try
            {
                // 获取保存路径
                string savePath = Ribbon_YJZS.GetDefaultSavePath();

                // 确保目录存在
                if (!Directory.Exists(savePath))
                {
                    Directory.CreateDirectory(savePath);
                }

                // 打开总表
                totalExcelApp = new Excel.Application();
                totalExcelApp.Visible = false;
                totalExcelApp.DisplayAlerts = false;

                // 打开总表文件
                totalWorkbook = totalExcelApp.Workbooks.Open(
                    Filename: excelPath,
                    ReadOnly: true, // 只读打开，不修改总表
                    UpdateLinks: 0);

                System.Diagnostics.Debug.WriteLine($"已打开总表: {excelPath}");

                // 获取单位名称列表
                List<string> unitNames = new List<string>();

                if (investmentUnitList.Count == 8)
                {
                    unitNames = new List<string>
                    {
                        "公司总部",
                        "南网超高压公司",
                        "广东电网公司",
                        "广西电网公司",
                        "云南电网公司",
                        "贵州电网公司",
                        "海南电网公司",
                        "深圳供电局"
                    };
                }
                else if (investmentUnitList.Count == 7)
                {
                    unitNames = new List<string>
                    {
                        "公司总部",
                        "广东电网公司",
                        "广西电网公司",
                        "云南电网公司",
                        "贵州电网公司",
                        "海南电网公司",
                        "深圳供电局"
                    };
                }
                else if (investmentUnitList.Count == 6)
                {
                    unitNames = new List<string>
                    {
                        "公司总部",
                        "广东电网公司",
                        "广西电网公司",
                        "云南电网公司",
                        "贵州电网公司",
                        "海南电网公司"
                    };
                }
                else
                {
                    // 不处理其他情况
                    return;
                }

                // 依次打开每个分摊单位的Excel
                for (int i = 0; i < unitNames.Count; i++)
                {
                    // 检查是否请求取消
                    if (IsCancellationRequested())
                    {
                        return;
                    }

                    string unitName = unitNames[i];
                    string unitFileName = $"{i + 1}、{projectName}建设项目可行性研究投资估算书（{unitName}）.xls";
                    // 需要从excelPath中提取项目文件夹路径
                    string projectFolderPath = Path.GetDirectoryName(excelPath);
                    string unitFilePath = Path.Combine(projectFolderPath, unitFileName);

                    if (!File.Exists(unitFilePath))
                    {
                        System.Diagnostics.Debug.WriteLine($"找不到分摊单位Excel文件: {unitFilePath}");
                        continue;
                    }

                    try
                    {
                        // 创建新的Excel应用程序实例
                        Excel.Application unitExcelApp = new Excel.Application();
                        unitExcelApp.Visible = false;
                        unitExcelApp.DisplayAlerts = false;
                        unitExcelApps.Add(unitExcelApp);

                        // 打开分摊单位Excel文件
                        Excel.Workbook unitWorkbook = unitExcelApp.Workbooks.Open(
                            Filename: unitFilePath,
                            ReadOnly: false,
                            UpdateLinks: 1); // 启用链接更新
                        unitWorkbooks.Add(unitWorkbook);

                        System.Diagnostics.Debug.WriteLine($"已打开分摊单位Excel: {unitFilePath}");

                        // 更新工作簿的链接 - 链接到总表
                        try
                        {
                            // 获取外部链接源
                            object linkSourcesObj = unitWorkbook.LinkSources();

                            // 检查是否有链接
                            if (linkSourcesObj != null)
                            {
                                // 转换为数组
                                Array linkSourcesArray = linkSourcesObj as Array;
                                if (linkSourcesArray != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"{unitName} 有 {linkSourcesArray.Length} 个外部链接");

                                    // 在Excel中，数组索引从1开始
                                    for (int j = 1; j <= linkSourcesArray.Length; j++)
                                    {
                                        try
                                        {
                                            // 获取链接源
                                            string linkSource = linkSourcesArray.GetValue(j).ToString();
                                            System.Diagnostics.Debug.WriteLine($"正在更新 {unitName} 的链接: {linkSource}");

                                            // 无论链接是什么，都尝试将其替换为总表的链接
                                            try
                                            {
                                                // 使用完整路径更新链接中的源
                                                System.Diagnostics.Debug.WriteLine($"尝试将链接 {linkSource} 更改为总表链接 {excelPath}");

                                                unitWorkbook.ChangeLink(
                                                    Name: linkSource,
                                                    NewName: excelPath,
                                                    Type: Excel.XlLinkType.xlLinkTypeExcelLinks);

                                                System.Diagnostics.Debug.WriteLine($"已将链接源从 {linkSource} 更改为 {excelPath}");

                                                // 更新已更改的链接
                                                unitWorkbook.UpdateLink(
                                                    Name: excelPath,
                                                    Type: Excel.XlLinkType.xlLinkTypeExcelLinks);

                                                System.Diagnostics.Debug.WriteLine($"成功更新 {unitName} 的链接: {excelPath}");
                                            }
                                            catch (Exception)
                                            {
                                                // System.Diagnostics.Debug.WriteLine($"更改链接 {linkSource} 时出错: {ex.Message}");

                                                // 如果更改链接失败，记录错误但继续处理
                                                try
                                                {
                                                    // 尝试直接更新原始链接
                                                    unitWorkbook.UpdateLink(
                                                        Name: linkSource,
                                                        Type: Excel.XlLinkType.xlLinkTypeExcelLinks);

                                                    System.Diagnostics.Debug.WriteLine($"使用原始链接路径成功更新 {unitName} 的链接: {linkSource}");
                                                }
                                                catch (Exception)
                                                {
                                                    // System.Diagnostics.Debug.WriteLine($"使用原始链接路径更新失败: {innerEx.Message}");
                                                }
                                            }
                                        }
                                        catch (Exception)
                                        {
                                            // System.Diagnostics.Debug.WriteLine($"更新 {unitName} 的第 {j} 个链接时出错: {ex.Message}");
                                        }
                                    }
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"{unitName} 的链接源不是数组类型");
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"{unitName} 没有外部链接");
                            }
                        }
                        catch (Exception)
                        {
                            // System.Diagnostics.Debug.WriteLine($"获取 {unitName} 的链接源时出错: {ex.Message}");
                        }

                        // 保存更新后的文件
                        unitWorkbook.Save();
                        System.Diagnostics.Debug.WriteLine($"已保存更新后的分摊单位Excel: {unitFilePath}");
                    }
                    catch (Exception)
                    {
                        // System.Diagnostics.Debug.WriteLine($"处理分摊单位 {unitName} 的Excel时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception)
            {
                // System.Diagnostics.Debug.WriteLine($"更新Excel链接时出错: {ex.Message}");
            }
            finally
            {
                // 关闭所有打开的工作簿和应用程序实例
                for (int i = 0; i < unitWorkbooks.Count; i++)
                {
                    try
                    {
                        if (unitWorkbooks[i] != null)
                        {
                            unitWorkbooks[i].Close(true); // 保存更改
                            Marshal.ReleaseComObject(unitWorkbooks[i]);
                        }
                    }
                    catch { }
                }

                for (int i = 0; i < unitExcelApps.Count; i++)
                {
                    try
                    {
                        if (unitExcelApps[i] != null)
                        {
                            unitExcelApps[i].Quit();
                            Marshal.ReleaseComObject(unitExcelApps[i]);
                        }
                    }
                    catch { }
                }

                if (totalWorkbook != null)
                {
                    try
                    {
                        totalWorkbook.Close(false); // 不保存更改
                        Marshal.ReleaseComObject(totalWorkbook);
                    }
                    catch { }
                }

                if (totalExcelApp != null)
                {
                    try
                    {
                        totalExcelApp.Quit();
                        Marshal.ReleaseComObject(totalExcelApp);
                    }
                    catch { }
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
            });
       }

        // 辅助方法：根据内容调整行高
        private void AdjustRowHeights(Excel.Worksheet sheet, int startRow, int endRow, int minHeight = 15, int maxHeight = 60)
        {
            try
            {
                for (int i = startRow; i <= endRow; i++)
                {
                    // 获取当前行
                    Excel.Range row = sheet.Rows[i];

                    // 获取行的当前高度
                    double currentHeight = row.RowHeight;

                    // 自动调整行高以适应内容
                    row.AutoFit();

                    // 获取自动调整后的行高
                    double autoFitHeight = row.RowHeight;

                    // 如果自动调整后的行高小于最小高度，则设置为最小高度
                    // 如果超过最大高度，则限制为最大高度
                    if (autoFitHeight < minHeight)
                    {
                        row.RowHeight = minHeight;
                    }
                    else if (autoFitHeight > maxHeight)
                    {
                        row.RowHeight = maxHeight;
                    }
                }
            }
            catch (Exception)
            {
                // System.Diagnostics.Debug.WriteLine($"调整行高时出错: {ex.Message}");
                // 继续执行，不要因为调整行高出错而中断整个过程
            }
        }

        public async Task<string>CheckExcelFromWordAsync(string projectName, bool requiresExcel, bool generateEmptyIfNull = true)
        {
            return await CheckExcelFromWordAsync(projectName, requiresExcel, generateEmptyIfNull, null);
        }

        public async Task<string>CheckExcelFromWordAsync(string projectName, bool requiresExcel, bool generateEmptyIfNull, string sharedTimestamp)
        {
            if (!_loginManager.IsLoggedIn)
            {
                throw new Exception("请先登录系统");
            }

            if (!requiresExcel)
            {
                return null;
            }

            Excel.Application excelApp = null;
            Excel.Workbook workbook = null;

            try
            {
                // 获取必要的数据
                var coverService = new CoverApiService();

                string investmentUnit = await coverService.GetInvestmentUnitAsync(projectName);

                // 检查是否请求取消
                if (IsCancellationRequested())
                {
                    // 如果请求取消，关闭Excel并返回
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    return null;
                }

                // 按照"、"切分投资单位字符串
                string[] investmentUnits = investmentUnit.Split(new string[] { "、" }, StringSplitOptions.RemoveEmptyEntries);

                // 如果需要，可以将切分后的数组转换为列表
                List<string> investmentUnitList = investmentUnits.ToList();

                // 获取保存路径 - 使用Ribbon_YJZS中的静态方法
                string savePath = Ribbon_YJZS.GetDefaultSavePath();

                // 获取用户设置的保存路径，如果没有则使用默认路径
                //string savePath = GetUserSavePath();

                // 确保目录存在
                if (!Directory.Exists(savePath))
                {
                    Directory.CreateDirectory(savePath);
                }

                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                if (!File.Exists(templatePath))
                {
                    // 尝试在保存路径下的Style文件夹中查找模板
                    templatePath = Path.Combine(savePath, "Style", "excel_template20250411.xls");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception($"找不到Excel模板文件: {templatePath}");
                    }
                }

                string MultitemplatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "xx项目可行性研究投资估算书-总表（含开发、实施工作量）.xls");
                if (!File.Exists(MultitemplatePath))
                {
                    // 尝试在保存路径下的Style文件夹中查找模板
                    MultitemplatePath = Path.Combine(savePath, "Style", "xx项目可行性研究投资估算书-总表（含开发、实施工作量）.xls");
                    if (!File.Exists(MultitemplatePath))
                    {
                        throw new Exception($"找不到Excel模板文件: {MultitemplatePath}");
                    }
                }

                // 创建 Excel 实例
                excelApp = new Excel.Application();
                excelApp.Visible = false;
                excelApp.DisplayAlerts = false;

                if (investmentUnitList.Count > 1)
                {
                    // 打开模板文件
                    workbook = excelApp.Workbooks.Open(
                        Filename: MultitemplatePath,
                        ReadOnly: false,
                        UpdateLinks: 0);
                }
                else
                {
                    // 打开模板文件
                    workbook = excelApp.Workbooks.Open(
                        Filename: templatePath,
                        ReadOnly: false,
                        UpdateLinks: 0);
                }

                // 添加标志变量，用于跟踪是否有任何API返回了有效数据
                bool hasValidData = false;

                // 检查是否请求取消
                if (IsCancellationRequested())
                {
                    // 如果请求取消，关闭Excel并返回
                    if (workbook != null)
                    {
                        workbook.Close(false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    if (excelApp != null)
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    return null;
                }

                //会签页
                var authorInfo = await coverService.ProcessAuthorAsync(_loginManager.Username);
                if (authorInfo != null && authorInfo.Count > 0)
                {
                    hasValidData = true;
                }
                string editorName = authorInfo.ContainsKey("编  写") ? authorInfo["编  写"] : _loginManager.Username;

                ////封面
                //string processedProjectName = await coverService.ProcessCoverProjectNameAsync(_loginManager.Username, projectName);
                //// 去掉项目名称中的"建设项目可行性研究报告"
                //processedProjectName = processedProjectName + projectName;


                // 定义需要的工作表列表
                Dictionary<string, bool> requiredSheets = new Dictionary<string, bool>
                {
                    { "封面", false },
                    { "会签页", false },
                    { "编制说明", false },
                    { "表三附1（开发、集成工作量测算）", false },
                };

                // 检查工作表是否存在，并标记
                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    if (requiredSheets.ContainsKey(sheet.Name))
                    {
                        requiredSheets[sheet.Name] = true;
                    }
                }

                // 创建缺失的工作表
                foreach (var sheetInfo in requiredSheets)
                {
                    if (!sheetInfo.Value)
                    {
                        try
                        {
                            // 添加新工作表
                            Excel.Worksheet newSheet = workbook.Sheets.Add(After: workbook.Sheets[workbook.Sheets.Count]);
                            newSheet.Name = sheetInfo.Key;

                            // 记录创建了新工作表
                            //MessageBox.Show($"创建了缺失的工作表: {sheetInfo.Key}");
                        }
                        catch (Exception)
                        {
                            // MessageBox.Show($"创建工作表 '{sheetInfo.Key}' 时出错: {ex.Message}");
                        }
                    }
                }

                // 定义工作表处理顺序
                string[] sheetProcessOrder = new string[]
                {
                    "会签页",
                    "表三附1（开发、集成工作量测算）",
                    "编制说明",
                    "封面"
                };

                // 创建工作表字典，用于快速查找
                Dictionary<string, Excel.Worksheet> worksheets = new Dictionary<string, Excel.Worksheet>();
                foreach (Excel.Worksheet sheet in workbook.Sheets)
                {
                    if (requiredSheets.ContainsKey(sheet.Name))
                    {
                        worksheets[sheet.Name] = sheet;
                    }
                }

                // 按顺序处理各个工作表
                foreach (string sheetName in sheetProcessOrder)
                {
                    // 检查是否请求取消
                    if (IsCancellationRequested())
                    {
                        // 如果请求取消，关闭Excel并返回
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                        }
                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                        }
                        return null;
                    }

                    try
                    {
                        // 查找对应名称的工作表
                        if (!worksheets.TryGetValue(sheetName, out Excel.Worksheet sheet))
                        {
                            continue; // 如果找不到工作表，跳过处理
                        }

                        if (sheetName == "封面")
                        {
                            // 获取封面数据
                            var excelCoverService = new ExcelCoverApiService();
                            var coverResponse = await excelCoverService.GetCoverEstimateCostAsync(_loginManager.Username, projectName);
                            if (coverResponse != null)
                            {
                                hasValidData = true;
                            }

                            FillCoverSheet(sheet, projectName, investmentUnit, coverResponse);
                        }
                        else if (sheetName == "会签页")
                        {
                            FillSignatureSheet(sheet, editorName);
                            hasValidData = true;
                        }
                        else if (sheetName == "编制说明")
                        {
                            // 获取编制说明数据
                            var excelExplainService = new ExcelExplainApiService();
                            var explainResponse = await excelExplainService.GetProjectInvestAsync(_loginManager.Username, projectName);
                            if (explainResponse != null)
                            {
                                hasValidData = true;
                            }
                            FillInstructionSheet(sheet, projectName, investmentUnit, explainResponse);
                        }
                        else if (sheetName == "表三附1（开发、集成工作量测算）")
                        {
                            try
                            {
                                var checkExcelService = new CheckExcelApiService();
                                var checktable3_1Response = await checkExcelService.CheckExcelAsync(_loginManager.Username, projectName, 14, 1);

                                if (checktable3_1Response != null && checkExcelService.ValidateResponse(checktable3_1Response))
                                {
                                    hasValidData = true;
                                    System.Diagnostics.Debug.WriteLine("表三附1 (检查模式): 已从API获取数据，开始填充");
                                    var processedContent = checkExcelService.ProcessContentList(checktable3_1Response.ContentList);
                                    var marks = checkExcelService.ProcessMarkList(checktable3_1Response.MarkList);
                                    var merged = checkExcelService.ProcessMergedList(checktable3_1Response.MergedList);
                                    FillSheet3_1(sheet, processedContent, marks, merged, 14, 7);
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("表三附1 (检查模式): 从API获取数据失败或数据无效。");
                                }
                            }
                            catch (Exception)
                            {
                                // System.Diagnostics.Debug.WriteLine($"处理表三附1时出错: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception)
                    {
                        //MessageBox.Show($"处理工作表 '{sheetName}' 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                // 即使没有有效数据，如果generateEmptyIfNull为true，也保存Excel文件
                if (!hasValidData && generateEmptyIfNull)
                {
                    try
                    {
                        // 关闭之前的实例
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 重新创建Excel应用程序和工作簿
                        excelApp = new Excel.Application();
                        excelApp.Visible = false;
                        excelApp.DisplayAlerts = false;

                        string saveNullPath = Ribbon_YJZS.GetDefaultSavePath();
                        string templateNullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                        if (!File.Exists(templatePath))
                        {
                            // 尝试在保存路径下的Style文件夹中查找模板
                            templatePath = Path.Combine(saveNullPath, "Style", "excel_template20250411.xls");
                            if (!File.Exists(templateNullPath))
                            {
                                return null; // 如果模板不存在，则返回null
                            }
                        }

                        //workbook = excelApp.Workbooks.Open(
                        //    Filename: templateNullPath,
                        //    ReadOnly: false,
                        //    UpdateLinks: 0);

                        // 创建项目专用文件夹（使用共享时间戳）
                        string checkNullProjectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                            ? Ribbon_YJZS.CreateProjectFolder(saveNullPath, projectName)
                            : Ribbon_YJZS.CreateProjectFolder(saveNullPath, projectName, sharedTimestamp);

                        // 保存为新文件
                        string fileNullName = $"{projectName}建设项目可行性研究投资估算书-总表（含开发、实施工作量）.xls";
                        string excelNullPath = Path.Combine(checkNullProjectFolderPath, fileNullName);

                        workbook.SaveAs(
                            Filename: excelNullPath,
                            FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                            CreateBackup: false);

                        // 添加新代码：打开生成的空Excel文件
                        try
                        {
                            System.Diagnostics.Process.Start(excelNullPath);
                            // 最小化Excel窗口
                            _ = MinimizeExcelWindows();
                        }
                        catch (Exception)
                        {
                            // System.Diagnostics.Debug.WriteLine($"打开空Excel文件时出错: {openEx.Message}");
                        }

                        return excelNullPath;
                    }
                    catch
                    {
                        return null; // 如果再次失败，则返回null
                    }
                    finally
                    {
                        // 确保释放资源
                        if (workbook != null)
                        {
                            try { workbook.Close(false); Marshal.ReleaseComObject(workbook); } catch { }
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            try { excelApp.Quit(); Marshal.ReleaseComObject(excelApp); } catch { }
                            excelApp = null;
                        }
                    }
                }

                // 创建项目专用文件夹（使用共享时间戳）
                string projectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                    ? Ribbon_YJZS.CreateProjectFolder(savePath, projectName)
                    : Ribbon_YJZS.CreateProjectFolder(savePath, projectName, sharedTimestamp);

                // 一切正常保存新文件
                string fileName = $"{projectName}建设项目可行性研究投资估算书检查-总表（含开发、实施工作量）.xls";
                string excelPath = Path.Combine(projectFolderPath, fileName);

                workbook.SaveAs(
                    Filename: excelPath,
                    FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                    CreateBackup: false);

                // 如果是多投资单位，则更新分摊Excel的数据链接
                if (investmentUnitList.Count > 1)
                {
                    try
                    {
                        // 释放当前工作簿和Excel应用程序，以便可以在UpdateExcelLinksAsync中重新打开
                        if (workbook != null)
                        {
                            workbook.Close(true); // 保存更改
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 强制垃圾回收
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();

                        // 等待一小段时间，确保文件已完全释放，支持取消
                        await DelayWithCancellation(500);

                        // 调用更新链接的方法
                        await UpdateExcelLinksAsync(projectName, excelPath, investmentUnitList);
                    }
                    catch (Exception)
                    {
                        // System.Diagnostics.Debug.WriteLine($"更新分摊Excel链接时出错: {ex.Message}");
                        // 继续返回生成的Excel路径，即使更新链接失败
                    }
                }

                // 添加新代码：打开所有生成的Excel文件
                try
                {
                    // 打开总表
                    System.Diagnostics.Process.Start(excelPath);

                    // 如果是多投资单位，还需要打开各个分摊单位的Excel文件
                    if (investmentUnitList.Count > 1)
                    {
                        // 获取单位名称列表
                        List<string> unitNames = new List<string>();

                        if (investmentUnitList.Count == 8)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "南网超高压公司",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司",
                                "深圳供电局"
                            };
                        }
                        else if (investmentUnitList.Count == 7)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司",
                                "深圳供电局"
                            };
                        }
                        else if (investmentUnitList.Count == 6)
                        {
                            unitNames = new List<string>
                            {
                                "公司总部",
                                "广东电网公司",
                                "广西电网公司",
                                "云南电网公司",
                                "贵州电网公司",
                                "海南电网公司"
                            };
                        }

                        // 打开每个分摊单位的Excel文件
                        for (int i = 0; i < unitNames.Count; i++)
                        {
                            string unitName = unitNames[i];
                            string unitFileName = $"{i + 1}、{projectName}建设项目可行性研究投资估算书（{unitName}）.xls";
                            string unitFilePath = Path.Combine(projectFolderPath, unitFileName);

                            if (File.Exists(unitFilePath))
                            {
                                System.Diagnostics.Process.Start(unitFilePath);
                                // 短暂延迟，避免同时打开太多文件导致系统卡顿
                                await Task.Delay(200);
                            }
                        }
                    }

                    // 最小化所有打开的Excel窗口
                    _ = MinimizeExcelWindows();
                }
                catch (Exception)
                {
                    // System.Diagnostics.Debug.WriteLine($"打开Excel文件时出错: {ex.Message}");
                    // 即使打开文件失败，也继续返回生成的Excel路径
                }

                return excelPath;
            }
            catch (Exception)
            {
                //MessageBox.Show($"生成Excel文件时出错：{ex.Message}\n\n堆栈跟踪：{ex.StackTrace}");
                // 如果发生异常但generateEmptyIfNull为true，尝试只保存模板
                if (generateEmptyIfNull)
                {
                    try
                    {
                        // 关闭之前的实例
                        if (workbook != null)
                        {
                            workbook.Close(false);
                            Marshal.ReleaseComObject(workbook);
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            excelApp.Quit();
                            Marshal.ReleaseComObject(excelApp);
                            excelApp = null;
                        }

                        // 重新创建Excel应用程序和工作簿
                        excelApp = new Excel.Application();
                        excelApp.Visible = false;
                        excelApp.DisplayAlerts = false;

                        string savePath = Ribbon_YJZS.GetDefaultSavePath();
                        string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Style", "excel_template20250411.xls");
                        if (!File.Exists(templatePath))
                        {
                            // 尝试在保存路径下的Style文件夹中查找模板
                            templatePath = Path.Combine(savePath, "Style", "excel_template20250411.xls");
                            if (!File.Exists(templatePath))
                            {
                                return null; // 如果模板不存在，则返回null
                            }
                        }

                        workbook = excelApp.Workbooks.Open(
                            Filename: templatePath,
                            ReadOnly: false,
                            UpdateLinks: 0);

                        // 创建项目专用文件夹（使用共享时间戳）
                        string projectFolderPath = string.IsNullOrEmpty(sharedTimestamp)
                            ? Ribbon_YJZS.CreateProjectFolder(savePath, projectName)
                            : Ribbon_YJZS.CreateProjectFolder(savePath, projectName, sharedTimestamp);

                        // 保存为新文件
                        string fileName = $"{projectName}建设项目可行性研究投资估算书检查-总表（含开发、实施工作量）.xls";
                        string excelPath = Path.Combine(projectFolderPath, fileName);

                        workbook.SaveAs(
                            Filename: excelPath,
                            FileFormat: Excel.XlFileFormat.xlWorkbookNormal,
                            CreateBackup: false);

                        // 添加新代码：打开生成的模板Excel文件
                        try
                        {
                            System.Diagnostics.Process.Start(excelPath);
                            // 最小化Excel窗口
                            _ = MinimizeExcelWindows();
                        }
                        catch (Exception)
                        {
                            // System.Diagnostics.Debug.WriteLine($"打开模板Excel文件时出错: {openEx.Message}");
                        }

                        return excelPath;
                    }
                    catch
                    {
                        return null; // 如果再次失败，则返回null
                    }
                    finally
                    {
                        // 确保释放资源
                        if (workbook != null)
                        {
                            try { workbook.Close(false); Marshal.ReleaseComObject(workbook); } catch { }
                            workbook = null;
                        }

                        if (excelApp != null)
                        {
                            try { excelApp.Quit(); Marshal.ReleaseComObject(excelApp); } catch { }
                            excelApp = null;
                        }
                    }
                }
                throw;
            }
            finally
            {
                if (workbook != null)
                {
                    try
                    {
                        workbook.Close(SaveChanges: false);
                        Marshal.ReleaseComObject(workbook);
                    }
                    catch { }
                    workbook = null;
                }

                if (excelApp != null)
                {
                    try
                    {
                        excelApp.Quit();
                        Marshal.ReleaseComObject(excelApp);
                    }
                    catch { }
                    excelApp = null;
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        // 添加新方法: 从JSON文件加载表三附1数据并填充
        private void FillSheet3_1FromJson(Excel.Worksheet sheet, string jsonFilePath, int startRow = 14, int columnsCount = 32)
        {
            if (sheet == null || string.IsNullOrEmpty(jsonFilePath) || !File.Exists(jsonFilePath))
            {
                System.Diagnostics.Debug.WriteLine($"FillSheet3_1FromJson: 工作表为空或JSON文件不存在: {jsonFilePath}");
                return;
            }

            try
            {
                // 读取JSON文件内容
                string jsonContent = File.ReadAllText(jsonFilePath);
                System.Diagnostics.Debug.WriteLine($"从JSON文件读取内容成功，开始解析...");

                // 解析JSON数据
                dynamic jsonData = JsonConvert.DeserializeObject(jsonContent);
                if (jsonData == null)
                {
                    System.Diagnostics.Debug.WriteLine("FillSheet3_1FromJson: JSON数据解析失败");
                    return;
                }

                // 检查响应状态
                if (jsonData.status_code != null && jsonData.status_code != 200)
                {
                    System.Diagnostics.Debug.WriteLine($"FillSheet3_1FromJson: 服务器返回错误状态码: {jsonData.status_code}");
                    return;
                }

                // 获取data部分
                dynamic data = jsonData.data;
                if (data == null)
                {
                    System.Diagnostics.Debug.WriteLine("FillSheet3_1FromJson: JSON数据中没有找到data字段");
                    return;
                }

                // 解析内容
                List<List<string>> contentList = new List<List<string>>();
                Dictionary<(int, int), string> markDict = new Dictionary<(int, int), string>();
                List<List<int>> mergedList = new List<List<int>>();

                try
                {
                    // 解析content_list
                    if (data.content_list != null)
                    {
                        var rawContentList = JsonConvert.DeserializeObject<List<List<object>>>(data.content_list.ToString());
                        contentList = ConvertContentList(rawContentList);
                        System.Diagnostics.Debug.WriteLine($"成功解析content_list，共{contentList.Count}行数据");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("JSON数据中没有找到content_list字段");
                        return;
                    }

                    // 解析mark_list
                    if (data.mark_list != null)
                    {
                        var markList = JsonConvert.DeserializeObject<List<dynamic>>(data.mark_list.ToString());
                        foreach (var mark in markList)
                        {
                            int row = (int)mark.row;
                            int col = (int)mark.col;
                            string content = mark.content.ToString();
                            markDict.Add((row, col), content);
                        }
                        System.Diagnostics.Debug.WriteLine($"成功解析mark_list，共{markDict.Count}个批注");
                    }

                    // 解析merged_list
                    if (data.merged_list != null)
                    {
                        mergedList = JsonConvert.DeserializeObject<List<List<int>>>(data.merged_list.ToString());
                        System.Diagnostics.Debug.WriteLine($"成功解析merged_list，共{mergedList.Count}个合并区域");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"解析JSON数据时出错: {ex.Message}");
                    return;
                }

                if (contentList == null || contentList.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"表三附1: 从JSON文件解析出的内容列表为空");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"表三附1: 开始使用FillSheet3_1函数填充数据，共{contentList.Count}行");

                // 直接调用现有的FillSheet3_1函数，复用其完整的逻辑
                FillSheet3_1(sheet, contentList, markDict, mergedList, startRow, columnsCount);

                System.Diagnostics.Debug.WriteLine($"表三附1: 已从JSON文件 {jsonFilePath} 成功加载并填充数据");


            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从JSON文件填充表三附1时出错: {ex.Message}");
            }
        }

        // 辅助方法：将object列表转换为string列表
        private List<List<string>> ConvertContentList(List<List<object>> contentList)
        {
            if (contentList == null)
                return new List<List<string>>();

            List<List<string>> result = new List<List<string>>();

            foreach (var row in contentList)
            {
                List<string> processedRow = new List<string>();
                foreach (var cell in row)
                {
                    if (cell == null)
                    {
                        processedRow.Add(string.Empty);
                    }
                    else
                    {
                        processedRow.Add(cell.ToString());
                    }
                }
                result.Add(processedRow);
            }

            return result;
        }

    }
}
