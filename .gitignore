## Visual Studio临时文件
.vs/
.vscode/
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

## 构建结果
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

## Visual Studio 2015/2017缓存/选项目录
.vs/

## NuGet包
packages/*
*.nupkg
# 如果您想要检入NuGet包，可以取消下面这行的注释
#!packages/repositories.config
# NuGet v3的项目.json文件会生成更多的忽略项
*.nuget.props
*.nuget.targets

## VSTO特定文件
*.vsto
*.vstox
*.vsix
*.pfx
*.pfx.bak
*.publishsettings

## Office临时文件
~$*

## 用户特定文件
*.rsuser

## 缓存文件
*.[Cc]ache
*.csproj.AssemblyReference.cache
*.csproj.ResolveComReference.cache
DesignTimeResolveAssemblyReferencesInput.cache
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

## 配置文件
*.dll.config

## 备份和日志文件
*.bak
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

## 其他
.DS_Store
Thumbs.db


## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.
##
## Get latest from https://github.com/github/gitignore/blob/main/VisualStudio.gitignore

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates
*.env

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono auto generated files
mono_crash.*

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
[Aa][Rr][Mm]64[Ee][Cc]/
bld/
[Oo]bj/
[Oo]ut/
[Ll]og/
[Ll]ogs/

# Build results on 'Bin' directories
**/[Bb]in/*
# Uncomment if you have tasks that rely on *.refresh files to move binaries
# (https://github.com/github/gitignore/pull/3736)
#!**/[Bb]in/*.refresh

# Visual Studio 2015/2017 cache/options directory
.vs/
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Visual Studio 2017 auto generated files
Generated\ Files/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.trx

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Approval Tests result files
*.received.*

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.idb
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
# but not Directory.Build.rsp, as it configures directory-level build defaults
!Directory.Build.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Chutzpah Test files
_Chutzpah*

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity is a build add-in
_TeamCity*

# DotCover is a Code Coverage Tool
*.dotCover

# AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

# Coverlet is a free, cross platform Code Coverage Tool
coverage*.json
coverage*.xml
coverage*.info

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# Including strong name files can present a security risk
# (https://github.com/github/gitignore/pull/2483#issue-259490424)
#*.snk

# Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
ServiceFabricBackup/
*.rptproj.bak

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
FakesAssemblies/

# GhostDoc plugin setting file
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# Visual Studio 6 build log
*.plg

# Visual Studio 6 workspace options file
*.opt

# Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

# Visual Studio 6 auto-generated project file (contains which files were open etc.)
*.vbp

# Visual Studio 6 workspace and project file (working project files containing files to include in project)
*.dsw
*.dsp

# Visual Studio 6 technical files
*.ncb
*.aps

# Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket dependency manager
**/.paket/paket.exe
paket-files/

# FAKE - F# Make
**/.fake/

# CodeRush personal settings
**/.cr/personal

# Python Tools for Visual Studio (PTVS)
**/__pycache__/
*.pyc

# Cake - Uncomment if you are using it
#tools/**
#!tools/packages.config

# Tabs Studio
*.tss

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# OpenCover UI analysis results
OpenCover/

# Azure Stream Analytics local run output
ASALocalRun/

# MSBuild Binary and Structured Log
*.binlog
MSBuild_Logs/

# AWS SAM Build and Temporary Artifacts folder
.aws-sam

# NVidia Nsight GPU debugger configuration file
*.nvuser

# MFractors (Xamarin productivity tool) working folder
**/.mfractor/

# Local History for Visual Studio
**/.localhistory/

# Visual Studio History (VSHistory) files
.vshistory/

# BeatPulse healthcheck temp database
healthchecksdb

# Backup folder for Package Reference Convert tool in Visual Studio 2017
MigrationBackup/

# Ionide (cross platform F# VS Code tools) working folder
**/.ionide/

# Fody - auto-generated XML schema
FodyWeavers.xsd

# VS Code files for those working on multiple tools
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# Windows Installer files from build outputs
*.cab
*.msi
*.msix
*.msm
*.msp


# Visual Studio生成的文件
bin/
obj/
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# 用户特定文件
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# 用户特定文件 (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono自动生成的文件
mono_crash.*

# 构建结果
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017缓存/选项目录
.vs/
# 取消注释如果你有使用它的任务
#.vscode/

# MSTest测试结果
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# 构建结果的.NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# StyleCop
StyleCopReport.xml

# 由Microsoft Fakes生成的文件
FakesAssemblies/

# GhostDoc插件设置文件
*.GhostDoc.xml

# Node.js工具 for Visual Studio
.ntvs_analysis.dat
node_modules/

# Visual Studio 6构建日志
*.plg

# Visual Studio 6工作区选项文件
*.opt

# Visual Studio 6自动生成的工作区文件 (包含哪些文件打开等信息)
*.vbw

# Visual Studio LightSwitch构建输出
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket依赖管理器
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# CodeRush个人设置
.cr/personal

# Python工具 for Visual Studio (PTVS)
__pycache__/
*.pyc

# Cake - 取消注释如果你正在使用它
# tools/**
# !tools/packages.config

# Tabs Studio
*.tss

# Telerik的JustMock配置文件
*.jmconfig

# BizTalk构建输出
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# OpenCover UI分析结果
OpenCover/

# Azure Stream Analytics本地运行输出
ASALocalRun/

# MSBuild二进制和结构化日志
*.binlog

# NVidia Nsight GPU调试器配置文件
*.nvuser

# MFractors (Xamarin productivity tool) 工作文件夹
.mfractor/

# 本地历史 for Visual Studio
.localhistory/

# BeatPulse健康检查临时数据库
healthchecksdb

# 备份文件夹 for Package Reference Convert tool in Visual Studio 2017
MigrationBackup/

# Ionide (cross platform F# VS Code tools) 工作文件夹
.ionide/

# Fody - 自动生成的XML模式
FodyWeavers.xsd

# VSTO特定文件
# Office应用程序缓存
*.cache

# VSTO发布文件夹
publish/

# ClickOnce发布目录
PublishProfiles/

# Office临时文件
~$*
*.tmp

# Excel临时文件
~$*.xls*

# Word临时文件
~$*.doc*

# PowerPoint临时文件
~$*.ppt*

# Visio自动恢复文件
*.~vsd*

# Office文档的临时文件
*.xlk

# NuGet包
*.nupkg
# NuGet符号包
*.snupkg
# NuGet包文件夹可以被忽略，因为包恢复会重新创建它们
**/[Pp]ackages/*
# 除了build/，它被用作MSBuild目标
!**/[Pp]ackages/build/
# 取消注释如果需要，但通常它会被自动重新生成
#!**/[Pp]ackages/repositories.config
# NuGet v3的project.json文件产生更多可忽略的文件
*.nuget.props
*.nuget.targets

# Microsoft Azure构建输出
csx/
*.build.csdef

# Microsoft Azure模拟器
ecf/
rcf/

# Windows Store应用包目录和文件
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

# Visual Studio缓存文件
# 以"2E" + 10个随机字符开头的文件名
[0-9A-Fa-f]{32}.tmp

# JetBrains Rider
.idea/
*.sln.iml

# CodeRush
.cr/

# Python工具 for Visual Studio (PTVS)
*.pyproj

# Installshield输出文件夹
[Ee]xpress/

# DocProject是一个文档生成器加载项
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once目录
publish/

# 发布Web输出
*.[Pp]ublish.xml
*.azurePubxml
# 注意：注释下一行如果你想要检入你的web部署设置，
# 但数据库连接字符串（和可能的其他设置）将不被加密
*.pubxml
*.publishproj

# Microsoft Azure Web App发布设置。注释下一行如果你想要
# 检入你的Azure Web App发布设置，但敏感信息包含在这些脚本中
# 将被加密
PublishScripts/

# Windows Azure构建输出
csx/
*.build.csdef

# Windows Store应用包
AppPackages/

# 其他
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# RIA/Silverlight项目
Generated_Code/

# 备份和报告文件从转换旧项目文件
# 到较新的Visual Studio版本。备份文件不需要，
# 因为我们有git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm
ServiceFabricBackup/
*.rptproj.bak

# SQL Server文件
*.mdf
*.ldf
*.ndf

# 业务智能项目
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser
*- [Bb]ackup.rdl
*- [Bb]ackup ([0-9]).rdl
*- [Bb]ackup ([0-9][0-9]).rdl

# Microsoft Fakes
FakesAssemblies/

# GhostDoc插件设置文件
*.GhostDoc.xml

# Node.js工具 for Visual Studio
.ntvs_analysis.dat

# Typescript v1声明文件
typings/

# Visual Studio 6构建日志
*.plg

# Visual Studio 6工作区选项文件
*.opt

# Visual Studio 6自动生成的工作区文件 (包含哪些文件打开等信息)
*.vbw

# Visual Studio LightSwitch构建输出
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# LightSwitch生成的文件
GeneratedArtifacts/
ModelManifest.xml
_Pvt_Extensions/

# Paket依赖管理器
.paket/paket.exe

# FAKE - F# Make
.fake/

# JetBrains Rider
.idea/
*.sln.iml

# CodeRush
.cr/

# Python工具 for Visual Studio (PTVS)
__pycache__/
*.pyc

# VSTO部署清单
*.vsto
*.application

# Office开发者工具生成的文件
*.manifest

# 安装程序项目输出
[Dd]ebug/
[Rr]elease/