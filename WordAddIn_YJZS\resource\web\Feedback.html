<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .container {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .logo {
            width: 80px;
            margin: 30px auto 0 auto;
        }

        .options {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .option {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            width: 80px;
            text-align: center;
        }

            .option:hover {
                transform: translateY(-5px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            }

            .option img {
                width: 50px;
                margin-bottom: 15px;
            }

            .option p {
                font-size: 14px;
                color: #333;
                margin: 0;
            }

        .search-bar {
            padding: 20px;
            border-top: 1px solid rgba(238, 238, 238, 0.5);
            background: rgba(249, 249, 249, 0.5);
        }

            .search-bar input[type="text"] {
                padding: 12px 20px;
                width: calc(100% - 40px);
                border: none;
                border-radius: 25px;
                background: rgba(255, 255, 255, 0.9);
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                font-size: 14px;
                transition: all 0.3s ease;
            }

                .search-bar input[type="text"]:focus {
                    outline: none;
                    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
                }

        .results {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            text-align: left;
        }

            .results p {
                background: rgba(255, 255, 255, 0.9);
                padding: 15px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 10px;
                word-wrap: break-word;
            }

        .question {
            text-align: right;
            font-weight: 500;
            background: rgba(230, 247, 255, 0.9) !important;
        }

        /* 滚动条样式 */
        .results::-webkit-scrollbar {
            width: 8px;
        }

        .results::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .results::-webkit-scrollbar-thumb {
            background: rgba(74, 144, 226, 0.5);
            border-radius: 4px;
        }

            .results::-webkit-scrollbar-thumb:hover {
                background: rgba(74, 144, 226, 0.7);
            }
    </style>
</head>
<body>

    <div class="container">
        <div class="results" id="results">
            <!-- 搜索结果将显示在这里 -->
        </div>

        <img src="your-logo-url-here.png" alt="Logo" class="logo" id="logo">

        <div class="options" id="options">
            <div class="option">
                <img src="your-icon-url-here.png" alt="图标1">
                <p>反馈问题1</p>
            </div>
            <div class="option">
                <img src="your-icon-url-here.png" alt="图标2">
                <p>反馈问题2</p>
            </div>
            <div class="option">
                <img src="your-icon-url-here.png" alt="图标3">
                <p>反馈问题3</p>
            </div>
            <div class="option">
                <img src="your-icon-url-here.png" alt="图标4">
                <p>反馈问题4</p>
            </div>
        </div>

        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="搜索...">
        </div>

    </div>

    <script>
        // 脚本部分保持不变
        document.getElementById('searchInput').addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const query = this.value.trim();
                if (query) {
                    displayResults(query);
                }
            }
        });

        function displayResults(query) {
            document.getElementById('searchInput').value = '';
            document.getElementById('logo').style.display = 'none';
            document.getElementById('options').style.display = 'none';

            const resultsContainer = document.getElementById('results');

            const userQuestion = document.createElement('p');
            userQuestion.className = 'question';
            userQuestion.textContent = query;
            resultsContainer.appendChild(userQuestion);

            const answer = generateAnswer(query);
            const botAnswer = document.createElement('p');
            botAnswer.textContent = answer;
            resultsContainer.appendChild(botAnswer);
        }

        function generateAnswer(query) {
            return `你搜索了: "${query}". 这是一个示例回答。`;
        }
    </script>

</body>
</html>